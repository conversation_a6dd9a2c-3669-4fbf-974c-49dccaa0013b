# Security Policy

If you have discovered a security vulnerability in this project, please report it
privately. **Do not disclose it as a public issue.** This gives us time to work with you
to fix the issue before public exposure, reducing the chance that the exploit will be
used before a patch is released.

Please submit the report by filling out
[this form](https://github.com/open-source-parsers/jsoncpp/security/advisories/new).

Please provide the following information in your report:

- A description of the vulnerability and its impact
- How to reproduce the issue

This project is maintained by volunteers on a reasonable-effort basis. As such,
we ask that you give us 90 days to work on a fix before public exposure.
