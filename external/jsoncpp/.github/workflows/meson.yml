name: meson build and test
run-name: update pushed to ${{ github.ref }}
on: [check_run, push, pull_request]

jobs:
  meson-publish:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:     
      - name: checkout repository
        uses: actions/checkout@v4
        
      - name: setup python
        uses: actions/setup-python@v5
      
      - name: meson build
        uses: BSFishy/meson-build@v1.0.3
        with:
          meson-version: 1.5.1
          ninja-version: 1.11.1.1
          action: build
  
      - name: meson test
        uses: BSFishy/meson-build@v1.0.3
        with:
          meson-version: 1.5.1
          ninja-version: 1.11.1.1
          action: test

  meson-coverage:
    runs-on: ubuntu-latest
    
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
        
      - name: setup python
        uses: actions/setup-python@v5
      
      - name: meson build
        uses: BSFishy/meson-build@v1.0.3
        with:
          meson-version: 1.5.1
          ninja-version: 1.11.1.1
          setup-options: -Db_coverage=true
          action: build 
  
      - name: meson test
        uses: BSFishy/meson-build@v1.0.3
        with:
          meson-version: 1.5.1
          ninja-version: 1.11.1.1
          setup-options: -Db_coverage=true
          action: test

      - name: generate code coverage report
        uses: threeal/gcovr-action@v1.0.0
        with:
          coveralls-send: true
          github-token: ${{ secrets.GITHUB_TOKEN }}
