module(
    name = "jsoncpp",

    # Note: version must be updated in four places when doing a release. This
    # annoying process ensures that amalgam<PERSON>, C<PERSON><PERSON>, and meson all report the
    # correct version.
    # 1. /meson.build
    # 2. /include/json/version.h
    # 3. /CMakeLists.txt
    # 4. /MODULE.bazel
    # IMPORTANT: also update the SOVERSION!!
    version = "1.9.7",
    compatibility_level = 1,
)
