@PACKA<PERSON>_INIT@

set(hidapi_VERSION_MAJOR "@hidapi_VERSION_MAJOR@")
set(hidapi_VERSION_MINOR "@hidapi_VERSION_MINOR@")
set(hidapi_VERSION_PATCH "@hidapi_VERSION_PATCH@")
set(hidapi_VERSION "@hidapi_VERSION@")
set(hidapi_VERSION_STR "@hidapi_VERSION@@VERSION_SUFFIX@")

set(hidapi_FOUND FALSE)

set(HIDAPI_NEED_EXPORT_THREADS @HIDAPI_NEED_EXPORT_THREADS@)
set(HIDAPI_NEED_EXPORT_LIBUSB @HIDAPI_NEED_EXPORT_LIBUSB@)
set(HIDAPI_NEED_EXPORT_LIBUDEV @HIDAPI_NEED_EXPORT_LIBUDEV@)
set(HIDAPI_NEED_EXPORT_ICONV @HIDAPI_NEED_EXPORT_ICONV@)

if(HIDAPI_NEED_EXPORT_THREADS)
  if(CMAKE_VERSION VERSION_LESS 3.4.3)
    message(FATAL_ERROR "This file relies on consumers using CMake 3.4.3 or greater.")
  endif()
  find_package(Threads REQUIRED)
endif()

if(HIDAPI_NEED_EXPORT_LIBUSB OR HIDAPI_NEED_EXPORT_LIBUDEV)
  if(CMAKE_VERSION VERSION_LESS 3.6.3)
    message(FATAL_ERROR "This file relies on consumers using CMake 3.6.3 or greater.")
  endif()
  find_package(PkgConfig)
  if(HIDAPI_NEED_EXPORT_LIBUSB)
    pkg_check_modules(libusb REQUIRED IMPORTED_TARGET libusb-1.0>=1.0.9)
  endif()
  if(HIDAPI_NEED_EXPORT_LIBUDEV)
    pkg_check_modules(libudev REQUIRED IMPORTED_TARGET libudev)
  endif()
endif()

if(HIDAPI_NEED_EXPORT_ICONV)
  if(CMAKE_VERSION VERSION_LESS 3.11)
    message(WARNING "HIDAPI requires CMake target Iconv::Iconv, make sure to provide it")
  else()
    find_package(Iconv REQUIRED)
  endif()
endif()

include("${CMAKE_CURRENT_LIST_DIR}/libhidapi.cmake")

set(hidapi_FOUND TRUE)

foreach(_component @EXPORT_COMPONENTS@)
  if(TARGET hidapi::${_component})
    set(hidapi_${_component}_FOUND TRUE)
  endif()
endforeach()

check_required_components(hidapi)

if(NOT TARGET hidapi::hidapi)
  add_library(hidapi::hidapi INTERFACE IMPORTED)
  set_target_properties(hidapi::hidapi PROPERTIES
    INTERFACE_LINK_LIBRARIES hidapi::@EXPORT_ALIAS@
  )
endif()
