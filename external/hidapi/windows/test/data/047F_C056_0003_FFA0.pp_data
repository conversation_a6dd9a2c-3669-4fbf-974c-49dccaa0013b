# HIDAPI device info struct:
dev->vendor_id           = 0x047F
dev->product_id          = 0xC056
dev->manufacturer_string = "Plantronics"
dev->product_string      = "Plantronics Blackwire 3220 Series"
dev->release_number      = 0x0210
dev->interface_number    = 3
dev->usage               = 0x0003
dev->usage_page          = 0xFFA0
dev->path                = "\\?\hid#vid_047f&pid_c056&mi_03&col03#f&39e6f119&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}"

# Preparsed Data struct:
pp_data->MagicKey                             = 0x48696450204B4452
pp_data->Usage                                = 0x0003
pp_data->UsagePage                            = 0xFFA0
pp_data->Reserved                             = 0x00000000
# Input caps_info struct:
pp_data->caps_info[0]->FirstCap           = 0
pp_data->caps_info[0]->LastCap            = 8
pp_data->caps_info[0]->NumberOfCaps       = 8
pp_data->caps_info[0]->ReportByteLength   = 33
# Output caps_info struct:
pp_data->caps_info[1]->FirstCap           = 8
pp_data->caps_info[1]->LastCap            = 16
pp_data->caps_info[1]->NumberOfCaps       = 8
pp_data->caps_info[1]->ReportByteLength   = 33
# Feature caps_info struct:
pp_data->caps_info[2]->FirstCap           = 16
pp_data->caps_info[2]->LastCap            = 26
pp_data->caps_info[2]->NumberOfCaps       = 10
pp_data->caps_info[2]->ReportByteLength   = 3
# LinkCollectionArray Offset & Size:
pp_data->FirstByteOfLinkCollectionArray       = 0x0A90
pp_data->NumberLinkCollectionNodes            = 1
# Input hid_pp_cap struct:
pp_data->cap[0]->UsagePage                    = 0xFFA0
pp_data->cap[0]->ReportID                     = 0x03
pp_data->cap[0]->BitPosition                  = 0
pp_data->cap[0]->BitSize                      = 8
pp_data->cap[0]->ReportCount                  = 32
pp_data->cap[0]->BytePosition                 = 0x0001
pp_data->cap[0]->BitCount                     = 256
pp_data->cap[0]->BitField                     = 0x02
pp_data->cap[0]->NextBytePosition             = 0x0021
pp_data->cap[0]->LinkCollection               = 0x0000
pp_data->cap[0]->LinkUsagePage                = 0xFFA0
pp_data->cap[0]->LinkUsage                    = 0x0003
pp_data->cap[0]->IsMultipleItemsForArray      = 0
pp_data->cap[0]->IsButtonCap                  = 0
pp_data->cap[0]->IsPadding                    = 0
pp_data->cap[0]->IsAbsolute                   = 1
pp_data->cap[0]->IsRange                      = 0
pp_data->cap[0]->IsAlias                      = 0
pp_data->cap[0]->IsStringRange                = 0
pp_data->cap[0]->IsDesignatorRange            = 0
pp_data->cap[0]->Reserved1                    = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[0]->NotRange.Usage                        = 0x0030
pp_data->cap[0]->NotRange.Reserved1                    = 0x0030
pp_data->cap[0]->NotRange.StringIndex                  = 0
pp_data->cap[0]->NotRange.Reserved2                    = 0
pp_data->cap[0]->NotRange.DesignatorIndex              = 0
pp_data->cap[0]->NotRange.Reserved3                    = 0
pp_data->cap[0]->NotRange.DataIndex                    = 0
pp_data->cap[0]->NotRange.Reserved4                    = 0
pp_data->cap[0]->NotButton.HasNull                   = 0
pp_data->cap[0]->NotButton.Reserved4                 = 0x000000
pp_data->cap[0]->NotButton.LogicalMin                = 0
pp_data->cap[0]->NotButton.LogicalMax                = 1
pp_data->cap[0]->NotButton.PhysicalMin               = 0
pp_data->cap[0]->NotButton.PhysicalMax               = 0
pp_data->cap[0]->Units                    = 0
pp_data->cap[0]->UnitsExp                 = 0

pp_data->cap[1]->UsagePage                    = 0xFFA0
pp_data->cap[1]->ReportID                     = 0x14
pp_data->cap[1]->BitPosition                  = 4
pp_data->cap[1]->BitSize                      = 1
pp_data->cap[1]->ReportCount                  = 1
pp_data->cap[1]->BytePosition                 = 0x0001
pp_data->cap[1]->BitCount                     = 1
pp_data->cap[1]->BitField                     = 0x06
pp_data->cap[1]->NextBytePosition             = 0x0002
pp_data->cap[1]->LinkCollection               = 0x0000
pp_data->cap[1]->LinkUsagePage                = 0xFFA0
pp_data->cap[1]->LinkUsage                    = 0x0003
pp_data->cap[1]->IsMultipleItemsForArray      = 0
pp_data->cap[1]->IsButtonCap                  = 1
pp_data->cap[1]->IsPadding                    = 0
pp_data->cap[1]->IsAbsolute                   = 0
pp_data->cap[1]->IsRange                      = 0
pp_data->cap[1]->IsAlias                      = 0
pp_data->cap[1]->IsStringRange                = 0
pp_data->cap[1]->IsDesignatorRange            = 0
pp_data->cap[1]->Reserved1                    = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[1]->NotRange.Usage                        = 0x00B3
pp_data->cap[1]->NotRange.Reserved1                    = 0x00B3
pp_data->cap[1]->NotRange.StringIndex                  = 0
pp_data->cap[1]->NotRange.Reserved2                    = 0
pp_data->cap[1]->NotRange.DesignatorIndex              = 0
pp_data->cap[1]->NotRange.Reserved3                    = 0
pp_data->cap[1]->NotRange.DataIndex                    = 1
pp_data->cap[1]->NotRange.Reserved4                    = 1
pp_data->cap[1]->Button.LogicalMin                   = 0
pp_data->cap[1]->Button.LogicalMax                   = 0
pp_data->cap[1]->Units                    = 0
pp_data->cap[1]->UnitsExp                 = 0

pp_data->cap[2]->UsagePage                    = 0xFFA0
pp_data->cap[2]->ReportID                     = 0x14
pp_data->cap[2]->BitPosition                  = 3
pp_data->cap[2]->BitSize                      = 1
pp_data->cap[2]->ReportCount                  = 1
pp_data->cap[2]->BytePosition                 = 0x0001
pp_data->cap[2]->BitCount                     = 1
pp_data->cap[2]->BitField                     = 0x06
pp_data->cap[2]->NextBytePosition             = 0x0002
pp_data->cap[2]->LinkCollection               = 0x0000
pp_data->cap[2]->LinkUsagePage                = 0xFFA0
pp_data->cap[2]->LinkUsage                    = 0x0003
pp_data->cap[2]->IsMultipleItemsForArray      = 0
pp_data->cap[2]->IsButtonCap                  = 1
pp_data->cap[2]->IsPadding                    = 0
pp_data->cap[2]->IsAbsolute                   = 0
pp_data->cap[2]->IsRange                      = 0
pp_data->cap[2]->IsAlias                      = 0
pp_data->cap[2]->IsStringRange                = 0
pp_data->cap[2]->IsDesignatorRange            = 0
pp_data->cap[2]->Reserved1                    = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[2]->NotRange.Usage                        = 0x00B7
pp_data->cap[2]->NotRange.Reserved1                    = 0x00B7
pp_data->cap[2]->NotRange.StringIndex                  = 0
pp_data->cap[2]->NotRange.Reserved2                    = 0
pp_data->cap[2]->NotRange.DesignatorIndex              = 0
pp_data->cap[2]->NotRange.Reserved3                    = 0
pp_data->cap[2]->NotRange.DataIndex                    = 2
pp_data->cap[2]->NotRange.Reserved4                    = 2
pp_data->cap[2]->Button.LogicalMin                   = 0
pp_data->cap[2]->Button.LogicalMax                   = 0
pp_data->cap[2]->Units                    = 0
pp_data->cap[2]->UnitsExp                 = 0

pp_data->cap[3]->UsagePage                    = 0xFFA0
pp_data->cap[3]->ReportID                     = 0x14
pp_data->cap[3]->BitPosition                  = 2
pp_data->cap[3]->BitSize                      = 1
pp_data->cap[3]->ReportCount                  = 1
pp_data->cap[3]->BytePosition                 = 0x0001
pp_data->cap[3]->BitCount                     = 1
pp_data->cap[3]->BitField                     = 0x06
pp_data->cap[3]->NextBytePosition             = 0x0002
pp_data->cap[3]->LinkCollection               = 0x0000
pp_data->cap[3]->LinkUsagePage                = 0xFFA0
pp_data->cap[3]->LinkUsage                    = 0x0003
pp_data->cap[3]->IsMultipleItemsForArray      = 0
pp_data->cap[3]->IsButtonCap                  = 1
pp_data->cap[3]->IsPadding                    = 0
pp_data->cap[3]->IsAbsolute                   = 0
pp_data->cap[3]->IsRange                      = 0
pp_data->cap[3]->IsAlias                      = 0
pp_data->cap[3]->IsStringRange                = 0
pp_data->cap[3]->IsDesignatorRange            = 0
pp_data->cap[3]->Reserved1                    = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[3]->NotRange.Usage                        = 0x00B5
pp_data->cap[3]->NotRange.Reserved1                    = 0x00B5
pp_data->cap[3]->NotRange.StringIndex                  = 0
pp_data->cap[3]->NotRange.Reserved2                    = 0
pp_data->cap[3]->NotRange.DesignatorIndex              = 0
pp_data->cap[3]->NotRange.Reserved3                    = 0
pp_data->cap[3]->NotRange.DataIndex                    = 3
pp_data->cap[3]->NotRange.Reserved4                    = 3
pp_data->cap[3]->Button.LogicalMin                   = 0
pp_data->cap[3]->Button.LogicalMax                   = 0
pp_data->cap[3]->Units                    = 0
pp_data->cap[3]->UnitsExp                 = 0

pp_data->cap[4]->UsagePage                    = 0xFFA0
pp_data->cap[4]->ReportID                     = 0x14
pp_data->cap[4]->BitPosition                  = 1
pp_data->cap[4]->BitSize                      = 1
pp_data->cap[4]->ReportCount                  = 1
pp_data->cap[4]->BytePosition                 = 0x0001
pp_data->cap[4]->BitCount                     = 1
pp_data->cap[4]->BitField                     = 0x06
pp_data->cap[4]->NextBytePosition             = 0x0002
pp_data->cap[4]->LinkCollection               = 0x0000
pp_data->cap[4]->LinkUsagePage                = 0xFFA0
pp_data->cap[4]->LinkUsage                    = 0x0003
pp_data->cap[4]->IsMultipleItemsForArray      = 0
pp_data->cap[4]->IsButtonCap                  = 1
pp_data->cap[4]->IsPadding                    = 0
pp_data->cap[4]->IsAbsolute                   = 0
pp_data->cap[4]->IsRange                      = 0
pp_data->cap[4]->IsAlias                      = 0
pp_data->cap[4]->IsStringRange                = 0
pp_data->cap[4]->IsDesignatorRange            = 0
pp_data->cap[4]->Reserved1                    = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[4]->NotRange.Usage                        = 0x00B2
pp_data->cap[4]->NotRange.Reserved1                    = 0x00B2
pp_data->cap[4]->NotRange.StringIndex                  = 0
pp_data->cap[4]->NotRange.Reserved2                    = 0
pp_data->cap[4]->NotRange.DesignatorIndex              = 0
pp_data->cap[4]->NotRange.Reserved3                    = 0
pp_data->cap[4]->NotRange.DataIndex                    = 4
pp_data->cap[4]->NotRange.Reserved4                    = 4
pp_data->cap[4]->Button.LogicalMin                   = 0
pp_data->cap[4]->Button.LogicalMax                   = 0
pp_data->cap[4]->Units                    = 0
pp_data->cap[4]->UnitsExp                 = 0

pp_data->cap[5]->UsagePage                    = 0xFFA0
pp_data->cap[5]->ReportID                     = 0x14
pp_data->cap[5]->BitPosition                  = 0
pp_data->cap[5]->BitSize                      = 1
pp_data->cap[5]->ReportCount                  = 1
pp_data->cap[5]->BytePosition                 = 0x0001
pp_data->cap[5]->BitCount                     = 1
pp_data->cap[5]->BitField                     = 0x06
pp_data->cap[5]->NextBytePosition             = 0x0002
pp_data->cap[5]->LinkCollection               = 0x0000
pp_data->cap[5]->LinkUsagePage                = 0xFFA0
pp_data->cap[5]->LinkUsage                    = 0x0003
pp_data->cap[5]->IsMultipleItemsForArray      = 0
pp_data->cap[5]->IsButtonCap                  = 1
pp_data->cap[5]->IsPadding                    = 0
pp_data->cap[5]->IsAbsolute                   = 0
pp_data->cap[5]->IsRange                      = 0
pp_data->cap[5]->IsAlias                      = 0
pp_data->cap[5]->IsStringRange                = 0
pp_data->cap[5]->IsDesignatorRange            = 0
pp_data->cap[5]->Reserved1                    = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[5]->NotRange.Usage                        = 0x00B1
pp_data->cap[5]->NotRange.Reserved1                    = 0x00B1
pp_data->cap[5]->NotRange.StringIndex                  = 0
pp_data->cap[5]->NotRange.Reserved2                    = 0
pp_data->cap[5]->NotRange.DesignatorIndex              = 0
pp_data->cap[5]->NotRange.Reserved3                    = 0
pp_data->cap[5]->NotRange.DataIndex                    = 5
pp_data->cap[5]->NotRange.Reserved4                    = 5
pp_data->cap[5]->Button.LogicalMin                   = 0
pp_data->cap[5]->Button.LogicalMax                   = 0
pp_data->cap[5]->Units                    = 0
pp_data->cap[5]->UnitsExp                 = 0

pp_data->cap[6]->UsagePage                    = 0xFFA0
pp_data->cap[6]->ReportID                     = 0x15
pp_data->cap[6]->BitPosition                  = 0
pp_data->cap[6]->BitSize                      = 16
pp_data->cap[6]->ReportCount                  = 1
pp_data->cap[6]->BytePosition                 = 0x0001
pp_data->cap[6]->BitCount                     = 16
pp_data->cap[6]->BitField                     = 0x22
pp_data->cap[6]->NextBytePosition             = 0x0003
pp_data->cap[6]->LinkCollection               = 0x0000
pp_data->cap[6]->LinkUsagePage                = 0xFFA0
pp_data->cap[6]->LinkUsage                    = 0x0003
pp_data->cap[6]->IsMultipleItemsForArray      = 0
pp_data->cap[6]->IsButtonCap                  = 0
pp_data->cap[6]->IsPadding                    = 0
pp_data->cap[6]->IsAbsolute                   = 1
pp_data->cap[6]->IsRange                      = 0
pp_data->cap[6]->IsAlias                      = 0
pp_data->cap[6]->IsStringRange                = 0
pp_data->cap[6]->IsDesignatorRange            = 0
pp_data->cap[6]->Reserved1                    = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[6]->NotRange.Usage                        = 0x008C
pp_data->cap[6]->NotRange.Reserved1                    = 0x008C
pp_data->cap[6]->NotRange.StringIndex                  = 0
pp_data->cap[6]->NotRange.Reserved2                    = 0
pp_data->cap[6]->NotRange.DesignatorIndex              = 0
pp_data->cap[6]->NotRange.Reserved3                    = 0
pp_data->cap[6]->NotRange.DataIndex                    = 6
pp_data->cap[6]->NotRange.Reserved4                    = 6
pp_data->cap[6]->NotButton.HasNull                   = 0
pp_data->cap[6]->NotButton.Reserved4                 = 0x000000
pp_data->cap[6]->NotButton.LogicalMin                = 0
pp_data->cap[6]->NotButton.LogicalMax                = 65535
pp_data->cap[6]->NotButton.PhysicalMin               = 0
pp_data->cap[6]->NotButton.PhysicalMax               = 0
pp_data->cap[6]->Units                    = 0
pp_data->cap[6]->UnitsExp                 = 0

pp_data->cap[7]->UsagePage                    = 0xFFA0
pp_data->cap[7]->ReportID                     = 0x1F
pp_data->cap[7]->BitPosition                  = 0
pp_data->cap[7]->BitSize                      = 1
pp_data->cap[7]->ReportCount                  = 1
pp_data->cap[7]->BytePosition                 = 0x0001
pp_data->cap[7]->BitCount                     = 1
pp_data->cap[7]->BitField                     = 0x06
pp_data->cap[7]->NextBytePosition             = 0x0002
pp_data->cap[7]->LinkCollection               = 0x0000
pp_data->cap[7]->LinkUsagePage                = 0xFFA0
pp_data->cap[7]->LinkUsage                    = 0x0003
pp_data->cap[7]->IsMultipleItemsForArray      = 0
pp_data->cap[7]->IsButtonCap                  = 1
pp_data->cap[7]->IsPadding                    = 0
pp_data->cap[7]->IsAbsolute                   = 0
pp_data->cap[7]->IsRange                      = 0
pp_data->cap[7]->IsAlias                      = 0
pp_data->cap[7]->IsStringRange                = 0
pp_data->cap[7]->IsDesignatorRange            = 0
pp_data->cap[7]->Reserved1                    = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[7]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[7]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[7]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[7]->NotRange.Usage                        = 0x009C
pp_data->cap[7]->NotRange.Reserved1                    = 0x009C
pp_data->cap[7]->NotRange.StringIndex                  = 0
pp_data->cap[7]->NotRange.Reserved2                    = 0
pp_data->cap[7]->NotRange.DesignatorIndex              = 0
pp_data->cap[7]->NotRange.Reserved3                    = 0
pp_data->cap[7]->NotRange.DataIndex                    = 7
pp_data->cap[7]->NotRange.Reserved4                    = 7
pp_data->cap[7]->Button.LogicalMin                   = 0
pp_data->cap[7]->Button.LogicalMax                   = 0
pp_data->cap[7]->Units                    = 0
pp_data->cap[7]->UnitsExp                 = 0

# Output hid_pp_cap struct:
pp_data->cap[8]->UsagePage                    = 0xFFA0
pp_data->cap[8]->ReportID                     = 0x03
pp_data->cap[8]->BitPosition                  = 0
pp_data->cap[8]->BitSize                      = 8
pp_data->cap[8]->ReportCount                  = 32
pp_data->cap[8]->BytePosition                 = 0x0001
pp_data->cap[8]->BitCount                     = 256
pp_data->cap[8]->BitField                     = 0x02
pp_data->cap[8]->NextBytePosition             = 0x0021
pp_data->cap[8]->LinkCollection               = 0x0000
pp_data->cap[8]->LinkUsagePage                = 0xFFA0
pp_data->cap[8]->LinkUsage                    = 0x0003
pp_data->cap[8]->IsMultipleItemsForArray      = 0
pp_data->cap[8]->IsButtonCap                  = 0
pp_data->cap[8]->IsPadding                    = 0
pp_data->cap[8]->IsAbsolute                   = 1
pp_data->cap[8]->IsRange                      = 0
pp_data->cap[8]->IsAlias                      = 0
pp_data->cap[8]->IsStringRange                = 0
pp_data->cap[8]->IsDesignatorRange            = 0
pp_data->cap[8]->Reserved1                    = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[8]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[8]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[8]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[8]->NotRange.Usage                        = 0x0030
pp_data->cap[8]->NotRange.Reserved1                    = 0x0030
pp_data->cap[8]->NotRange.StringIndex                  = 0
pp_data->cap[8]->NotRange.Reserved2                    = 0
pp_data->cap[8]->NotRange.DesignatorIndex              = 0
pp_data->cap[8]->NotRange.Reserved3                    = 0
pp_data->cap[8]->NotRange.DataIndex                    = 0
pp_data->cap[8]->NotRange.Reserved4                    = 0
pp_data->cap[8]->NotButton.HasNull                   = 0
pp_data->cap[8]->NotButton.Reserved4                 = 0x000000
pp_data->cap[8]->NotButton.LogicalMin                = 0
pp_data->cap[8]->NotButton.LogicalMax                = 1
pp_data->cap[8]->NotButton.PhysicalMin               = 0
pp_data->cap[8]->NotButton.PhysicalMax               = 0
pp_data->cap[8]->Units                    = 0
pp_data->cap[8]->UnitsExp                 = 0

pp_data->cap[9]->UsagePage                    = 0xFFA0
pp_data->cap[9]->ReportID                     = 0x19
pp_data->cap[9]->BitPosition                  = 3
pp_data->cap[9]->BitSize                      = 1
pp_data->cap[9]->ReportCount                  = 1
pp_data->cap[9]->BytePosition                 = 0x0001
pp_data->cap[9]->BitCount                     = 1
pp_data->cap[9]->BitField                     = 0x22
pp_data->cap[9]->NextBytePosition             = 0x0002
pp_data->cap[9]->LinkCollection               = 0x0000
pp_data->cap[9]->LinkUsagePage                = 0xFFA0
pp_data->cap[9]->LinkUsage                    = 0x0003
pp_data->cap[9]->IsMultipleItemsForArray      = 0
pp_data->cap[9]->IsButtonCap                  = 1
pp_data->cap[9]->IsPadding                    = 0
pp_data->cap[9]->IsAbsolute                   = 1
pp_data->cap[9]->IsRange                      = 0
pp_data->cap[9]->IsAlias                      = 0
pp_data->cap[9]->IsStringRange                = 0
pp_data->cap[9]->IsDesignatorRange            = 0
pp_data->cap[9]->Reserved1                    = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[9]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[9]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[9]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[9]->NotRange.Usage                        = 0x00DC
pp_data->cap[9]->NotRange.Reserved1                    = 0x00DC
pp_data->cap[9]->NotRange.StringIndex                  = 0
pp_data->cap[9]->NotRange.Reserved2                    = 0
pp_data->cap[9]->NotRange.DesignatorIndex              = 0
pp_data->cap[9]->NotRange.Reserved3                    = 0
pp_data->cap[9]->NotRange.DataIndex                    = 1
pp_data->cap[9]->NotRange.Reserved4                    = 1
pp_data->cap[9]->Button.LogicalMin                   = 0
pp_data->cap[9]->Button.LogicalMax                   = 0
pp_data->cap[9]->Units                    = 0
pp_data->cap[9]->UnitsExp                 = 0

pp_data->cap[10]->UsagePage                    = 0xFFA0
pp_data->cap[10]->ReportID                     = 0x19
pp_data->cap[10]->BitPosition                  = 2
pp_data->cap[10]->BitSize                      = 1
pp_data->cap[10]->ReportCount                  = 1
pp_data->cap[10]->BytePosition                 = 0x0001
pp_data->cap[10]->BitCount                     = 1
pp_data->cap[10]->BitField                     = 0x22
pp_data->cap[10]->NextBytePosition             = 0x0002
pp_data->cap[10]->LinkCollection               = 0x0000
pp_data->cap[10]->LinkUsagePage                = 0xFFA0
pp_data->cap[10]->LinkUsage                    = 0x0003
pp_data->cap[10]->IsMultipleItemsForArray      = 0
pp_data->cap[10]->IsButtonCap                  = 1
pp_data->cap[10]->IsPadding                    = 0
pp_data->cap[10]->IsAbsolute                   = 1
pp_data->cap[10]->IsRange                      = 0
pp_data->cap[10]->IsAlias                      = 0
pp_data->cap[10]->IsStringRange                = 0
pp_data->cap[10]->IsDesignatorRange            = 0
pp_data->cap[10]->Reserved1                    = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[10]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[10]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[10]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[10]->NotRange.Usage                        = 0x009E
pp_data->cap[10]->NotRange.Reserved1                    = 0x009E
pp_data->cap[10]->NotRange.StringIndex                  = 0
pp_data->cap[10]->NotRange.Reserved2                    = 0
pp_data->cap[10]->NotRange.DesignatorIndex              = 0
pp_data->cap[10]->NotRange.Reserved3                    = 0
pp_data->cap[10]->NotRange.DataIndex                    = 2
pp_data->cap[10]->NotRange.Reserved4                    = 2
pp_data->cap[10]->Button.LogicalMin                   = 0
pp_data->cap[10]->Button.LogicalMax                   = 0
pp_data->cap[10]->Units                    = 0
pp_data->cap[10]->UnitsExp                 = 0

pp_data->cap[11]->UsagePage                    = 0xFFA0
pp_data->cap[11]->ReportID                     = 0x19
pp_data->cap[11]->BitPosition                  = 1
pp_data->cap[11]->BitSize                      = 1
pp_data->cap[11]->ReportCount                  = 1
pp_data->cap[11]->BytePosition                 = 0x0001
pp_data->cap[11]->BitCount                     = 1
pp_data->cap[11]->BitField                     = 0x22
pp_data->cap[11]->NextBytePosition             = 0x0002
pp_data->cap[11]->LinkCollection               = 0x0000
pp_data->cap[11]->LinkUsagePage                = 0xFFA0
pp_data->cap[11]->LinkUsage                    = 0x0003
pp_data->cap[11]->IsMultipleItemsForArray      = 0
pp_data->cap[11]->IsButtonCap                  = 1
pp_data->cap[11]->IsPadding                    = 0
pp_data->cap[11]->IsAbsolute                   = 1
pp_data->cap[11]->IsRange                      = 0
pp_data->cap[11]->IsAlias                      = 0
pp_data->cap[11]->IsStringRange                = 0
pp_data->cap[11]->IsDesignatorRange            = 0
pp_data->cap[11]->Reserved1                    = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[11]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[11]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[11]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[11]->NotRange.Usage                        = 0x008F
pp_data->cap[11]->NotRange.Reserved1                    = 0x008F
pp_data->cap[11]->NotRange.StringIndex                  = 0
pp_data->cap[11]->NotRange.Reserved2                    = 0
pp_data->cap[11]->NotRange.DesignatorIndex              = 0
pp_data->cap[11]->NotRange.Reserved3                    = 0
pp_data->cap[11]->NotRange.DataIndex                    = 3
pp_data->cap[11]->NotRange.Reserved4                    = 3
pp_data->cap[11]->Button.LogicalMin                   = 0
pp_data->cap[11]->Button.LogicalMax                   = 0
pp_data->cap[11]->Units                    = 0
pp_data->cap[11]->UnitsExp                 = 0

pp_data->cap[12]->UsagePage                    = 0xFFA0
pp_data->cap[12]->ReportID                     = 0x19
pp_data->cap[12]->BitPosition                  = 0
pp_data->cap[12]->BitSize                      = 1
pp_data->cap[12]->ReportCount                  = 1
pp_data->cap[12]->BytePosition                 = 0x0001
pp_data->cap[12]->BitCount                     = 1
pp_data->cap[12]->BitField                     = 0x22
pp_data->cap[12]->NextBytePosition             = 0x0002
pp_data->cap[12]->LinkCollection               = 0x0000
pp_data->cap[12]->LinkUsagePage                = 0xFFA0
pp_data->cap[12]->LinkUsage                    = 0x0003
pp_data->cap[12]->IsMultipleItemsForArray      = 0
pp_data->cap[12]->IsButtonCap                  = 1
pp_data->cap[12]->IsPadding                    = 0
pp_data->cap[12]->IsAbsolute                   = 1
pp_data->cap[12]->IsRange                      = 0
pp_data->cap[12]->IsAlias                      = 0
pp_data->cap[12]->IsStringRange                = 0
pp_data->cap[12]->IsDesignatorRange            = 0
pp_data->cap[12]->Reserved1                    = 0x000000
pp_data->cap[12]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[12]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[12]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[12]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[12]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[12]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[12]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[12]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[12]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[12]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[12]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[12]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[12]->NotRange.Usage                        = 0x008D
pp_data->cap[12]->NotRange.Reserved1                    = 0x008D
pp_data->cap[12]->NotRange.StringIndex                  = 0
pp_data->cap[12]->NotRange.Reserved2                    = 0
pp_data->cap[12]->NotRange.DesignatorIndex              = 0
pp_data->cap[12]->NotRange.Reserved3                    = 0
pp_data->cap[12]->NotRange.DataIndex                    = 4
pp_data->cap[12]->NotRange.Reserved4                    = 4
pp_data->cap[12]->Button.LogicalMin                   = 0
pp_data->cap[12]->Button.LogicalMax                   = 0
pp_data->cap[12]->Units                    = 0
pp_data->cap[12]->UnitsExp                 = 0

pp_data->cap[13]->UsagePage                    = 0xFFA0
pp_data->cap[13]->ReportID                     = 0x19
pp_data->cap[13]->BitPosition                  = 5
pp_data->cap[13]->BitSize                      = 1
pp_data->cap[13]->ReportCount                  = 1
pp_data->cap[13]->BytePosition                 = 0x0001
pp_data->cap[13]->BitCount                     = 1
pp_data->cap[13]->BitField                     = 0x06
pp_data->cap[13]->NextBytePosition             = 0x0002
pp_data->cap[13]->LinkCollection               = 0x0000
pp_data->cap[13]->LinkUsagePage                = 0xFFA0
pp_data->cap[13]->LinkUsage                    = 0x0003
pp_data->cap[13]->IsMultipleItemsForArray      = 0
pp_data->cap[13]->IsButtonCap                  = 1
pp_data->cap[13]->IsPadding                    = 0
pp_data->cap[13]->IsAbsolute                   = 0
pp_data->cap[13]->IsRange                      = 0
pp_data->cap[13]->IsAlias                      = 0
pp_data->cap[13]->IsStringRange                = 0
pp_data->cap[13]->IsDesignatorRange            = 0
pp_data->cap[13]->Reserved1                    = 0x000000
pp_data->cap[13]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[13]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[13]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[13]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[13]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[13]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[13]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[13]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[13]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[13]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[13]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[13]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[13]->NotRange.Usage                        = 0x00D9
pp_data->cap[13]->NotRange.Reserved1                    = 0x00D9
pp_data->cap[13]->NotRange.StringIndex                  = 0
pp_data->cap[13]->NotRange.Reserved2                    = 0
pp_data->cap[13]->NotRange.DesignatorIndex              = 0
pp_data->cap[13]->NotRange.Reserved3                    = 0
pp_data->cap[13]->NotRange.DataIndex                    = 5
pp_data->cap[13]->NotRange.Reserved4                    = 5
pp_data->cap[13]->Button.LogicalMin                   = 0
pp_data->cap[13]->Button.LogicalMax                   = 0
pp_data->cap[13]->Units                    = 0
pp_data->cap[13]->UnitsExp                 = 0

pp_data->cap[14]->UsagePage                    = 0xFFA0
pp_data->cap[14]->ReportID                     = 0x19
pp_data->cap[14]->BitPosition                  = 4
pp_data->cap[14]->BitSize                      = 1
pp_data->cap[14]->ReportCount                  = 1
pp_data->cap[14]->BytePosition                 = 0x0001
pp_data->cap[14]->BitCount                     = 1
pp_data->cap[14]->BitField                     = 0x06
pp_data->cap[14]->NextBytePosition             = 0x0002
pp_data->cap[14]->LinkCollection               = 0x0000
pp_data->cap[14]->LinkUsagePage                = 0xFFA0
pp_data->cap[14]->LinkUsage                    = 0x0003
pp_data->cap[14]->IsMultipleItemsForArray      = 0
pp_data->cap[14]->IsButtonCap                  = 1
pp_data->cap[14]->IsPadding                    = 0
pp_data->cap[14]->IsAbsolute                   = 0
pp_data->cap[14]->IsRange                      = 0
pp_data->cap[14]->IsAlias                      = 0
pp_data->cap[14]->IsStringRange                = 0
pp_data->cap[14]->IsDesignatorRange            = 0
pp_data->cap[14]->Reserved1                    = 0x000000
pp_data->cap[14]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[14]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[14]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[14]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[14]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[14]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[14]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[14]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[14]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[14]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[14]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[14]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[14]->NotRange.Usage                        = 0x00D2
pp_data->cap[14]->NotRange.Reserved1                    = 0x00D2
pp_data->cap[14]->NotRange.StringIndex                  = 0
pp_data->cap[14]->NotRange.Reserved2                    = 0
pp_data->cap[14]->NotRange.DesignatorIndex              = 0
pp_data->cap[14]->NotRange.Reserved3                    = 0
pp_data->cap[14]->NotRange.DataIndex                    = 6
pp_data->cap[14]->NotRange.Reserved4                    = 6
pp_data->cap[14]->Button.LogicalMin                   = 0
pp_data->cap[14]->Button.LogicalMax                   = 0
pp_data->cap[14]->Units                    = 0
pp_data->cap[14]->UnitsExp                 = 0

pp_data->cap[15]->UsagePage                    = 0xFFA0
pp_data->cap[15]->ReportID                     = 0x1A
pp_data->cap[15]->BitPosition                  = 0
pp_data->cap[15]->BitSize                      = 1
pp_data->cap[15]->ReportCount                  = 1
pp_data->cap[15]->BytePosition                 = 0x0001
pp_data->cap[15]->BitCount                     = 1
pp_data->cap[15]->BitField                     = 0x22
pp_data->cap[15]->NextBytePosition             = 0x0002
pp_data->cap[15]->LinkCollection               = 0x0000
pp_data->cap[15]->LinkUsagePage                = 0xFFA0
pp_data->cap[15]->LinkUsage                    = 0x0003
pp_data->cap[15]->IsMultipleItemsForArray      = 0
pp_data->cap[15]->IsButtonCap                  = 1
pp_data->cap[15]->IsPadding                    = 0
pp_data->cap[15]->IsAbsolute                   = 1
pp_data->cap[15]->IsRange                      = 0
pp_data->cap[15]->IsAlias                      = 0
pp_data->cap[15]->IsStringRange                = 0
pp_data->cap[15]->IsDesignatorRange            = 0
pp_data->cap[15]->Reserved1                    = 0x000000
pp_data->cap[15]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[15]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[15]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[15]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[15]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[15]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[15]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[15]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[15]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[15]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[15]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[15]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[15]->NotRange.Usage                        = 0x00B5
pp_data->cap[15]->NotRange.Reserved1                    = 0x00B5
pp_data->cap[15]->NotRange.StringIndex                  = 0
pp_data->cap[15]->NotRange.Reserved2                    = 0
pp_data->cap[15]->NotRange.DesignatorIndex              = 0
pp_data->cap[15]->NotRange.Reserved3                    = 0
pp_data->cap[15]->NotRange.DataIndex                    = 7
pp_data->cap[15]->NotRange.Reserved4                    = 7
pp_data->cap[15]->Button.LogicalMin                   = 0
pp_data->cap[15]->Button.LogicalMax                   = 0
pp_data->cap[15]->Units                    = 0
pp_data->cap[15]->UnitsExp                 = 0

# Feature hid_pp_cap struct:
pp_data->cap[16]->UsagePage                    = 0xFFA0
pp_data->cap[16]->ReportID                     = 0x1B
pp_data->cap[16]->BitPosition                  = 1
pp_data->cap[16]->BitSize                      = 1
pp_data->cap[16]->ReportCount                  = 1
pp_data->cap[16]->BytePosition                 = 0x0001
pp_data->cap[16]->BitCount                     = 1
pp_data->cap[16]->BitField                     = 0x22
pp_data->cap[16]->NextBytePosition             = 0x0002
pp_data->cap[16]->LinkCollection               = 0x0000
pp_data->cap[16]->LinkUsagePage                = 0xFFA0
pp_data->cap[16]->LinkUsage                    = 0x0003
pp_data->cap[16]->IsMultipleItemsForArray      = 0
pp_data->cap[16]->IsButtonCap                  = 1
pp_data->cap[16]->IsPadding                    = 0
pp_data->cap[16]->IsAbsolute                   = 1
pp_data->cap[16]->IsRange                      = 0
pp_data->cap[16]->IsAlias                      = 0
pp_data->cap[16]->IsStringRange                = 0
pp_data->cap[16]->IsDesignatorRange            = 0
pp_data->cap[16]->Reserved1                    = 0x000000
pp_data->cap[16]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[16]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[16]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[16]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[16]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[16]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[16]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[16]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[16]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[16]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[16]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[16]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[16]->NotRange.Usage                        = 0x00B5
pp_data->cap[16]->NotRange.Reserved1                    = 0x00B5
pp_data->cap[16]->NotRange.StringIndex                  = 0
pp_data->cap[16]->NotRange.Reserved2                    = 0
pp_data->cap[16]->NotRange.DesignatorIndex              = 0
pp_data->cap[16]->NotRange.Reserved3                    = 0
pp_data->cap[16]->NotRange.DataIndex                    = 0
pp_data->cap[16]->NotRange.Reserved4                    = 0
pp_data->cap[16]->Button.LogicalMin                   = 0
pp_data->cap[16]->Button.LogicalMax                   = 0
pp_data->cap[16]->Units                    = 0
pp_data->cap[16]->UnitsExp                 = 0

pp_data->cap[17]->UsagePage                    = 0xFFA0
pp_data->cap[17]->ReportID                     = 0x1B
pp_data->cap[17]->BitPosition                  = 0
pp_data->cap[17]->BitSize                      = 1
pp_data->cap[17]->ReportCount                  = 1
pp_data->cap[17]->BytePosition                 = 0x0001
pp_data->cap[17]->BitCount                     = 1
pp_data->cap[17]->BitField                     = 0x22
pp_data->cap[17]->NextBytePosition             = 0x0002
pp_data->cap[17]->LinkCollection               = 0x0000
pp_data->cap[17]->LinkUsagePage                = 0xFFA0
pp_data->cap[17]->LinkUsage                    = 0x0003
pp_data->cap[17]->IsMultipleItemsForArray      = 0
pp_data->cap[17]->IsButtonCap                  = 1
pp_data->cap[17]->IsPadding                    = 0
pp_data->cap[17]->IsAbsolute                   = 1
pp_data->cap[17]->IsRange                      = 0
pp_data->cap[17]->IsAlias                      = 0
pp_data->cap[17]->IsStringRange                = 0
pp_data->cap[17]->IsDesignatorRange            = 0
pp_data->cap[17]->Reserved1                    = 0x000000
pp_data->cap[17]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[17]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[17]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[17]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[17]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[17]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[17]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[17]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[17]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[17]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[17]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[17]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[17]->NotRange.Usage                        = 0x00CF
pp_data->cap[17]->NotRange.Reserved1                    = 0x00CF
pp_data->cap[17]->NotRange.StringIndex                  = 0
pp_data->cap[17]->NotRange.Reserved2                    = 0
pp_data->cap[17]->NotRange.DesignatorIndex              = 0
pp_data->cap[17]->NotRange.Reserved3                    = 0
pp_data->cap[17]->NotRange.DataIndex                    = 1
pp_data->cap[17]->NotRange.Reserved4                    = 1
pp_data->cap[17]->Button.LogicalMin                   = 0
pp_data->cap[17]->Button.LogicalMax                   = 0
pp_data->cap[17]->Units                    = 0
pp_data->cap[17]->UnitsExp                 = 0

pp_data->cap[18]->UsagePage                    = 0xFFA0
pp_data->cap[18]->ReportID                     = 0x1B
pp_data->cap[18]->BitPosition                  = 2
pp_data->cap[18]->BitSize                      = 1
pp_data->cap[18]->ReportCount                  = 1
pp_data->cap[18]->BytePosition                 = 0x0001
pp_data->cap[18]->BitCount                     = 1
pp_data->cap[18]->BitField                     = 0x23
pp_data->cap[18]->NextBytePosition             = 0x0002
pp_data->cap[18]->LinkCollection               = 0x0000
pp_data->cap[18]->LinkUsagePage                = 0xFFA0
pp_data->cap[18]->LinkUsage                    = 0x0003
pp_data->cap[18]->IsMultipleItemsForArray      = 0
pp_data->cap[18]->IsButtonCap                  = 1
pp_data->cap[18]->IsPadding                    = 1
pp_data->cap[18]->IsAbsolute                   = 1
pp_data->cap[18]->IsRange                      = 0
pp_data->cap[18]->IsAlias                      = 0
pp_data->cap[18]->IsStringRange                = 0
pp_data->cap[18]->IsDesignatorRange            = 0
pp_data->cap[18]->Reserved1                    = 0x000000
pp_data->cap[18]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[18]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[18]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[18]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[18]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[18]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[18]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[18]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[18]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[18]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[18]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[18]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[18]->NotRange.Usage                        = 0x00DE
pp_data->cap[18]->NotRange.Reserved1                    = 0x00DE
pp_data->cap[18]->NotRange.StringIndex                  = 0
pp_data->cap[18]->NotRange.Reserved2                    = 0
pp_data->cap[18]->NotRange.DesignatorIndex              = 0
pp_data->cap[18]->NotRange.Reserved3                    = 0
pp_data->cap[18]->NotRange.DataIndex                    = 2
pp_data->cap[18]->NotRange.Reserved4                    = 2
pp_data->cap[18]->Button.LogicalMin                   = 0
pp_data->cap[18]->Button.LogicalMax                   = 0
pp_data->cap[18]->Units                    = 0
pp_data->cap[18]->UnitsExp                 = 0

pp_data->cap[19]->UsagePage                    = 0xFFA0
pp_data->cap[19]->ReportID                     = 0x1B
pp_data->cap[19]->BitPosition                  = 3
pp_data->cap[19]->BitSize                      = 1
pp_data->cap[19]->ReportCount                  = 1
pp_data->cap[19]->BytePosition                 = 0x0001
pp_data->cap[19]->BitCount                     = 1
pp_data->cap[19]->BitField                     = 0x22
pp_data->cap[19]->NextBytePosition             = 0x0002
pp_data->cap[19]->LinkCollection               = 0x0000
pp_data->cap[19]->LinkUsagePage                = 0xFFA0
pp_data->cap[19]->LinkUsage                    = 0x0003
pp_data->cap[19]->IsMultipleItemsForArray      = 0
pp_data->cap[19]->IsButtonCap                  = 1
pp_data->cap[19]->IsPadding                    = 0
pp_data->cap[19]->IsAbsolute                   = 1
pp_data->cap[19]->IsRange                      = 0
pp_data->cap[19]->IsAlias                      = 0
pp_data->cap[19]->IsStringRange                = 0
pp_data->cap[19]->IsDesignatorRange            = 0
pp_data->cap[19]->Reserved1                    = 0x000000
pp_data->cap[19]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[19]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[19]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[19]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[19]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[19]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[19]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[19]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[19]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[19]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[19]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[19]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[19]->NotRange.Usage                        = 0x00D8
pp_data->cap[19]->NotRange.Reserved1                    = 0x00D8
pp_data->cap[19]->NotRange.StringIndex                  = 0
pp_data->cap[19]->NotRange.Reserved2                    = 0
pp_data->cap[19]->NotRange.DesignatorIndex              = 0
pp_data->cap[19]->NotRange.Reserved3                    = 0
pp_data->cap[19]->NotRange.DataIndex                    = 3
pp_data->cap[19]->NotRange.Reserved4                    = 3
pp_data->cap[19]->Button.LogicalMin                   = 0
pp_data->cap[19]->Button.LogicalMax                   = 0
pp_data->cap[19]->Units                    = 0
pp_data->cap[19]->UnitsExp                 = 0

pp_data->cap[20]->UsagePage                    = 0xFFA0
pp_data->cap[20]->ReportID                     = 0x1B
pp_data->cap[20]->BitPosition                  = 5
pp_data->cap[20]->BitSize                      = 1
pp_data->cap[20]->ReportCount                  = 1
pp_data->cap[20]->BytePosition                 = 0x0002
pp_data->cap[20]->BitCount                     = 1
pp_data->cap[20]->BitField                     = 0x22
pp_data->cap[20]->NextBytePosition             = 0x0003
pp_data->cap[20]->LinkCollection               = 0x0000
pp_data->cap[20]->LinkUsagePage                = 0xFFA0
pp_data->cap[20]->LinkUsage                    = 0x0003
pp_data->cap[20]->IsMultipleItemsForArray      = 0
pp_data->cap[20]->IsButtonCap                  = 1
pp_data->cap[20]->IsPadding                    = 0
pp_data->cap[20]->IsAbsolute                   = 1
pp_data->cap[20]->IsRange                      = 0
pp_data->cap[20]->IsAlias                      = 0
pp_data->cap[20]->IsStringRange                = 0
pp_data->cap[20]->IsDesignatorRange            = 0
pp_data->cap[20]->Reserved1                    = 0x000000
pp_data->cap[20]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[20]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[20]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[20]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[20]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[20]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[20]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[20]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[20]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[20]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[20]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[20]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[20]->NotRange.Usage                        = 0x002A
pp_data->cap[20]->NotRange.Reserved1                    = 0x002A
pp_data->cap[20]->NotRange.StringIndex                  = 0
pp_data->cap[20]->NotRange.Reserved2                    = 0
pp_data->cap[20]->NotRange.DesignatorIndex              = 0
pp_data->cap[20]->NotRange.Reserved3                    = 0
pp_data->cap[20]->NotRange.DataIndex                    = 4
pp_data->cap[20]->NotRange.Reserved4                    = 4
pp_data->cap[20]->Button.LogicalMin                   = 0
pp_data->cap[20]->Button.LogicalMax                   = 0
pp_data->cap[20]->Units                    = 0
pp_data->cap[20]->UnitsExp                 = 0

pp_data->cap[21]->UsagePage                    = 0xFFA0
pp_data->cap[21]->ReportID                     = 0x1B
pp_data->cap[21]->BitPosition                  = 4
pp_data->cap[21]->BitSize                      = 1
pp_data->cap[21]->ReportCount                  = 1
pp_data->cap[21]->BytePosition                 = 0x0002
pp_data->cap[21]->BitCount                     = 1
pp_data->cap[21]->BitField                     = 0x22
pp_data->cap[21]->NextBytePosition             = 0x0003
pp_data->cap[21]->LinkCollection               = 0x0000
pp_data->cap[21]->LinkUsagePage                = 0xFFA0
pp_data->cap[21]->LinkUsage                    = 0x0003
pp_data->cap[21]->IsMultipleItemsForArray      = 0
pp_data->cap[21]->IsButtonCap                  = 1
pp_data->cap[21]->IsPadding                    = 0
pp_data->cap[21]->IsAbsolute                   = 1
pp_data->cap[21]->IsRange                      = 0
pp_data->cap[21]->IsAlias                      = 0
pp_data->cap[21]->IsStringRange                = 0
pp_data->cap[21]->IsDesignatorRange            = 0
pp_data->cap[21]->Reserved1                    = 0x000000
pp_data->cap[21]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[21]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[21]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[21]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[21]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[21]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[21]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[21]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[21]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[21]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[21]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[21]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[21]->NotRange.Usage                        = 0x0020
pp_data->cap[21]->NotRange.Reserved1                    = 0x0020
pp_data->cap[21]->NotRange.StringIndex                  = 0
pp_data->cap[21]->NotRange.Reserved2                    = 0
pp_data->cap[21]->NotRange.DesignatorIndex              = 0
pp_data->cap[21]->NotRange.Reserved3                    = 0
pp_data->cap[21]->NotRange.DataIndex                    = 5
pp_data->cap[21]->NotRange.Reserved4                    = 5
pp_data->cap[21]->Button.LogicalMin                   = 0
pp_data->cap[21]->Button.LogicalMax                   = 0
pp_data->cap[21]->Units                    = 0
pp_data->cap[21]->UnitsExp                 = 0

pp_data->cap[22]->UsagePage                    = 0xFFA0
pp_data->cap[22]->ReportID                     = 0x1B
pp_data->cap[22]->BitPosition                  = 3
pp_data->cap[22]->BitSize                      = 1
pp_data->cap[22]->ReportCount                  = 1
pp_data->cap[22]->BytePosition                 = 0x0002
pp_data->cap[22]->BitCount                     = 1
pp_data->cap[22]->BitField                     = 0x22
pp_data->cap[22]->NextBytePosition             = 0x0003
pp_data->cap[22]->LinkCollection               = 0x0000
pp_data->cap[22]->LinkUsagePage                = 0xFFA0
pp_data->cap[22]->LinkUsage                    = 0x0003
pp_data->cap[22]->IsMultipleItemsForArray      = 0
pp_data->cap[22]->IsButtonCap                  = 1
pp_data->cap[22]->IsPadding                    = 0
pp_data->cap[22]->IsAbsolute                   = 1
pp_data->cap[22]->IsRange                      = 0
pp_data->cap[22]->IsAlias                      = 0
pp_data->cap[22]->IsStringRange                = 0
pp_data->cap[22]->IsDesignatorRange            = 0
pp_data->cap[22]->Reserved1                    = 0x000000
pp_data->cap[22]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[22]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[22]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[22]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[22]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[22]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[22]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[22]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[22]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[22]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[22]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[22]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[22]->NotRange.Usage                        = 0x001E
pp_data->cap[22]->NotRange.Reserved1                    = 0x001E
pp_data->cap[22]->NotRange.StringIndex                  = 0
pp_data->cap[22]->NotRange.Reserved2                    = 0
pp_data->cap[22]->NotRange.DesignatorIndex              = 0
pp_data->cap[22]->NotRange.Reserved3                    = 0
pp_data->cap[22]->NotRange.DataIndex                    = 6
pp_data->cap[22]->NotRange.Reserved4                    = 6
pp_data->cap[22]->Button.LogicalMin                   = 0
pp_data->cap[22]->Button.LogicalMax                   = 0
pp_data->cap[22]->Units                    = 0
pp_data->cap[22]->UnitsExp                 = 0

pp_data->cap[23]->UsagePage                    = 0xFFA0
pp_data->cap[23]->ReportID                     = 0x1B
pp_data->cap[23]->BitPosition                  = 2
pp_data->cap[23]->BitSize                      = 1
pp_data->cap[23]->ReportCount                  = 1
pp_data->cap[23]->BytePosition                 = 0x0002
pp_data->cap[23]->BitCount                     = 1
pp_data->cap[23]->BitField                     = 0x22
pp_data->cap[23]->NextBytePosition             = 0x0003
pp_data->cap[23]->LinkCollection               = 0x0000
pp_data->cap[23]->LinkUsagePage                = 0xFFA0
pp_data->cap[23]->LinkUsage                    = 0x0003
pp_data->cap[23]->IsMultipleItemsForArray      = 0
pp_data->cap[23]->IsButtonCap                  = 1
pp_data->cap[23]->IsPadding                    = 0
pp_data->cap[23]->IsAbsolute                   = 1
pp_data->cap[23]->IsRange                      = 0
pp_data->cap[23]->IsAlias                      = 0
pp_data->cap[23]->IsStringRange                = 0
pp_data->cap[23]->IsDesignatorRange            = 0
pp_data->cap[23]->Reserved1                    = 0x000000
pp_data->cap[23]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[23]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[23]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[23]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[23]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[23]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[23]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[23]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[23]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[23]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[23]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[23]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[23]->NotRange.Usage                        = 0x0018
pp_data->cap[23]->NotRange.Reserved1                    = 0x0018
pp_data->cap[23]->NotRange.StringIndex                  = 0
pp_data->cap[23]->NotRange.Reserved2                    = 0
pp_data->cap[23]->NotRange.DesignatorIndex              = 0
pp_data->cap[23]->NotRange.Reserved3                    = 0
pp_data->cap[23]->NotRange.DataIndex                    = 7
pp_data->cap[23]->NotRange.Reserved4                    = 7
pp_data->cap[23]->Button.LogicalMin                   = 0
pp_data->cap[23]->Button.LogicalMax                   = 0
pp_data->cap[23]->Units                    = 0
pp_data->cap[23]->UnitsExp                 = 0

pp_data->cap[24]->UsagePage                    = 0xFFA0
pp_data->cap[24]->ReportID                     = 0x1B
pp_data->cap[24]->BitPosition                  = 1
pp_data->cap[24]->BitSize                      = 1
pp_data->cap[24]->ReportCount                  = 1
pp_data->cap[24]->BytePosition                 = 0x0002
pp_data->cap[24]->BitCount                     = 1
pp_data->cap[24]->BitField                     = 0x22
pp_data->cap[24]->NextBytePosition             = 0x0003
pp_data->cap[24]->LinkCollection               = 0x0000
pp_data->cap[24]->LinkUsagePage                = 0xFFA0
pp_data->cap[24]->LinkUsage                    = 0x0003
pp_data->cap[24]->IsMultipleItemsForArray      = 0
pp_data->cap[24]->IsButtonCap                  = 1
pp_data->cap[24]->IsPadding                    = 0
pp_data->cap[24]->IsAbsolute                   = 1
pp_data->cap[24]->IsRange                      = 0
pp_data->cap[24]->IsAlias                      = 0
pp_data->cap[24]->IsStringRange                = 0
pp_data->cap[24]->IsDesignatorRange            = 0
pp_data->cap[24]->Reserved1                    = 0x000000
pp_data->cap[24]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[24]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[24]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[24]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[24]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[24]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[24]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[24]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[24]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[24]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[24]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[24]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[24]->NotRange.Usage                        = 0x0017
pp_data->cap[24]->NotRange.Reserved1                    = 0x0017
pp_data->cap[24]->NotRange.StringIndex                  = 0
pp_data->cap[24]->NotRange.Reserved2                    = 0
pp_data->cap[24]->NotRange.DesignatorIndex              = 0
pp_data->cap[24]->NotRange.Reserved3                    = 0
pp_data->cap[24]->NotRange.DataIndex                    = 8
pp_data->cap[24]->NotRange.Reserved4                    = 8
pp_data->cap[24]->Button.LogicalMin                   = 0
pp_data->cap[24]->Button.LogicalMax                   = 0
pp_data->cap[24]->Units                    = 0
pp_data->cap[24]->UnitsExp                 = 0

pp_data->cap[25]->UsagePage                    = 0xFFA0
pp_data->cap[25]->ReportID                     = 0x1B
pp_data->cap[25]->BitPosition                  = 0
pp_data->cap[25]->BitSize                      = 1
pp_data->cap[25]->ReportCount                  = 1
pp_data->cap[25]->BytePosition                 = 0x0002
pp_data->cap[25]->BitCount                     = 1
pp_data->cap[25]->BitField                     = 0x22
pp_data->cap[25]->NextBytePosition             = 0x0003
pp_data->cap[25]->LinkCollection               = 0x0000
pp_data->cap[25]->LinkUsagePage                = 0xFFA0
pp_data->cap[25]->LinkUsage                    = 0x0003
pp_data->cap[25]->IsMultipleItemsForArray      = 0
pp_data->cap[25]->IsButtonCap                  = 1
pp_data->cap[25]->IsPadding                    = 0
pp_data->cap[25]->IsAbsolute                   = 1
pp_data->cap[25]->IsRange                      = 0
pp_data->cap[25]->IsAlias                      = 0
pp_data->cap[25]->IsStringRange                = 0
pp_data->cap[25]->IsDesignatorRange            = 0
pp_data->cap[25]->Reserved1                    = 0x000000
pp_data->cap[25]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[25]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[25]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[25]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[25]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[25]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[25]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[25]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[25]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[25]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[25]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[25]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[25]->NotRange.Usage                        = 0x0009
pp_data->cap[25]->NotRange.Reserved1                    = 0x0009
pp_data->cap[25]->NotRange.StringIndex                  = 0
pp_data->cap[25]->NotRange.Reserved2                    = 0
pp_data->cap[25]->NotRange.DesignatorIndex              = 0
pp_data->cap[25]->NotRange.Reserved3                    = 0
pp_data->cap[25]->NotRange.DataIndex                    = 9
pp_data->cap[25]->NotRange.Reserved4                    = 9
pp_data->cap[25]->Button.LogicalMin                   = 0
pp_data->cap[25]->Button.LogicalMax                   = 0
pp_data->cap[25]->Units                    = 0
pp_data->cap[25]->UnitsExp                 = 0

# Link Collections:
pp_data->LinkCollectionArray[0]->LinkUsage          = 0x0003
pp_data->LinkCollectionArray[0]->LinkUsagePage      = 0xFFA0
pp_data->LinkCollectionArray[0]->Parent             = 0
pp_data->LinkCollectionArray[0]->NumberOfChildren   = 0
pp_data->LinkCollectionArray[0]->NextSibling        = 0
pp_data->LinkCollectionArray[0]->FirstChild         = 0
pp_data->LinkCollectionArray[0]->CollectionType     = 1
pp_data->LinkCollectionArray[0]->IsAlias            = 0
pp_data->LinkCollectionArray[0]->Reserved           = 0x00000000
