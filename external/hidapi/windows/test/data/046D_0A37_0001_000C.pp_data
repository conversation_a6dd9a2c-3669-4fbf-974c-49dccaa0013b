# HIDAPI device info struct:
dev->vendor_id           = 0x046D
dev->product_id          = 0x0A37
dev->manufacturer_string = "Logitech Inc "
dev->product_string      = "Logitech USB Headset H540"
dev->release_number      = 0x0122
dev->interface_number    = 3
dev->usage               = 0x0001
dev->usage_page          = 0x000C
dev->path                = "\\?\hid#vid_046d&pid_0a37&mi_03#8&1717f300&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}"

# Preparsed Data struct:
pp_data->MagicKey                             = 0x48696450204B4452
pp_data->Usage                                = 0x0001
pp_data->UsagePage                            = 0x000C
pp_data->Reserved                             = 0x00000000
# Input caps_info struct:
pp_data->caps_info[0]->FirstCap           = 0
pp_data->caps_info[0]->LastCap            = 7
pp_data->caps_info[0]->NumberOfCaps       = 9
pp_data->caps_info[0]->ReportByteLength   = 33
# Output caps_info struct:
pp_data->caps_info[1]->FirstCap           = 9
pp_data->caps_info[1]->LastCap            = 12
pp_data->caps_info[1]->NumberOfCaps       = 3
pp_data->caps_info[1]->ReportByteLength   = 37
# Feature caps_info struct:
pp_data->caps_info[2]->FirstCap           = 12
pp_data->caps_info[2]->LastCap            = 12
pp_data->caps_info[2]->NumberOfCaps       = 0
pp_data->caps_info[2]->ReportByteLength   = 0
# LinkCollectionArray Offset & Size:
pp_data->FirstByteOfLinkCollectionArray       = 0x04E0
pp_data->NumberLinkCollectionNodes            = 2
# Input hid_pp_cap struct:
pp_data->cap[0]->UsagePage                    = 0x000C
pp_data->cap[0]->ReportID                     = 0x01
pp_data->cap[0]->BitPosition                  = 1
pp_data->cap[0]->BitSize                      = 1
pp_data->cap[0]->ReportCount                  = 1
pp_data->cap[0]->BytePosition                 = 0x0001
pp_data->cap[0]->BitCount                     = 1
pp_data->cap[0]->BitField                     = 0x02
pp_data->cap[0]->NextBytePosition             = 0x0002
pp_data->cap[0]->LinkCollection               = 0x0000
pp_data->cap[0]->LinkUsagePage                = 0x000C
pp_data->cap[0]->LinkUsage                    = 0x0001
pp_data->cap[0]->IsMultipleItemsForArray      = 0
pp_data->cap[0]->IsButtonCap                  = 1
pp_data->cap[0]->IsPadding                    = 0
pp_data->cap[0]->IsAbsolute                   = 1
pp_data->cap[0]->IsRange                      = 0
pp_data->cap[0]->IsAlias                      = 0
pp_data->cap[0]->IsStringRange                = 0
pp_data->cap[0]->IsDesignatorRange            = 0
pp_data->cap[0]->Reserved1                    = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[0]->NotRange.Usage                        = 0x00EA
pp_data->cap[0]->NotRange.Reserved1                    = 0x00EA
pp_data->cap[0]->NotRange.StringIndex                  = 0
pp_data->cap[0]->NotRange.Reserved2                    = 0
pp_data->cap[0]->NotRange.DesignatorIndex              = 0
pp_data->cap[0]->NotRange.Reserved3                    = 0
pp_data->cap[0]->NotRange.DataIndex                    = 0
pp_data->cap[0]->NotRange.Reserved4                    = 0
pp_data->cap[0]->Button.LogicalMin                   = 0
pp_data->cap[0]->Button.LogicalMax                   = 0
pp_data->cap[0]->Units                    = 0
pp_data->cap[0]->UnitsExp                 = 0

pp_data->cap[1]->UsagePage                    = 0x000C
pp_data->cap[1]->ReportID                     = 0x01
pp_data->cap[1]->BitPosition                  = 0
pp_data->cap[1]->BitSize                      = 1
pp_data->cap[1]->ReportCount                  = 1
pp_data->cap[1]->BytePosition                 = 0x0001
pp_data->cap[1]->BitCount                     = 1
pp_data->cap[1]->BitField                     = 0x02
pp_data->cap[1]->NextBytePosition             = 0x0002
pp_data->cap[1]->LinkCollection               = 0x0000
pp_data->cap[1]->LinkUsagePage                = 0x000C
pp_data->cap[1]->LinkUsage                    = 0x0001
pp_data->cap[1]->IsMultipleItemsForArray      = 0
pp_data->cap[1]->IsButtonCap                  = 1
pp_data->cap[1]->IsPadding                    = 0
pp_data->cap[1]->IsAbsolute                   = 1
pp_data->cap[1]->IsRange                      = 0
pp_data->cap[1]->IsAlias                      = 0
pp_data->cap[1]->IsStringRange                = 0
pp_data->cap[1]->IsDesignatorRange            = 0
pp_data->cap[1]->Reserved1                    = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[1]->NotRange.Usage                        = 0x00E9
pp_data->cap[1]->NotRange.Reserved1                    = 0x00E9
pp_data->cap[1]->NotRange.StringIndex                  = 0
pp_data->cap[1]->NotRange.Reserved2                    = 0
pp_data->cap[1]->NotRange.DesignatorIndex              = 0
pp_data->cap[1]->NotRange.Reserved3                    = 0
pp_data->cap[1]->NotRange.DataIndex                    = 1
pp_data->cap[1]->NotRange.Reserved4                    = 1
pp_data->cap[1]->Button.LogicalMin                   = 0
pp_data->cap[1]->Button.LogicalMax                   = 0
pp_data->cap[1]->Units                    = 0
pp_data->cap[1]->UnitsExp                 = 0

pp_data->cap[2]->UsagePage                    = 0x000C
pp_data->cap[2]->ReportID                     = 0x01
pp_data->cap[2]->BitPosition                  = 2
pp_data->cap[2]->BitSize                      = 1
pp_data->cap[2]->ReportCount                  = 1
pp_data->cap[2]->BytePosition                 = 0x0001
pp_data->cap[2]->BitCount                     = 1
pp_data->cap[2]->BitField                     = 0x06
pp_data->cap[2]->NextBytePosition             = 0x0002
pp_data->cap[2]->LinkCollection               = 0x0000
pp_data->cap[2]->LinkUsagePage                = 0x000C
pp_data->cap[2]->LinkUsage                    = 0x0001
pp_data->cap[2]->IsMultipleItemsForArray      = 0
pp_data->cap[2]->IsButtonCap                  = 1
pp_data->cap[2]->IsPadding                    = 0
pp_data->cap[2]->IsAbsolute                   = 0
pp_data->cap[2]->IsRange                      = 0
pp_data->cap[2]->IsAlias                      = 0
pp_data->cap[2]->IsStringRange                = 0
pp_data->cap[2]->IsDesignatorRange            = 0
pp_data->cap[2]->Reserved1                    = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[2]->NotRange.Usage                        = 0x00E2
pp_data->cap[2]->NotRange.Reserved1                    = 0x00E2
pp_data->cap[2]->NotRange.StringIndex                  = 0
pp_data->cap[2]->NotRange.Reserved2                    = 0
pp_data->cap[2]->NotRange.DesignatorIndex              = 0
pp_data->cap[2]->NotRange.Reserved3                    = 0
pp_data->cap[2]->NotRange.DataIndex                    = 2
pp_data->cap[2]->NotRange.Reserved4                    = 2
pp_data->cap[2]->Button.LogicalMin                   = 0
pp_data->cap[2]->Button.LogicalMax                   = 0
pp_data->cap[2]->Units                    = 0
pp_data->cap[2]->UnitsExp                 = 0

pp_data->cap[3]->UsagePage                    = 0x0009
pp_data->cap[3]->ReportID                     = 0x01
pp_data->cap[3]->BitPosition                  = 5
pp_data->cap[3]->BitSize                      = 2
pp_data->cap[3]->ReportCount                  = 1
pp_data->cap[3]->BytePosition                 = 0x0001
pp_data->cap[3]->BitCount                     = 2
pp_data->cap[3]->BitField                     = 0x40
pp_data->cap[3]->NextBytePosition             = 0x0002
pp_data->cap[3]->LinkCollection               = 0x0001
pp_data->cap[3]->LinkUsagePage                = 0x000C
pp_data->cap[3]->LinkUsage                    = 0x0036
pp_data->cap[3]->IsMultipleItemsForArray      = 0
pp_data->cap[3]->IsButtonCap                  = 1
pp_data->cap[3]->IsPadding                    = 0
pp_data->cap[3]->IsAbsolute                   = 1
pp_data->cap[3]->IsRange                      = 1
pp_data->cap[3]->IsAlias                      = 0
pp_data->cap[3]->IsStringRange                = 0
pp_data->cap[3]->IsDesignatorRange            = 0
pp_data->cap[3]->Reserved1                    = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[3]->Range.UsageMin                     = 0x0001
pp_data->cap[3]->Range.UsageMax                     = 0x0002
pp_data->cap[3]->Range.StringMin                    = 0
pp_data->cap[3]->Range.StringMax                    = 0
pp_data->cap[3]->Range.DesignatorMin                = 0
pp_data->cap[3]->Range.DesignatorMax                = 0
pp_data->cap[3]->Range.DataIndexMin                 = 3
pp_data->cap[3]->Range.DataIndexMax                 = 4
pp_data->cap[3]->Button.LogicalMin                   = 1
pp_data->cap[3]->Button.LogicalMax                   = 2
pp_data->cap[3]->Units                    = 0
pp_data->cap[3]->UnitsExp                 = 0

pp_data->cap[4]->UsagePage                    = 0x000C
pp_data->cap[4]->ReportID                     = 0x02
pp_data->cap[4]->BitPosition                  = 0
pp_data->cap[4]->BitSize                      = 1
pp_data->cap[4]->ReportCount                  = 16
pp_data->cap[4]->BytePosition                 = 0x0001
pp_data->cap[4]->BitCount                     = 16
pp_data->cap[4]->BitField                     = 0x02
pp_data->cap[4]->NextBytePosition             = 0x0003
pp_data->cap[4]->LinkCollection               = 0x0000
pp_data->cap[4]->LinkUsagePage                = 0x000C
pp_data->cap[4]->LinkUsage                    = 0x0001
pp_data->cap[4]->IsMultipleItemsForArray      = 0
pp_data->cap[4]->IsButtonCap                  = 1
pp_data->cap[4]->IsPadding                    = 0
pp_data->cap[4]->IsAbsolute                   = 1
pp_data->cap[4]->IsRange                      = 0
pp_data->cap[4]->IsAlias                      = 0
pp_data->cap[4]->IsStringRange                = 0
pp_data->cap[4]->IsDesignatorRange            = 0
pp_data->cap[4]->Reserved1                    = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[4]->NotRange.Usage                        = 0x0000
pp_data->cap[4]->NotRange.Reserved1                    = 0x0000
pp_data->cap[4]->NotRange.StringIndex                  = 0
pp_data->cap[4]->NotRange.Reserved2                    = 0
pp_data->cap[4]->NotRange.DesignatorIndex              = 0
pp_data->cap[4]->NotRange.Reserved3                    = 0
pp_data->cap[4]->NotRange.DataIndex                    = 5
pp_data->cap[4]->NotRange.Reserved4                    = 5
pp_data->cap[4]->Button.LogicalMin                   = 0
pp_data->cap[4]->Button.LogicalMax                   = 0
pp_data->cap[4]->Units                    = 0
pp_data->cap[4]->UnitsExp                 = 0

pp_data->cap[5]->UsagePage                    = 0x000C
pp_data->cap[5]->ReportID                     = 0x05
pp_data->cap[5]->BitPosition                  = 0
pp_data->cap[5]->BitSize                      = 8
pp_data->cap[5]->ReportCount                  = 32
pp_data->cap[5]->BytePosition                 = 0x0001
pp_data->cap[5]->BitCount                     = 256
pp_data->cap[5]->BitField                     = 0x02
pp_data->cap[5]->NextBytePosition             = 0x0021
pp_data->cap[5]->LinkCollection               = 0x0000
pp_data->cap[5]->LinkUsagePage                = 0x000C
pp_data->cap[5]->LinkUsage                    = 0x0001
pp_data->cap[5]->IsMultipleItemsForArray      = 0
pp_data->cap[5]->IsButtonCap                  = 0
pp_data->cap[5]->IsPadding                    = 0
pp_data->cap[5]->IsAbsolute                   = 1
pp_data->cap[5]->IsRange                      = 0
pp_data->cap[5]->IsAlias                      = 0
pp_data->cap[5]->IsStringRange                = 0
pp_data->cap[5]->IsDesignatorRange            = 0
pp_data->cap[5]->Reserved1                    = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[5]->NotRange.Usage                        = 0x0000
pp_data->cap[5]->NotRange.Reserved1                    = 0x0000
pp_data->cap[5]->NotRange.StringIndex                  = 0
pp_data->cap[5]->NotRange.Reserved2                    = 0
pp_data->cap[5]->NotRange.DesignatorIndex              = 0
pp_data->cap[5]->NotRange.Reserved3                    = 0
pp_data->cap[5]->NotRange.DataIndex                    = 6
pp_data->cap[5]->NotRange.Reserved4                    = 6
pp_data->cap[5]->NotButton.HasNull                   = 0
pp_data->cap[5]->NotButton.Reserved4                 = 0x000000
pp_data->cap[5]->NotButton.LogicalMin                = 0
pp_data->cap[5]->NotButton.LogicalMax                = 1
pp_data->cap[5]->NotButton.PhysicalMin               = 0
pp_data->cap[5]->NotButton.PhysicalMax               = 0
pp_data->cap[5]->Units                    = 0
pp_data->cap[5]->UnitsExp                 = 0

pp_data->cap[6]->UsagePage                    = 0x000C
pp_data->cap[6]->ReportID                     = 0x07
pp_data->cap[6]->BitPosition                  = 0
pp_data->cap[6]->BitSize                      = 8
pp_data->cap[6]->ReportCount                  = 32
pp_data->cap[6]->BytePosition                 = 0x0001
pp_data->cap[6]->BitCount                     = 256
pp_data->cap[6]->BitField                     = 0x02
pp_data->cap[6]->NextBytePosition             = 0x0021
pp_data->cap[6]->LinkCollection               = 0x0000
pp_data->cap[6]->LinkUsagePage                = 0x000C
pp_data->cap[6]->LinkUsage                    = 0x0001
pp_data->cap[6]->IsMultipleItemsForArray      = 0
pp_data->cap[6]->IsButtonCap                  = 0
pp_data->cap[6]->IsPadding                    = 0
pp_data->cap[6]->IsAbsolute                   = 1
pp_data->cap[6]->IsRange                      = 0
pp_data->cap[6]->IsAlias                      = 0
pp_data->cap[6]->IsStringRange                = 0
pp_data->cap[6]->IsDesignatorRange            = 0
pp_data->cap[6]->Reserved1                    = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[6]->NotRange.Usage                        = 0x0000
pp_data->cap[6]->NotRange.Reserved1                    = 0x0000
pp_data->cap[6]->NotRange.StringIndex                  = 0
pp_data->cap[6]->NotRange.Reserved2                    = 0
pp_data->cap[6]->NotRange.DesignatorIndex              = 0
pp_data->cap[6]->NotRange.Reserved3                    = 0
pp_data->cap[6]->NotRange.DataIndex                    = 7
pp_data->cap[6]->NotRange.Reserved4                    = 7
pp_data->cap[6]->NotButton.HasNull                   = 0
pp_data->cap[6]->NotButton.Reserved4                 = 0x000000
pp_data->cap[6]->NotButton.LogicalMin                = 0
pp_data->cap[6]->NotButton.LogicalMax                = 1
pp_data->cap[6]->NotButton.PhysicalMin               = 0
pp_data->cap[6]->NotButton.PhysicalMax               = 0
pp_data->cap[6]->Units                    = 0
pp_data->cap[6]->UnitsExp                 = 0

# Output hid_pp_cap struct:
pp_data->cap[9]->UsagePage                    = 0x000C
pp_data->cap[9]->ReportID                     = 0x03
pp_data->cap[9]->BitPosition                  = 0
pp_data->cap[9]->BitSize                      = 1
pp_data->cap[9]->ReportCount                  = 16
pp_data->cap[9]->BytePosition                 = 0x0001
pp_data->cap[9]->BitCount                     = 16
pp_data->cap[9]->BitField                     = 0x02
pp_data->cap[9]->NextBytePosition             = 0x0003
pp_data->cap[9]->LinkCollection               = 0x0000
pp_data->cap[9]->LinkUsagePage                = 0x000C
pp_data->cap[9]->LinkUsage                    = 0x0001
pp_data->cap[9]->IsMultipleItemsForArray      = 0
pp_data->cap[9]->IsButtonCap                  = 1
pp_data->cap[9]->IsPadding                    = 0
pp_data->cap[9]->IsAbsolute                   = 1
pp_data->cap[9]->IsRange                      = 0
pp_data->cap[9]->IsAlias                      = 0
pp_data->cap[9]->IsStringRange                = 0
pp_data->cap[9]->IsDesignatorRange            = 0
pp_data->cap[9]->Reserved1                    = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[9]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[9]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[9]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[9]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[9]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[9]->NotRange.Usage                        = 0x0000
pp_data->cap[9]->NotRange.Reserved1                    = 0x0000
pp_data->cap[9]->NotRange.StringIndex                  = 0
pp_data->cap[9]->NotRange.Reserved2                    = 0
pp_data->cap[9]->NotRange.DesignatorIndex              = 0
pp_data->cap[9]->NotRange.Reserved3                    = 0
pp_data->cap[9]->NotRange.DataIndex                    = 0
pp_data->cap[9]->NotRange.Reserved4                    = 0
pp_data->cap[9]->Button.LogicalMin                   = 0
pp_data->cap[9]->Button.LogicalMax                   = 0
pp_data->cap[9]->Units                    = 0
pp_data->cap[9]->UnitsExp                 = 0

pp_data->cap[10]->UsagePage                    = 0x000C
pp_data->cap[10]->ReportID                     = 0x04
pp_data->cap[10]->BitPosition                  = 0
pp_data->cap[10]->BitSize                      = 8
pp_data->cap[10]->ReportCount                  = 36
pp_data->cap[10]->BytePosition                 = 0x0001
pp_data->cap[10]->BitCount                     = 288
pp_data->cap[10]->BitField                     = 0x02
pp_data->cap[10]->NextBytePosition             = 0x0025
pp_data->cap[10]->LinkCollection               = 0x0000
pp_data->cap[10]->LinkUsagePage                = 0x000C
pp_data->cap[10]->LinkUsage                    = 0x0001
pp_data->cap[10]->IsMultipleItemsForArray      = 0
pp_data->cap[10]->IsButtonCap                  = 0
pp_data->cap[10]->IsPadding                    = 0
pp_data->cap[10]->IsAbsolute                   = 1
pp_data->cap[10]->IsRange                      = 0
pp_data->cap[10]->IsAlias                      = 0
pp_data->cap[10]->IsStringRange                = 0
pp_data->cap[10]->IsDesignatorRange            = 0
pp_data->cap[10]->Reserved1                    = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[10]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[10]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[10]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[10]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[10]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[10]->NotRange.Usage                        = 0x0000
pp_data->cap[10]->NotRange.Reserved1                    = 0x0000
pp_data->cap[10]->NotRange.StringIndex                  = 0
pp_data->cap[10]->NotRange.Reserved2                    = 0
pp_data->cap[10]->NotRange.DesignatorIndex              = 0
pp_data->cap[10]->NotRange.Reserved3                    = 0
pp_data->cap[10]->NotRange.DataIndex                    = 1
pp_data->cap[10]->NotRange.Reserved4                    = 1
pp_data->cap[10]->NotButton.HasNull                   = 0
pp_data->cap[10]->NotButton.Reserved4                 = 0x000000
pp_data->cap[10]->NotButton.LogicalMin                = 0
pp_data->cap[10]->NotButton.LogicalMax                = 1
pp_data->cap[10]->NotButton.PhysicalMin               = 0
pp_data->cap[10]->NotButton.PhysicalMax               = 0
pp_data->cap[10]->Units                    = 0
pp_data->cap[10]->UnitsExp                 = 0

pp_data->cap[11]->UsagePage                    = 0x000C
pp_data->cap[11]->ReportID                     = 0x06
pp_data->cap[11]->BitPosition                  = 0
pp_data->cap[11]->BitSize                      = 8
pp_data->cap[11]->ReportCount                  = 36
pp_data->cap[11]->BytePosition                 = 0x0001
pp_data->cap[11]->BitCount                     = 288
pp_data->cap[11]->BitField                     = 0x02
pp_data->cap[11]->NextBytePosition             = 0x0025
pp_data->cap[11]->LinkCollection               = 0x0000
pp_data->cap[11]->LinkUsagePage                = 0x000C
pp_data->cap[11]->LinkUsage                    = 0x0001
pp_data->cap[11]->IsMultipleItemsForArray      = 0
pp_data->cap[11]->IsButtonCap                  = 0
pp_data->cap[11]->IsPadding                    = 0
pp_data->cap[11]->IsAbsolute                   = 1
pp_data->cap[11]->IsRange                      = 0
pp_data->cap[11]->IsAlias                      = 0
pp_data->cap[11]->IsStringRange                = 0
pp_data->cap[11]->IsDesignatorRange            = 0
pp_data->cap[11]->Reserved1                    = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[11]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[11]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[11]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[11]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[11]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[11]->NotRange.Usage                        = 0x0000
pp_data->cap[11]->NotRange.Reserved1                    = 0x0000
pp_data->cap[11]->NotRange.StringIndex                  = 0
pp_data->cap[11]->NotRange.Reserved2                    = 0
pp_data->cap[11]->NotRange.DesignatorIndex              = 0
pp_data->cap[11]->NotRange.Reserved3                    = 0
pp_data->cap[11]->NotRange.DataIndex                    = 2
pp_data->cap[11]->NotRange.Reserved4                    = 2
pp_data->cap[11]->NotButton.HasNull                   = 0
pp_data->cap[11]->NotButton.Reserved4                 = 0x000000
pp_data->cap[11]->NotButton.LogicalMin                = 0
pp_data->cap[11]->NotButton.LogicalMax                = 1
pp_data->cap[11]->NotButton.PhysicalMin               = 0
pp_data->cap[11]->NotButton.PhysicalMax               = 0
pp_data->cap[11]->Units                    = 0
pp_data->cap[11]->UnitsExp                 = 0

# Feature hid_pp_cap struct:
# Link Collections:
pp_data->LinkCollectionArray[0]->LinkUsage          = 0x0001
pp_data->LinkCollectionArray[0]->LinkUsagePage      = 0x000C
pp_data->LinkCollectionArray[0]->Parent             = 0
pp_data->LinkCollectionArray[0]->NumberOfChildren   = 1
pp_data->LinkCollectionArray[0]->NextSibling        = 0
pp_data->LinkCollectionArray[0]->FirstChild         = 1
pp_data->LinkCollectionArray[0]->CollectionType     = 1
pp_data->LinkCollectionArray[0]->IsAlias            = 0
pp_data->LinkCollectionArray[0]->Reserved           = 0x00000000
pp_data->LinkCollectionArray[1]->LinkUsage          = 0x0036
pp_data->LinkCollectionArray[1]->LinkUsagePage      = 0x000C
pp_data->LinkCollectionArray[1]->Parent             = 0
pp_data->LinkCollectionArray[1]->NumberOfChildren   = 0
pp_data->LinkCollectionArray[1]->NextSibling        = 0
pp_data->LinkCollectionArray[1]->FirstChild         = 0
pp_data->LinkCollectionArray[1]->CollectionType     = 2
pp_data->LinkCollectionArray[1]->IsAlias            = 0
pp_data->LinkCollectionArray[1]->Reserved           = 0x00000000
