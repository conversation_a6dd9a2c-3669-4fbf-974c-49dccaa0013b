macOS USB Prober about 0x047F/0xC056 "Plantronics Blackwire 3220 Series"
05 0B 09 05 A1 01 85 08 15  
00 25 01 09 2F 75 01 95 01 81 06 09 20 09 21 75  
01 95 02 81 22 95 05 81 01 05 08 85 09 09 09 95  
01 91 22 95 07 91 01 85 17 09 17 95 01 91 22 95  
07 91 01 85 18 09 18 95 01 91 22 95 07 91 01 85  
1E 09 1E 95 01 91 22 95 07 91 01 85 20 09 20 95  
01 91 22 95 07 91 01 85 2A 09 2A 95 01 91 22 95  
07 91 01 C0

Parser output:
0x05, 0x0B,        // Usage Page (Telephony)
0x09, 0x05,        // Usage (Headset)
0xA1, 0x01,        // Collection (Application)
0x85, 0x08,        //   Report ID (8)
0x15, 0x00,        //   Logical Minimum (0)
0x25, 0x01,        //   Logical Maximum (1)
0x09, 0x2F,        //   Usage (Phone Mute)
0x75, 0x01,        //   Report Size (1)
0x95, 0x01,        //   Report Count (1)
0x81, 0x06,        //   Input (Data,Var,Rel,No Wrap,Linear,Preferred State,No Null Position)
0x09, 0x20,        //   Usage (Hook Switch)
0x09, 0x21,        //   Usage (Flash)
0x75, 0x01,        //   Report Size (1)
0x95, 0x02,        //   Report Count (2)
0x81, 0x22,        //   Input (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position)
0x95, 0x05,        //   Report Count (5)
0x81, 0x01,        //   Input (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position)
0x05, 0x08,        //   Usage Page (LEDs)
0x85, 0x09,        //   Report ID (9)
0x09, 0x09,        //   Usage (Mute)
0x95, 0x01,        //   Report Count (1)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x07,        //   Report Count (7)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x17,        //   Report ID (23)
0x09, 0x17,        //   Usage (Off-Hook)
0x95, 0x01,        //   Report Count (1)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x07,        //   Report Count (7)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x18,        //   Report ID (24)
0x09, 0x18,        //   Usage (Ring)
0x95, 0x01,        //   Report Count (1)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x07,        //   Report Count (7)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x1E,        //   Report ID (30)
0x09, 0x1E,        //   Usage (Speaker)
0x95, 0x01,        //   Report Count (1)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x07,        //   Report Count (7)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x20,        //   Report ID (32)
0x09, 0x20,        //   Usage (Hold)
0x95, 0x01,        //   Report Count (1)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x07,        //   Report Count (7)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x2A,        //   Report ID (42)
0x09, 0x2A,        //   Usage (On-Line)
0x95, 0x01,        //   Report Count (1)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x07,        //   Report Count (7)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0xC0,              // End Collection

// 109 bytes
