macOS USB Prober about 0x047F/0xC056 "Plantronics Blackwire 3220 Series"
06 A0 FF 09 03 A1 01 85 03 09 30 75  
08 95 20 91 02 85 03 09 30 75 08 95 20 81 02 85  
14 09 B1 09 B2 09 B5 09 B7 09 B3 15 00 25 01 75  
01 95 05 81 06 95 03 81 01 85 15 09 8C 15 00 27  
FF FF 00 00 75 10 95 01 81 22 85 19 09 8D 09 8F  
09 9E 09 DC 15 00 25 01 75 01 95 04 91 22 09 D2  
09 D9 15 00 25 01 75 01 95 02 91 06 95 02 91 01  
85 1A 09 B5 15 00 25 01 75 01 95 01 91 22 95 07  
91 01 85 1B 09 CF 09 B5 75 01 95 02 B1 22 09 DE  
75 01 95 01 B1 23 09 D8 95 01 B1 22 95 04 B1 01  
09 09 09 17 09 18 09 1E 09 20 09 2A 75 01 95 06  
B1 22 95 02 B1 01 85 1F 09 9C 75 01 95 01 81 06  
95 07 81 01 C0 

Parser output:
0x06, 0xA0, 0xFF,  // Usage Page (Vendor Defined 0xFFA0)
0x09, 0x03,        // Usage (0x03)
0xA1, 0x01,        // Collection (Application)
0x85, 0x03,        //   Report ID (3)
0x09, 0x30,        //   Usage (0x30)
0x75, 0x08,        //   Report Size (8)
0x95, 0x20,        //   Report Count (32)
0x91, 0x02,        //   Output (Data,Var,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x03,        //   Report ID (3)
0x09, 0x30,        //   Usage (0x30)
0x75, 0x08,        //   Report Size (8)
0x95, 0x20,        //   Report Count (32)
0x81, 0x02,        //   Input (Data,Var,Abs,No Wrap,Linear,Preferred State,No Null Position)
0x85, 0x14,        //   Report ID (20)
0x09, 0xB1,        //   Usage (0xB1)
0x09, 0xB2,        //   Usage (0xB2)
0x09, 0xB5,        //   Usage (0xB5)
0x09, 0xB7,        //   Usage (0xB7)
0x09, 0xB3,        //   Usage (0xB3)
0x15, 0x00,        //   Logical Minimum (0)
0x25, 0x01,        //   Logical Maximum (1)
0x75, 0x01,        //   Report Size (1)
0x95, 0x05,        //   Report Count (5)
0x81, 0x06,        //   Input (Data,Var,Rel,No Wrap,Linear,Preferred State,No Null Position)
0x95, 0x03,        //   Report Count (3)
0x81, 0x01,        //   Input (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position)
0x85, 0x15,        //   Report ID (21)
0x09, 0x8C,        //   Usage (0x8C)
0x15, 0x00,        //   Logical Minimum (0)
0x27, 0xFF, 0xFF, 0x00, 0x00,  //   Logical Maximum (65534)
0x75, 0x10,        //   Report Size (16)
0x95, 0x01,        //   Report Count (1)
0x81, 0x22,        //   Input (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position)
0x85, 0x19,        //   Report ID (25)
0x09, 0x8D,        //   Usage (0x8D)
0x09, 0x8F,        //   Usage (0x8F)
0x09, 0x9E,        //   Usage (0x9E)
0x09, 0xDC,        //   Usage (0xDC)
0x15, 0x00,        //   Logical Minimum (0)
0x25, 0x01,        //   Logical Maximum (1)
0x75, 0x01,        //   Report Size (1)
0x95, 0x04,        //   Report Count (4)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x09, 0xD2,        //   Usage (0xD2)
0x09, 0xD9,        //   Usage (0xD9)
0x15, 0x00,        //   Logical Minimum (0)
0x25, 0x01,        //   Logical Maximum (1)
0x75, 0x01,        //   Report Size (1)
0x95, 0x02,        //   Report Count (2)
0x91, 0x06,        //   Output (Data,Var,Rel,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x95, 0x02,        //   Report Count (2)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x1A,        //   Report ID (26)
0x09, 0xB5,        //   Usage (0xB5)
0x15, 0x00,        //   Logical Minimum (0)
0x25, 0x01,        //   Logical Maximum (1)
0x75, 0x01,        //   Report Size (1)
0x95, 0x01,        //   Report Count (1)
0x91, 0x22,        //   Output (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x07,        //   Report Count (7)
0x91, 0x01,        //   Output (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x1B,        //   Report ID (27)
0x09, 0xCF,        //   Usage (0xCF)
0x09, 0xB5,        //   Usage (0xB5)
0x75, 0x01,        //   Report Size (1)
0x95, 0x02,        //   Report Count (2)
0xB1, 0x22,        //   Feature (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x09, 0xDE,        //   Usage (0xDE)
0x75, 0x01,        //   Report Size (1)
0x95, 0x01,        //   Report Count (1)
0xB1, 0x23,        //   Feature (Const,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x09, 0xD8,        //   Usage (0xD8)
0x95, 0x01,        //   Report Count (1)
0xB1, 0x22,        //   Feature (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x04,        //   Report Count (4)
0xB1, 0x01,        //   Feature (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x09, 0x09,        //   Usage (0x09)
0x09, 0x17,        //   Usage (0x17)
0x09, 0x18,        //   Usage (0x18)
0x09, 0x1E,        //   Usage (0x1E)
0x09, 0x20,        //   Usage (0x20)
0x09, 0x2A,        //   Usage (0x2A)
0x75, 0x01,        //   Report Size (1)
0x95, 0x06,        //   Report Count (6)
0xB1, 0x22,        //   Feature (Data,Var,Abs,No Wrap,Linear,No Preferred State,No Null Position,Non-volatile)
0x95, 0x02,        //   Report Count (2)
0xB1, 0x01,        //   Feature (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position,Non-volatile)
0x85, 0x1F,        //   Report ID (31)
0x09, 0x9C,        //   Usage (0x9C)
0x75, 0x01,        //   Report Size (1)
0x95, 0x01,        //   Report Count (1)
0x81, 0x06,        //   Input (Data,Var,Rel,No Wrap,Linear,Preferred State,No Null Position)
0x95, 0x07,        //   Report Count (7)
0x81, 0x01,        //   Input (Const,Array,Abs,No Wrap,Linear,Preferred State,No Null Position)
0xC0,              // End Collection

// 193 bytes
