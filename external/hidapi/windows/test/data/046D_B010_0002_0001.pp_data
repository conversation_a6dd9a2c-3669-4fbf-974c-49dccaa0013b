# HIDAPI device info struct:
dev->vendor_id           = 0x046D
dev->product_id          = 0xB010
dev->manufacturer_string = "Logitech"
dev->product_string      = "Logitech Bluetooth Wireless Mouse"
dev->release_number      = 0x0000
dev->interface_number    = -1
dev->usage               = 0x0002
dev->usage_page          = 0x0001
dev->path                = "\\?\hid#{00001124-0000-1000-8000-00805f9b34fb}_vid&0002046d_pid&b010&col01#8&1cf1c1b9&3&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}"

# Preparsed Data struct:
pp_data->MagicKey                             = 0x48696450204B4452
pp_data->Usage                                = 0x0002
pp_data->UsagePage                            = 0x0001
pp_data->Reserved                             = 0x00000000
# Input caps_info struct:
pp_data->caps_info[0]->FirstCap           = 0
pp_data->caps_info[0]->LastCap            = 5
pp_data->caps_info[0]->NumberOfCaps       = 5
pp_data->caps_info[0]->ReportByteLength   = 7
# Output caps_info struct:
pp_data->caps_info[1]->FirstCap           = 5
pp_data->caps_info[1]->LastCap            = 5
pp_data->caps_info[1]->NumberOfCaps       = 0
pp_data->caps_info[1]->ReportByteLength   = 0
# Feature caps_info struct:
pp_data->caps_info[2]->FirstCap           = 5
pp_data->caps_info[2]->LastCap            = 5
pp_data->caps_info[2]->NumberOfCaps       = 0
pp_data->caps_info[2]->ReportByteLength   = 0
# LinkCollectionArray Offset & Size:
pp_data->FirstByteOfLinkCollectionArray       = 0x0208
pp_data->NumberLinkCollectionNodes            = 2
# Input hid_pp_cap struct:
pp_data->cap[0]->UsagePage                    = 0x0009
pp_data->cap[0]->ReportID                     = 0x02
pp_data->cap[0]->BitPosition                  = 0
pp_data->cap[0]->BitSize                      = 1
pp_data->cap[0]->ReportCount                  = 8
pp_data->cap[0]->BytePosition                 = 0x0001
pp_data->cap[0]->BitCount                     = 8
pp_data->cap[0]->BitField                     = 0x02
pp_data->cap[0]->NextBytePosition             = 0x0002
pp_data->cap[0]->LinkCollection               = 0x0001
pp_data->cap[0]->LinkUsagePage                = 0x0001
pp_data->cap[0]->LinkUsage                    = 0x0001
pp_data->cap[0]->IsMultipleItemsForArray      = 0
pp_data->cap[0]->IsButtonCap                  = 1
pp_data->cap[0]->IsPadding                    = 0
pp_data->cap[0]->IsAbsolute                   = 1
pp_data->cap[0]->IsRange                      = 1
pp_data->cap[0]->IsAlias                      = 0
pp_data->cap[0]->IsStringRange                = 0
pp_data->cap[0]->IsDesignatorRange            = 0
pp_data->cap[0]->Reserved1                    = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[0]->Range.UsageMin                     = 0x0001
pp_data->cap[0]->Range.UsageMax                     = 0x0008
pp_data->cap[0]->Range.StringMin                    = 0
pp_data->cap[0]->Range.StringMax                    = 0
pp_data->cap[0]->Range.DesignatorMin                = 0
pp_data->cap[0]->Range.DesignatorMax                = 0
pp_data->cap[0]->Range.DataIndexMin                 = 0
pp_data->cap[0]->Range.DataIndexMax                 = 7
pp_data->cap[0]->Button.LogicalMin                   = 0
pp_data->cap[0]->Button.LogicalMax                   = 0
pp_data->cap[0]->Units                    = 0
pp_data->cap[0]->UnitsExp                 = 0

pp_data->cap[1]->UsagePage                    = 0x0001
pp_data->cap[1]->ReportID                     = 0x02
pp_data->cap[1]->BitPosition                  = 4
pp_data->cap[1]->BitSize                      = 12
pp_data->cap[1]->ReportCount                  = 1
pp_data->cap[1]->BytePosition                 = 0x0003
pp_data->cap[1]->BitCount                     = 12
pp_data->cap[1]->BitField                     = 0x06
pp_data->cap[1]->NextBytePosition             = 0x0005
pp_data->cap[1]->LinkCollection               = 0x0001
pp_data->cap[1]->LinkUsagePage                = 0x0001
pp_data->cap[1]->LinkUsage                    = 0x0001
pp_data->cap[1]->IsMultipleItemsForArray      = 0
pp_data->cap[1]->IsButtonCap                  = 0
pp_data->cap[1]->IsPadding                    = 0
pp_data->cap[1]->IsAbsolute                   = 0
pp_data->cap[1]->IsRange                      = 0
pp_data->cap[1]->IsAlias                      = 0
pp_data->cap[1]->IsStringRange                = 0
pp_data->cap[1]->IsDesignatorRange            = 0
pp_data->cap[1]->Reserved1                    = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[1]->NotRange.Usage                        = 0x0031
pp_data->cap[1]->NotRange.Reserved1                    = 0x0031
pp_data->cap[1]->NotRange.StringIndex                  = 0
pp_data->cap[1]->NotRange.Reserved2                    = 0
pp_data->cap[1]->NotRange.DesignatorIndex              = 0
pp_data->cap[1]->NotRange.Reserved3                    = 0
pp_data->cap[1]->NotRange.DataIndex                    = 8
pp_data->cap[1]->NotRange.Reserved4                    = 8
pp_data->cap[1]->NotButton.HasNull                   = 0
pp_data->cap[1]->NotButton.Reserved4                 = 0x000000
pp_data->cap[1]->NotButton.LogicalMin                = -2047
pp_data->cap[1]->NotButton.LogicalMax                = 2047
pp_data->cap[1]->NotButton.PhysicalMin               = 0
pp_data->cap[1]->NotButton.PhysicalMax               = 0
pp_data->cap[1]->Units                    = 0
pp_data->cap[1]->UnitsExp                 = 0

pp_data->cap[2]->UsagePage                    = 0x0001
pp_data->cap[2]->ReportID                     = 0x02
pp_data->cap[2]->BitPosition                  = 0
pp_data->cap[2]->BitSize                      = 12
pp_data->cap[2]->ReportCount                  = 1
pp_data->cap[2]->BytePosition                 = 0x0002
pp_data->cap[2]->BitCount                     = 12
pp_data->cap[2]->BitField                     = 0x06
pp_data->cap[2]->NextBytePosition             = 0x0004
pp_data->cap[2]->LinkCollection               = 0x0001
pp_data->cap[2]->LinkUsagePage                = 0x0001
pp_data->cap[2]->LinkUsage                    = 0x0001
pp_data->cap[2]->IsMultipleItemsForArray      = 0
pp_data->cap[2]->IsButtonCap                  = 0
pp_data->cap[2]->IsPadding                    = 0
pp_data->cap[2]->IsAbsolute                   = 0
pp_data->cap[2]->IsRange                      = 0
pp_data->cap[2]->IsAlias                      = 0
pp_data->cap[2]->IsStringRange                = 0
pp_data->cap[2]->IsDesignatorRange            = 0
pp_data->cap[2]->Reserved1                    = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[2]->NotRange.Usage                        = 0x0030
pp_data->cap[2]->NotRange.Reserved1                    = 0x0030
pp_data->cap[2]->NotRange.StringIndex                  = 0
pp_data->cap[2]->NotRange.Reserved2                    = 0
pp_data->cap[2]->NotRange.DesignatorIndex              = 0
pp_data->cap[2]->NotRange.Reserved3                    = 0
pp_data->cap[2]->NotRange.DataIndex                    = 9
pp_data->cap[2]->NotRange.Reserved4                    = 9
pp_data->cap[2]->NotButton.HasNull                   = 0
pp_data->cap[2]->NotButton.Reserved4                 = 0x000000
pp_data->cap[2]->NotButton.LogicalMin                = -2047
pp_data->cap[2]->NotButton.LogicalMax                = 2047
pp_data->cap[2]->NotButton.PhysicalMin               = 0
pp_data->cap[2]->NotButton.PhysicalMax               = 0
pp_data->cap[2]->Units                    = 0
pp_data->cap[2]->UnitsExp                 = 0

pp_data->cap[3]->UsagePage                    = 0x0001
pp_data->cap[3]->ReportID                     = 0x02
pp_data->cap[3]->BitPosition                  = 0
pp_data->cap[3]->BitSize                      = 8
pp_data->cap[3]->ReportCount                  = 1
pp_data->cap[3]->BytePosition                 = 0x0005
pp_data->cap[3]->BitCount                     = 8
pp_data->cap[3]->BitField                     = 0x06
pp_data->cap[3]->NextBytePosition             = 0x0006
pp_data->cap[3]->LinkCollection               = 0x0001
pp_data->cap[3]->LinkUsagePage                = 0x0001
pp_data->cap[3]->LinkUsage                    = 0x0001
pp_data->cap[3]->IsMultipleItemsForArray      = 0
pp_data->cap[3]->IsButtonCap                  = 0
pp_data->cap[3]->IsPadding                    = 0
pp_data->cap[3]->IsAbsolute                   = 0
pp_data->cap[3]->IsRange                      = 0
pp_data->cap[3]->IsAlias                      = 0
pp_data->cap[3]->IsStringRange                = 0
pp_data->cap[3]->IsDesignatorRange            = 0
pp_data->cap[3]->Reserved1                    = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[3]->NotRange.Usage                        = 0x0038
pp_data->cap[3]->NotRange.Reserved1                    = 0x0038
pp_data->cap[3]->NotRange.StringIndex                  = 0
pp_data->cap[3]->NotRange.Reserved2                    = 0
pp_data->cap[3]->NotRange.DesignatorIndex              = 0
pp_data->cap[3]->NotRange.Reserved3                    = 0
pp_data->cap[3]->NotRange.DataIndex                    = 10
pp_data->cap[3]->NotRange.Reserved4                    = 10
pp_data->cap[3]->NotButton.HasNull                   = 0
pp_data->cap[3]->NotButton.Reserved4                 = 0x000000
pp_data->cap[3]->NotButton.LogicalMin                = -127
pp_data->cap[3]->NotButton.LogicalMax                = 127
pp_data->cap[3]->NotButton.PhysicalMin               = 0
pp_data->cap[3]->NotButton.PhysicalMax               = 0
pp_data->cap[3]->Units                    = 0
pp_data->cap[3]->UnitsExp                 = 0

pp_data->cap[4]->UsagePage                    = 0x000C
pp_data->cap[4]->ReportID                     = 0x02
pp_data->cap[4]->BitPosition                  = 0
pp_data->cap[4]->BitSize                      = 8
pp_data->cap[4]->ReportCount                  = 1
pp_data->cap[4]->BytePosition                 = 0x0006
pp_data->cap[4]->BitCount                     = 8
pp_data->cap[4]->BitField                     = 0x06
pp_data->cap[4]->NextBytePosition             = 0x0007
pp_data->cap[4]->LinkCollection               = 0x0001
pp_data->cap[4]->LinkUsagePage                = 0x0001
pp_data->cap[4]->LinkUsage                    = 0x0001
pp_data->cap[4]->IsMultipleItemsForArray      = 0
pp_data->cap[4]->IsButtonCap                  = 0
pp_data->cap[4]->IsPadding                    = 0
pp_data->cap[4]->IsAbsolute                   = 0
pp_data->cap[4]->IsRange                      = 0
pp_data->cap[4]->IsAlias                      = 0
pp_data->cap[4]->IsStringRange                = 0
pp_data->cap[4]->IsDesignatorRange            = 0
pp_data->cap[4]->Reserved1                    = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[4]->NotRange.Usage                        = 0x0238
pp_data->cap[4]->NotRange.Reserved1                    = 0x0238
pp_data->cap[4]->NotRange.StringIndex                  = 0
pp_data->cap[4]->NotRange.Reserved2                    = 0
pp_data->cap[4]->NotRange.DesignatorIndex              = 0
pp_data->cap[4]->NotRange.Reserved3                    = 0
pp_data->cap[4]->NotRange.DataIndex                    = 11
pp_data->cap[4]->NotRange.Reserved4                    = 11
pp_data->cap[4]->NotButton.HasNull                   = 0
pp_data->cap[4]->NotButton.Reserved4                 = 0x000000
pp_data->cap[4]->NotButton.LogicalMin                = -127
pp_data->cap[4]->NotButton.LogicalMax                = 127
pp_data->cap[4]->NotButton.PhysicalMin               = 0
pp_data->cap[4]->NotButton.PhysicalMax               = 0
pp_data->cap[4]->Units                    = 0
pp_data->cap[4]->UnitsExp                 = 0

# Output hid_pp_cap struct:
# Feature hid_pp_cap struct:
# Link Collections:
pp_data->LinkCollectionArray[0]->LinkUsage          = 0x0002
pp_data->LinkCollectionArray[0]->LinkUsagePage      = 0x0001
pp_data->LinkCollectionArray[0]->Parent             = 0
pp_data->LinkCollectionArray[0]->NumberOfChildren   = 1
pp_data->LinkCollectionArray[0]->NextSibling        = 0
pp_data->LinkCollectionArray[0]->FirstChild         = 1
pp_data->LinkCollectionArray[0]->CollectionType     = 1
pp_data->LinkCollectionArray[0]->IsAlias            = 0
pp_data->LinkCollectionArray[0]->Reserved           = 0x00000000
pp_data->LinkCollectionArray[1]->LinkUsage          = 0x0001
pp_data->LinkCollectionArray[1]->LinkUsagePage      = 0x0001
pp_data->LinkCollectionArray[1]->Parent             = 0
pp_data->LinkCollectionArray[1]->NumberOfChildren   = 0
pp_data->LinkCollectionArray[1]->NextSibling        = 0
pp_data->LinkCollectionArray[1]->FirstChild         = 0
pp_data->LinkCollectionArray[1]->CollectionType     = 0
pp_data->LinkCollectionArray[1]->IsAlias            = 0
pp_data->LinkCollectionArray[1]->Reserved           = 0x00000000
