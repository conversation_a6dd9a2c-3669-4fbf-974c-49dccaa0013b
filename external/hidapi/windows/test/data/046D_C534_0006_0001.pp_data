# HIDAPI device info struct:
dev->vendor_id           = 0x046D
dev->product_id          = 0xC534
dev->manufacturer_string = "Logitech"
dev->product_string      = "USB Receiver"
dev->release_number      = 0x2901
dev->interface_number    = 0
dev->usage               = 0x0006
dev->usage_page          = 0x0001
dev->path                = "\\?\hid#vid_046d&pid_c534&mi_00#7&51bc424&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd"

# Preparsed Data struct:
pp_data->MagicKey                             = 0x48696450204B4452
pp_data->Usage                                = 0x0006
pp_data->UsagePage                            = 0x0001
pp_data->Reserved                             = 0x00000000
# Input caps_info struct:
pp_data->caps_info[0]->FirstCap           = 0
pp_data->caps_info[0]->LastCap            = 2
pp_data->caps_info[0]->NumberOfCaps       = 2
pp_data->caps_info[0]->ReportByteLength   = 9
# Output caps_info struct:
pp_data->caps_info[1]->FirstCap           = 2
pp_data->caps_info[1]->LastCap            = 3
pp_data->caps_info[1]->NumberOfCaps       = 1
pp_data->caps_info[1]->ReportByteLength   = 2
# Feature caps_info struct:
pp_data->caps_info[2]->FirstCap           = 3
pp_data->caps_info[2]->LastCap            = 3
pp_data->caps_info[2]->NumberOfCaps       = 0
pp_data->caps_info[2]->ReportByteLength   = 0
# LinkCollectionArray Offset & Size:
pp_data->FirstByteOfLinkCollectionArray       = 0x0138
pp_data->NumberLinkCollectionNodes            = 1
# Input hid_pp_cap struct:
pp_data->cap[0]->UsagePage                    = 0x0007
pp_data->cap[0]->ReportID                     = 0x00
pp_data->cap[0]->BitPosition                  = 0
pp_data->cap[0]->BitSize                      = 1
pp_data->cap[0]->ReportCount                  = 8
pp_data->cap[0]->BytePosition                 = 0x0001
pp_data->cap[0]->BitCount                     = 8
pp_data->cap[0]->BitField                     = 0x02
pp_data->cap[0]->NextBytePosition             = 0x0002
pp_data->cap[0]->LinkCollection               = 0x0000
pp_data->cap[0]->LinkUsagePage                = 0x0001
pp_data->cap[0]->LinkUsage                    = 0x0006
pp_data->cap[0]->IsMultipleItemsForArray      = 0
pp_data->cap[0]->IsButtonCap                  = 1
pp_data->cap[0]->IsPadding                    = 0
pp_data->cap[0]->IsAbsolute                   = 1
pp_data->cap[0]->IsRange                      = 1
pp_data->cap[0]->IsAlias                      = 0
pp_data->cap[0]->IsStringRange                = 0
pp_data->cap[0]->IsDesignatorRange            = 0
pp_data->cap[0]->Reserved1                    = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[0]->Range.UsageMin                     = 0x00E0
pp_data->cap[0]->Range.UsageMax                     = 0x00E7
pp_data->cap[0]->Range.StringMin                    = 0
pp_data->cap[0]->Range.StringMax                    = 0
pp_data->cap[0]->Range.DesignatorMin                = 0
pp_data->cap[0]->Range.DesignatorMax                = 0
pp_data->cap[0]->Range.DataIndexMin                 = 0
pp_data->cap[0]->Range.DataIndexMax                 = 7
pp_data->cap[0]->Button.LogicalMin                   = 0
pp_data->cap[0]->Button.LogicalMax                   = 0
pp_data->cap[0]->Units                    = 0
pp_data->cap[0]->UnitsExp                 = 0

pp_data->cap[1]->UsagePage                    = 0x0007
pp_data->cap[1]->ReportID                     = 0x00
pp_data->cap[1]->BitPosition                  = 0
pp_data->cap[1]->BitSize                      = 8
pp_data->cap[1]->ReportCount                  = 6
pp_data->cap[1]->BytePosition                 = 0x0003
pp_data->cap[1]->BitCount                     = 48
pp_data->cap[1]->BitField                     = 0x00
pp_data->cap[1]->NextBytePosition             = 0x0009
pp_data->cap[1]->LinkCollection               = 0x0000
pp_data->cap[1]->LinkUsagePage                = 0x0001
pp_data->cap[1]->LinkUsage                    = 0x0006
pp_data->cap[1]->IsMultipleItemsForArray      = 0
pp_data->cap[1]->IsButtonCap                  = 1
pp_data->cap[1]->IsPadding                    = 0
pp_data->cap[1]->IsAbsolute                   = 1
pp_data->cap[1]->IsRange                      = 1
pp_data->cap[1]->IsAlias                      = 0
pp_data->cap[1]->IsStringRange                = 0
pp_data->cap[1]->IsDesignatorRange            = 0
pp_data->cap[1]->Reserved1                    = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[1]->Range.UsageMin                     = 0x0000
pp_data->cap[1]->Range.UsageMax                     = 0x00A4
pp_data->cap[1]->Range.StringMin                    = 0
pp_data->cap[1]->Range.StringMax                    = 0
pp_data->cap[1]->Range.DesignatorMin                = 0
pp_data->cap[1]->Range.DesignatorMax                = 0
pp_data->cap[1]->Range.DataIndexMin                 = 8
pp_data->cap[1]->Range.DataIndexMax                 = 172
pp_data->cap[1]->Button.LogicalMin                   = 0
pp_data->cap[1]->Button.LogicalMax                   = 164
pp_data->cap[1]->Units                    = 0
pp_data->cap[1]->UnitsExp                 = 0

# Output hid_pp_cap struct:
pp_data->cap[2]->UsagePage                    = 0x0008
pp_data->cap[2]->ReportID                     = 0x00
pp_data->cap[2]->BitPosition                  = 0
pp_data->cap[2]->BitSize                      = 1
pp_data->cap[2]->ReportCount                  = 5
pp_data->cap[2]->BytePosition                 = 0x0001
pp_data->cap[2]->BitCount                     = 5
pp_data->cap[2]->BitField                     = 0x02
pp_data->cap[2]->NextBytePosition             = 0x0002
pp_data->cap[2]->LinkCollection               = 0x0000
pp_data->cap[2]->LinkUsagePage                = 0x0001
pp_data->cap[2]->LinkUsage                    = 0x0006
pp_data->cap[2]->IsMultipleItemsForArray      = 0
pp_data->cap[2]->IsButtonCap                  = 1
pp_data->cap[2]->IsPadding                    = 0
pp_data->cap[2]->IsAbsolute                   = 1
pp_data->cap[2]->IsRange                      = 1
pp_data->cap[2]->IsAlias                      = 0
pp_data->cap[2]->IsStringRange                = 0
pp_data->cap[2]->IsDesignatorRange            = 0
pp_data->cap[2]->Reserved1                    = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[2]->Range.UsageMin                     = 0x0001
pp_data->cap[2]->Range.UsageMax                     = 0x0005
pp_data->cap[2]->Range.StringMin                    = 0
pp_data->cap[2]->Range.StringMax                    = 0
pp_data->cap[2]->Range.DesignatorMin                = 0
pp_data->cap[2]->Range.DesignatorMax                = 0
pp_data->cap[2]->Range.DataIndexMin                 = 0
pp_data->cap[2]->Range.DataIndexMax                 = 4
pp_data->cap[2]->Button.LogicalMin                   = 0
pp_data->cap[2]->Button.LogicalMax                   = 0
pp_data->cap[2]->Units                    = 0
pp_data->cap[2]->UnitsExp                 = 0

# Feature hid_pp_cap struct:
# Link Collections:
pp_data->LinkCollectionArray[0]->LinkUsage          = 0x0006
pp_data->LinkCollectionArray[0]->LinkUsagePage      = 0x0001
pp_data->LinkCollectionArray[0]->Parent             = 0
pp_data->LinkCollectionArray[0]->NumberOfChildren   = 0
pp_data->LinkCollectionArray[0]->NextSibling        = 0
pp_data->LinkCollectionArray[0]->FirstChild         = 0
pp_data->LinkCollectionArray[0]->CollectionType     = 1
pp_data->LinkCollectionArray[0]->IsAlias            = 0
pp_data->LinkCollectionArray[0]->Reserved           = 0x00000000
