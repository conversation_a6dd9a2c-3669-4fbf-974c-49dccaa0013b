# HIDAPI device info struct:
dev->vendor_id           = 0x046D
dev->product_id          = 0xC283
dev->manufacturer_string = "Logitech Inc."
dev->product_string      = "WingMan Force 3D"
dev->release_number      = 0x0106
dev->interface_number    = -1
dev->usage               = 0x0004
dev->usage_page          = 0x0001
dev->path                = "\\?\hid#vid_046d&pid_c283#7&d7fb4bf&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}"

# Preparsed Data struct:
pp_data->MagicKey                             = 0x48696450204B4452
pp_data->Usage                                = 0x0004
pp_data->UsagePage                            = 0x0001
pp_data->Reserved                             = 0x00000000
# Input caps_info struct:
pp_data->caps_info[0]->FirstCap           = 0
pp_data->caps_info[0]->LastCap            = 8
pp_data->caps_info[0]->NumberOfCaps       = 8
pp_data->caps_info[0]->ReportByteLength   = 8
# Output caps_info struct:
pp_data->caps_info[1]->FirstCap           = 8
pp_data->caps_info[1]->LastCap            = 9
pp_data->caps_info[1]->NumberOfCaps       = 1
pp_data->caps_info[1]->ReportByteLength   = 9
# Feature caps_info struct:
pp_data->caps_info[2]->FirstCap           = 9
pp_data->caps_info[2]->LastCap            = 9
pp_data->caps_info[2]->NumberOfCaps       = 0
pp_data->caps_info[2]->ReportByteLength   = 0
# LinkCollectionArray Offset & Size:
pp_data->FirstByteOfLinkCollectionArray       = 0x03A8
pp_data->NumberLinkCollectionNodes            = 4
# Input hid_pp_cap struct:
pp_data->cap[0]->UsagePage                    = 0x0001
pp_data->cap[0]->ReportID                     = 0x00
pp_data->cap[0]->BitPosition                  = 0
pp_data->cap[0]->BitSize                      = 8
pp_data->cap[0]->ReportCount                  = 1
pp_data->cap[0]->BytePosition                 = 0x0002
pp_data->cap[0]->BitCount                     = 8
pp_data->cap[0]->BitField                     = 0x02
pp_data->cap[0]->NextBytePosition             = 0x0003
pp_data->cap[0]->LinkCollection               = 0x0002
pp_data->cap[0]->LinkUsagePage                = 0x0001
pp_data->cap[0]->LinkUsage                    = 0x0001
pp_data->cap[0]->IsMultipleItemsForArray      = 0
pp_data->cap[0]->IsButtonCap                  = 0
pp_data->cap[0]->IsPadding                    = 0
pp_data->cap[0]->IsAbsolute                   = 1
pp_data->cap[0]->IsRange                      = 0
pp_data->cap[0]->IsAlias                      = 0
pp_data->cap[0]->IsStringRange                = 0
pp_data->cap[0]->IsDesignatorRange            = 0
pp_data->cap[0]->Reserved1                    = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[0]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[0]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[0]->NotRange.Usage                        = 0x0031
pp_data->cap[0]->NotRange.Reserved1                    = 0x0031
pp_data->cap[0]->NotRange.StringIndex                  = 0
pp_data->cap[0]->NotRange.Reserved2                    = 0
pp_data->cap[0]->NotRange.DesignatorIndex              = 0
pp_data->cap[0]->NotRange.Reserved3                    = 0
pp_data->cap[0]->NotRange.DataIndex                    = 0
pp_data->cap[0]->NotRange.Reserved4                    = 0
pp_data->cap[0]->NotButton.HasNull                   = 0
pp_data->cap[0]->NotButton.Reserved4                 = 0x000000
pp_data->cap[0]->NotButton.LogicalMin                = 0
pp_data->cap[0]->NotButton.LogicalMax                = 255
pp_data->cap[0]->NotButton.PhysicalMin               = 0
pp_data->cap[0]->NotButton.PhysicalMax               = 255
pp_data->cap[0]->Units                    = 0
pp_data->cap[0]->UnitsExp                 = 0

pp_data->cap[1]->UsagePage                    = 0x0001
pp_data->cap[1]->ReportID                     = 0x00
pp_data->cap[1]->BitPosition                  = 0
pp_data->cap[1]->BitSize                      = 8
pp_data->cap[1]->ReportCount                  = 1
pp_data->cap[1]->BytePosition                 = 0x0001
pp_data->cap[1]->BitCount                     = 8
pp_data->cap[1]->BitField                     = 0x02
pp_data->cap[1]->NextBytePosition             = 0x0002
pp_data->cap[1]->LinkCollection               = 0x0002
pp_data->cap[1]->LinkUsagePage                = 0x0001
pp_data->cap[1]->LinkUsage                    = 0x0001
pp_data->cap[1]->IsMultipleItemsForArray      = 0
pp_data->cap[1]->IsButtonCap                  = 0
pp_data->cap[1]->IsPadding                    = 0
pp_data->cap[1]->IsAbsolute                   = 1
pp_data->cap[1]->IsRange                      = 0
pp_data->cap[1]->IsAlias                      = 0
pp_data->cap[1]->IsStringRange                = 0
pp_data->cap[1]->IsDesignatorRange            = 0
pp_data->cap[1]->Reserved1                    = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[1]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[1]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[1]->NotRange.Usage                        = 0x0030
pp_data->cap[1]->NotRange.Reserved1                    = 0x0030
pp_data->cap[1]->NotRange.StringIndex                  = 0
pp_data->cap[1]->NotRange.Reserved2                    = 0
pp_data->cap[1]->NotRange.DesignatorIndex              = 0
pp_data->cap[1]->NotRange.Reserved3                    = 0
pp_data->cap[1]->NotRange.DataIndex                    = 1
pp_data->cap[1]->NotRange.Reserved4                    = 1
pp_data->cap[1]->NotButton.HasNull                   = 0
pp_data->cap[1]->NotButton.Reserved4                 = 0x000000
pp_data->cap[1]->NotButton.LogicalMin                = 0
pp_data->cap[1]->NotButton.LogicalMax                = 255
pp_data->cap[1]->NotButton.PhysicalMin               = 0
pp_data->cap[1]->NotButton.PhysicalMax               = 255
pp_data->cap[1]->Units                    = 0
pp_data->cap[1]->UnitsExp                 = 0

pp_data->cap[2]->UsagePage                    = 0xFF00
pp_data->cap[2]->ReportID                     = 0x00
pp_data->cap[2]->BitPosition                  = 0
pp_data->cap[2]->BitSize                      = 4
pp_data->cap[2]->ReportCount                  = 1
pp_data->cap[2]->BytePosition                 = 0x0003
pp_data->cap[2]->BitCount                     = 4
pp_data->cap[2]->BitField                     = 0x02
pp_data->cap[2]->NextBytePosition             = 0x0004
pp_data->cap[2]->LinkCollection               = 0x0002
pp_data->cap[2]->LinkUsagePage                = 0x0001
pp_data->cap[2]->LinkUsage                    = 0x0001
pp_data->cap[2]->IsMultipleItemsForArray      = 0
pp_data->cap[2]->IsButtonCap                  = 0
pp_data->cap[2]->IsPadding                    = 0
pp_data->cap[2]->IsAbsolute                   = 1
pp_data->cap[2]->IsRange                      = 0
pp_data->cap[2]->IsAlias                      = 0
pp_data->cap[2]->IsStringRange                = 0
pp_data->cap[2]->IsDesignatorRange            = 0
pp_data->cap[2]->Reserved1                    = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[2]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[2]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[2]->NotRange.Usage                        = 0x0001
pp_data->cap[2]->NotRange.Reserved1                    = 0x0001
pp_data->cap[2]->NotRange.StringIndex                  = 0
pp_data->cap[2]->NotRange.Reserved2                    = 0
pp_data->cap[2]->NotRange.DesignatorIndex              = 0
pp_data->cap[2]->NotRange.Reserved3                    = 0
pp_data->cap[2]->NotRange.DataIndex                    = 2
pp_data->cap[2]->NotRange.Reserved4                    = 2
pp_data->cap[2]->NotButton.HasNull                   = 0
pp_data->cap[2]->NotButton.Reserved4                 = 0x000000
pp_data->cap[2]->NotButton.LogicalMin                = 0
pp_data->cap[2]->NotButton.LogicalMax                = 15
pp_data->cap[2]->NotButton.PhysicalMin               = 0
pp_data->cap[2]->NotButton.PhysicalMax               = 255
pp_data->cap[2]->Units                    = 0
pp_data->cap[2]->UnitsExp                 = 0

pp_data->cap[3]->UsagePage                    = 0x0001
pp_data->cap[3]->ReportID                     = 0x00
pp_data->cap[3]->BitPosition                  = 4
pp_data->cap[3]->BitSize                      = 4
pp_data->cap[3]->ReportCount                  = 1
pp_data->cap[3]->BytePosition                 = 0x0003
pp_data->cap[3]->BitCount                     = 4
pp_data->cap[3]->BitField                     = 0x42
pp_data->cap[3]->NextBytePosition             = 0x0004
pp_data->cap[3]->LinkCollection               = 0x0002
pp_data->cap[3]->LinkUsagePage                = 0x0001
pp_data->cap[3]->LinkUsage                    = 0x0001
pp_data->cap[3]->IsMultipleItemsForArray      = 0
pp_data->cap[3]->IsButtonCap                  = 0
pp_data->cap[3]->IsPadding                    = 0
pp_data->cap[3]->IsAbsolute                   = 1
pp_data->cap[3]->IsRange                      = 0
pp_data->cap[3]->IsAlias                      = 0
pp_data->cap[3]->IsStringRange                = 0
pp_data->cap[3]->IsDesignatorRange            = 0
pp_data->cap[3]->Reserved1                    = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[3]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[3]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[3]->NotRange.Usage                        = 0x0039
pp_data->cap[3]->NotRange.Reserved1                    = 0x0039
pp_data->cap[3]->NotRange.StringIndex                  = 0
pp_data->cap[3]->NotRange.Reserved2                    = 0
pp_data->cap[3]->NotRange.DesignatorIndex              = 0
pp_data->cap[3]->NotRange.Reserved3                    = 0
pp_data->cap[3]->NotRange.DataIndex                    = 3
pp_data->cap[3]->NotRange.Reserved4                    = 3
pp_data->cap[3]->NotButton.HasNull                   = 1
pp_data->cap[3]->NotButton.Reserved4                 = 0x000000
pp_data->cap[3]->NotButton.LogicalMin                = 0
pp_data->cap[3]->NotButton.LogicalMax                = 7
pp_data->cap[3]->NotButton.PhysicalMin               = 0
pp_data->cap[3]->NotButton.PhysicalMax               = 315
pp_data->cap[3]->Units                    = 20
pp_data->cap[3]->UnitsExp                 = 0

pp_data->cap[4]->UsagePage                    = 0x0001
pp_data->cap[4]->ReportID                     = 0x00
pp_data->cap[4]->BitPosition                  = 0
pp_data->cap[4]->BitSize                      = 8
pp_data->cap[4]->ReportCount                  = 1
pp_data->cap[4]->BytePosition                 = 0x0004
pp_data->cap[4]->BitCount                     = 8
pp_data->cap[4]->BitField                     = 0x02
pp_data->cap[4]->NextBytePosition             = 0x0005
pp_data->cap[4]->LinkCollection               = 0x0002
pp_data->cap[4]->LinkUsagePage                = 0x0001
pp_data->cap[4]->LinkUsage                    = 0x0001
pp_data->cap[4]->IsMultipleItemsForArray      = 0
pp_data->cap[4]->IsButtonCap                  = 0
pp_data->cap[4]->IsPadding                    = 0
pp_data->cap[4]->IsAbsolute                   = 1
pp_data->cap[4]->IsRange                      = 0
pp_data->cap[4]->IsAlias                      = 0
pp_data->cap[4]->IsStringRange                = 0
pp_data->cap[4]->IsDesignatorRange            = 0
pp_data->cap[4]->Reserved1                    = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[4]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[4]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[4]->NotRange.Usage                        = 0x0035
pp_data->cap[4]->NotRange.Reserved1                    = 0x0035
pp_data->cap[4]->NotRange.StringIndex                  = 0
pp_data->cap[4]->NotRange.Reserved2                    = 0
pp_data->cap[4]->NotRange.DesignatorIndex              = 0
pp_data->cap[4]->NotRange.Reserved3                    = 0
pp_data->cap[4]->NotRange.DataIndex                    = 4
pp_data->cap[4]->NotRange.Reserved4                    = 4
pp_data->cap[4]->NotButton.HasNull                   = 0
pp_data->cap[4]->NotButton.Reserved4                 = 0x000000
pp_data->cap[4]->NotButton.LogicalMin                = 0
pp_data->cap[4]->NotButton.LogicalMax                = 255
pp_data->cap[4]->NotButton.PhysicalMin               = 0
pp_data->cap[4]->NotButton.PhysicalMax               = 255
pp_data->cap[4]->Units                    = 20
pp_data->cap[4]->UnitsExp                 = 0

pp_data->cap[5]->UsagePage                    = 0x0009
pp_data->cap[5]->ReportID                     = 0x00
pp_data->cap[5]->BitPosition                  = 0
pp_data->cap[5]->BitSize                      = 1
pp_data->cap[5]->ReportCount                  = 7
pp_data->cap[5]->BytePosition                 = 0x0005
pp_data->cap[5]->BitCount                     = 7
pp_data->cap[5]->BitField                     = 0x02
pp_data->cap[5]->NextBytePosition             = 0x0006
pp_data->cap[5]->LinkCollection               = 0x0001
pp_data->cap[5]->LinkUsagePage                = 0x0001
pp_data->cap[5]->LinkUsage                    = 0x0000
pp_data->cap[5]->IsMultipleItemsForArray      = 0
pp_data->cap[5]->IsButtonCap                  = 1
pp_data->cap[5]->IsPadding                    = 0
pp_data->cap[5]->IsAbsolute                   = 1
pp_data->cap[5]->IsRange                      = 1
pp_data->cap[5]->IsAlias                      = 0
pp_data->cap[5]->IsStringRange                = 0
pp_data->cap[5]->IsDesignatorRange            = 0
pp_data->cap[5]->Reserved1                    = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[5]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[5]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[5]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[5]->Range.UsageMin                     = 0x0001
pp_data->cap[5]->Range.UsageMax                     = 0x0007
pp_data->cap[5]->Range.StringMin                    = 0
pp_data->cap[5]->Range.StringMax                    = 0
pp_data->cap[5]->Range.DesignatorMin                = 0
pp_data->cap[5]->Range.DesignatorMax                = 0
pp_data->cap[5]->Range.DataIndexMin                 = 5
pp_data->cap[5]->Range.DataIndexMax                 = 11
pp_data->cap[5]->Button.LogicalMin                   = 0
pp_data->cap[5]->Button.LogicalMax                   = 0
pp_data->cap[5]->Units                    = 0
pp_data->cap[5]->UnitsExp                 = 0

pp_data->cap[6]->UsagePage                    = 0x0001
pp_data->cap[6]->ReportID                     = 0x00
pp_data->cap[6]->BitPosition                  = 0
pp_data->cap[6]->BitSize                      = 8
pp_data->cap[6]->ReportCount                  = 1
pp_data->cap[6]->BytePosition                 = 0x0006
pp_data->cap[6]->BitCount                     = 8
pp_data->cap[6]->BitField                     = 0x02
pp_data->cap[6]->NextBytePosition             = 0x0007
pp_data->cap[6]->LinkCollection               = 0x0001
pp_data->cap[6]->LinkUsagePage                = 0x0001
pp_data->cap[6]->LinkUsage                    = 0x0000
pp_data->cap[6]->IsMultipleItemsForArray      = 0
pp_data->cap[6]->IsButtonCap                  = 0
pp_data->cap[6]->IsPadding                    = 0
pp_data->cap[6]->IsAbsolute                   = 1
pp_data->cap[6]->IsRange                      = 0
pp_data->cap[6]->IsAlias                      = 0
pp_data->cap[6]->IsStringRange                = 0
pp_data->cap[6]->IsDesignatorRange            = 0
pp_data->cap[6]->Reserved1                    = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[6]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[6]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[6]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[6]->NotRange.Usage                        = 0x0036
pp_data->cap[6]->NotRange.Reserved1                    = 0x0036
pp_data->cap[6]->NotRange.StringIndex                  = 0
pp_data->cap[6]->NotRange.Reserved2                    = 0
pp_data->cap[6]->NotRange.DesignatorIndex              = 0
pp_data->cap[6]->NotRange.Reserved3                    = 0
pp_data->cap[6]->NotRange.DataIndex                    = 12
pp_data->cap[6]->NotRange.Reserved4                    = 12
pp_data->cap[6]->NotButton.HasNull                   = 0
pp_data->cap[6]->NotButton.Reserved4                 = 0x000000
pp_data->cap[6]->NotButton.LogicalMin                = 0
pp_data->cap[6]->NotButton.LogicalMax                = 255
pp_data->cap[6]->NotButton.PhysicalMin               = 0
pp_data->cap[6]->NotButton.PhysicalMax               = 255
pp_data->cap[6]->Units                    = 0
pp_data->cap[6]->UnitsExp                 = 0

pp_data->cap[7]->UsagePage                    = 0xFF00
pp_data->cap[7]->ReportID                     = 0x00
pp_data->cap[7]->BitPosition                  = 0
pp_data->cap[7]->BitSize                      = 8
pp_data->cap[7]->ReportCount                  = 1
pp_data->cap[7]->BytePosition                 = 0x0007
pp_data->cap[7]->BitCount                     = 8
pp_data->cap[7]->BitField                     = 0x02
pp_data->cap[7]->NextBytePosition             = 0x0008
pp_data->cap[7]->LinkCollection               = 0x0001
pp_data->cap[7]->LinkUsagePage                = 0x0001
pp_data->cap[7]->LinkUsage                    = 0x0000
pp_data->cap[7]->IsMultipleItemsForArray      = 0
pp_data->cap[7]->IsButtonCap                  = 0
pp_data->cap[7]->IsPadding                    = 0
pp_data->cap[7]->IsAbsolute                   = 1
pp_data->cap[7]->IsRange                      = 0
pp_data->cap[7]->IsAlias                      = 0
pp_data->cap[7]->IsStringRange                = 0
pp_data->cap[7]->IsDesignatorRange            = 0
pp_data->cap[7]->Reserved1                    = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[7]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[7]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[7]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[7]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[7]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[7]->NotRange.Usage                        = 0x0001
pp_data->cap[7]->NotRange.Reserved1                    = 0x0001
pp_data->cap[7]->NotRange.StringIndex                  = 0
pp_data->cap[7]->NotRange.Reserved2                    = 0
pp_data->cap[7]->NotRange.DesignatorIndex              = 0
pp_data->cap[7]->NotRange.Reserved3                    = 0
pp_data->cap[7]->NotRange.DataIndex                    = 13
pp_data->cap[7]->NotRange.Reserved4                    = 13
pp_data->cap[7]->NotButton.HasNull                   = 0
pp_data->cap[7]->NotButton.Reserved4                 = 0x000000
pp_data->cap[7]->NotButton.LogicalMin                = 0
pp_data->cap[7]->NotButton.LogicalMax                = 255
pp_data->cap[7]->NotButton.PhysicalMin               = 0
pp_data->cap[7]->NotButton.PhysicalMax               = 255
pp_data->cap[7]->Units                    = 0
pp_data->cap[7]->UnitsExp                 = 0

# Output hid_pp_cap struct:
pp_data->cap[8]->UsagePage                    = 0xFF00
pp_data->cap[8]->ReportID                     = 0x00
pp_data->cap[8]->BitPosition                  = 0
pp_data->cap[8]->BitSize                      = 8
pp_data->cap[8]->ReportCount                  = 8
pp_data->cap[8]->BytePosition                 = 0x0001
pp_data->cap[8]->BitCount                     = 64
pp_data->cap[8]->BitField                     = 0x02
pp_data->cap[8]->NextBytePosition             = 0x0009
pp_data->cap[8]->LinkCollection               = 0x0003
pp_data->cap[8]->LinkUsagePage                = 0xFF00
pp_data->cap[8]->LinkUsage                    = 0x0000
pp_data->cap[8]->IsMultipleItemsForArray      = 0
pp_data->cap[8]->IsButtonCap                  = 0
pp_data->cap[8]->IsPadding                    = 0
pp_data->cap[8]->IsAbsolute                   = 1
pp_data->cap[8]->IsRange                      = 0
pp_data->cap[8]->IsAlias                      = 0
pp_data->cap[8]->IsStringRange                = 0
pp_data->cap[8]->IsDesignatorRange            = 0
pp_data->cap[8]->Reserved1                    = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[0].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[0].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[0].BitField = 0x00000000
pp_data->cap[8]->pp_cap->UnknownTokens[1].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[1].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[1].BitField = 0x00000000
pp_data->cap[8]->pp_cap->UnknownTokens[2].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[2].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[2].BitField = 0x00000000
pp_data->cap[8]->pp_cap->UnknownTokens[3].Token    = 0x00
pp_data->cap[8]->pp_cap->UnknownTokens[3].Reserved = 0x000000
pp_data->cap[8]->pp_cap->UnknownTokens[3].BitField = 0x00000000
pp_data->cap[8]->NotRange.Usage                        = 0x0002
pp_data->cap[8]->NotRange.Reserved1                    = 0x0002
pp_data->cap[8]->NotRange.StringIndex                  = 0
pp_data->cap[8]->NotRange.Reserved2                    = 0
pp_data->cap[8]->NotRange.DesignatorIndex              = 0
pp_data->cap[8]->NotRange.Reserved3                    = 0
pp_data->cap[8]->NotRange.DataIndex                    = 0
pp_data->cap[8]->NotRange.Reserved4                    = 0
pp_data->cap[8]->NotButton.HasNull                   = 0
pp_data->cap[8]->NotButton.Reserved4                 = 0x000000
pp_data->cap[8]->NotButton.LogicalMin                = 0
pp_data->cap[8]->NotButton.LogicalMax                = 255
pp_data->cap[8]->NotButton.PhysicalMin               = 0
pp_data->cap[8]->NotButton.PhysicalMax               = 255
pp_data->cap[8]->Units                    = 0
pp_data->cap[8]->UnitsExp                 = 0

# Feature hid_pp_cap struct:
# Link Collections:
pp_data->LinkCollectionArray[0]->LinkUsage          = 0x0004
pp_data->LinkCollectionArray[0]->LinkUsagePage      = 0x0001
pp_data->LinkCollectionArray[0]->Parent             = 0
pp_data->LinkCollectionArray[0]->NumberOfChildren   = 2
pp_data->LinkCollectionArray[0]->NextSibling        = 0
pp_data->LinkCollectionArray[0]->FirstChild         = 3
pp_data->LinkCollectionArray[0]->CollectionType     = 1
pp_data->LinkCollectionArray[0]->IsAlias            = 0
pp_data->LinkCollectionArray[0]->Reserved           = 0x00000000
pp_data->LinkCollectionArray[1]->LinkUsage          = 0x0000
pp_data->LinkCollectionArray[1]->LinkUsagePage      = 0x0001
pp_data->LinkCollectionArray[1]->Parent             = 0
pp_data->LinkCollectionArray[1]->NumberOfChildren   = 1
pp_data->LinkCollectionArray[1]->NextSibling        = 0
pp_data->LinkCollectionArray[1]->FirstChild         = 2
pp_data->LinkCollectionArray[1]->CollectionType     = 2
pp_data->LinkCollectionArray[1]->IsAlias            = 0
pp_data->LinkCollectionArray[1]->Reserved           = 0x00000000
pp_data->LinkCollectionArray[2]->LinkUsage          = 0x0001
pp_data->LinkCollectionArray[2]->LinkUsagePage      = 0x0001
pp_data->LinkCollectionArray[2]->Parent             = 1
pp_data->LinkCollectionArray[2]->NumberOfChildren   = 0
pp_data->LinkCollectionArray[2]->NextSibling        = 0
pp_data->LinkCollectionArray[2]->FirstChild         = 0
pp_data->LinkCollectionArray[2]->CollectionType     = 0
pp_data->LinkCollectionArray[2]->IsAlias            = 0
pp_data->LinkCollectionArray[2]->Reserved           = 0x00000000
pp_data->LinkCollectionArray[3]->LinkUsage          = 0x0000
pp_data->LinkCollectionArray[3]->LinkUsagePage      = 0xFF00
pp_data->LinkCollectionArray[3]->Parent             = 0
pp_data->LinkCollectionArray[3]->NumberOfChildren   = 0
pp_data->LinkCollectionArray[3]->NextSibling        = 1
pp_data->LinkCollectionArray[3]->FirstChild         = 0
pp_data->LinkCollectionArray[3]->CollectionType     = 2
pp_data->LinkCollectionArray[3]->IsAlias            = 0
pp_data->LinkCollectionArray[3]->Reserved           = 0x00000000
