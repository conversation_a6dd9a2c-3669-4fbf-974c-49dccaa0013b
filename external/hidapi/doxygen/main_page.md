# HIDAPI Doxygen output

This site is dedicated to hosting an [API reference for the HIDAPI library](#API).

For general information, see the [source repository](https://github.com/libusb/hidapi#readme).

There are also build instructions hosted on github:

- [Building from source](https://github.com/libusb/hidapi/blob/master/BUILD.md)
- [Using CMake](https://github.com/libusb/hidapi/blob/master/BUILD.cmake.md)
- [Using Autotools (deprecated)](https://github.com/libusb/hidapi/blob/master/BUILD.autotools.md)

\example test.c contains a basic example usage of the HIDAPI library.
