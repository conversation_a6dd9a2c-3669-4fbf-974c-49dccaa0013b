###########################################
# Simple Makefile for HIDAPI test program
#
# <PERSON>
# Signal 11 Software
# 2010-07-03
###########################################

all: hidtest

CC=gcc
COBJS=hid.o ../hidtest/test.o
OBJS=$(COBJS)
CFLAGS+=-I../hidapi -I. -Wall -g -c
LIBS=-framework IOKit -framework CoreFoundation


hidtest: $(OBJS)
	$(CC) -Wall -g $^ $(LIBS) -o hidtest

$(COBJS): %.o: %.c
	$(CC) $(CFLAGS) $< -o $@

clean:
	rm -f *.o hidtest

.PHONY: clean
