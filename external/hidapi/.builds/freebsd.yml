image: freebsd/latest
packages:
- autoconf
- automake
- gmake
- libiconv
- libtool
- pkgconf
- cmake
- ninja
sources:
- https://github.com/libusb/hidapi
tasks:
- configure: |
    cd hidapi
    echo Configure Autotools build
    ./bootstrap
    ./configure
    echo Configure CMake build
    mkdir -p build install_cmake
    cmake -GNinja -B build -S . -DCMAKE_INSTALL_PREFIX=install_cmake
- build-autotools: |
    cd hidapi
    make
    make DESTDIR=$PWD/root install
    make clean
- build-cmake: |
    cd hidapi/build
    ninja
    ninja install
    ninja clean
- build-manual: |
    cd hidapi/libusb
    gmake -f Makefile-manual
