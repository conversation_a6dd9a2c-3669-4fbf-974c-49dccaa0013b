name: MSYS2 clang64 build
on: [push, pull_request]

jobs:
  build:
    runs-on: windows-latest
    defaults:
      run:
        shell: msys2 {0}
    steps:
      - uses: actions/checkout@v3
      - uses: msys2/setup-msys2@v2
        with:
          msystem: clang64
          update: true
          install: git mingw-w64-clang-x86_64-cc mingw-w64-clang-x86_64-autotools
      - name: CI-Build
        run: |
          echo 'Running in MSYS2!'
          ./bootstrap.sh
          ./.private/ci-build.sh --build-dir build-msys2-clang64
