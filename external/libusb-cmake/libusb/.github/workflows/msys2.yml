name: MSYS2 build
on: [push, pull_request]

jobs:
  build:
    runs-on: windows-latest
    defaults:
      run:
        shell: msys2 {0}
    steps:
      - uses: actions/checkout@v3
      - uses: msys2/setup-msys2@v2
        with:
          msystem: MINGW64
          update: true
          install: git mingw-w64-x86_64-cc mingw-w64-x86_64-autotools
      - name: bootstrap
        run: ./bootstrap.sh
      - name: Build
        # GCC on MSYS2 doesn't have ASAN support (but Clan<PERSON> does).
        run: ./.private/ci-build.sh --build-dir build-msys2 --no-asan
      - name: Build with logging disabled
        run: ./.private/ci-build.sh --build-dir build-msys2-nolog --no-asan -- --disable-log
