/*
 * libusb synchronization on Microsoft Windows
 *
 * Copyright © 2010 <PERSON> <<EMAIL>>
 * Copyright © 2020 <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#include "libusbi.h"

int usbi_cond_timedwait(usbi_cond_t *cond,
	usbi_mutex_t *mutex, const struct timeval *tv)
{
	DWORD millis;

	millis = (DWORD)(tv->tv_sec * 1000L) + (tv->tv_usec / 1000L);
	/* round up to next millisecond */
	if (tv->tv_usec % 1000L)
		millis++;

	if (SleepConditionVariableCS(cond, mutex, millis))
		return 0;
	else if (GetLastError() == ERROR_TIMEOUT)
		return LIBUSB_ERROR_TIMEOUT;
	else
		return LIBUSB_ERROR_OTHER;
}
