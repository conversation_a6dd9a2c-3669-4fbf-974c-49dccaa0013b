// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 45;
	objects = {

/* Begin PBXAggregateTarget section */
		008FC0321628BC9400BC5BE2 /* all */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 008FC0331628BC9400BC5BE2 /* Build configuration list for PBXAggregateTarget "all" */;
			buildPhases = (
			);
			dependencies = (
				008FC0371628BC9A00BC5BE2 /* PBXTargetDependency */,
				008FC03B1628BC9A00BC5BE2 /* PBXTargetDependency */,
				008FC03D1628BC9A00BC5BE2 /* PBXTargetDependency */,
				008FC03F1628BC9A00BC5BE2 /* PBXTargetDependency */,
				006AD4281C8C5BBC007F8C6A /* PBXTargetDependency */,
				008FC0411628BC9A00BC5BE2 /* PBXTargetDependency */,
				20468D8E24329E3800650534 /* PBXTargetDependency */,
				008A23DE236C8619004854AA /* PBXTargetDependency */,
				20468D9024329E3F00650534 /* PBXTargetDependency */,
				008FC0391628BC9A00BC5BE2 /* PBXTargetDependency */,
			);
			name = all;
			productName = all;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		006AD4251C8C5AC4007F8C6A /* hotplugtest.c in Sources */ = {isa = PBXBuildFile; fileRef = 006AD4231C8C5AAE007F8C6A /* hotplugtest.c */; };
		006AD4261C8C5AD9007F8C6A /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		008A23DA236C85AF004854AA /* stress.c in Sources */ = {isa = PBXBuildFile; fileRef = 008A23C6236C8445004854AA /* stress.c */; };
		008A23DB236C85AF004854AA /* testlib.c in Sources */ = {isa = PBXBuildFile; fileRef = 008A23CB236C849A004854AA /* testlib.c */; };
		008FBF861628B7E800BC5BE2 /* core.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBF541628B7E800BC5BE2 /* core.c */; };
		008FBF871628B7E800BC5BE2 /* descriptor.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBF551628B7E800BC5BE2 /* descriptor.c */; };
		008FBF881628B7E800BC5BE2 /* io.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBF561628B7E800BC5BE2 /* io.c */; };
		008FBF891628B7E800BC5BE2 /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; settings = {ATTRIBUTES = (Public, ); }; };
		008FBF901628B7E800BC5BE2 /* libusbi.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF671628B7E800BC5BE2 /* libusbi.h */; };
		008FBF921628B7E800BC5BE2 /* darwin_usb.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBF6C1628B7E800BC5BE2 /* darwin_usb.c */; };
		008FBF931628B7E800BC5BE2 /* darwin_usb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF6D1628B7E800BC5BE2 /* darwin_usb.h */; };
		008FBF9A1628B7E800BC5BE2 /* threads_posix.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBF741628B7E800BC5BE2 /* threads_posix.c */; };
		008FBF9B1628B7E800BC5BE2 /* threads_posix.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF751628B7E800BC5BE2 /* threads_posix.h */; };
		008FBFA01628B7E800BC5BE2 /* sync.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBF7A1628B7E800BC5BE2 /* sync.c */; };
		008FBFA11628B7E800BC5BE2 /* version.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF7B1628B7E800BC5BE2 /* version.h */; };
		008FBFA21628B7E800BC5BE2 /* version_nano.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF7C1628B7E800BC5BE2 /* version_nano.h */; };
		008FBFA51628B84200BC5BE2 /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		008FBFA71628B87000BC5BE2 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBFA61628B87000BC5BE2 /* CoreFoundation.framework */; };
		008FBFA91628B88000BC5BE2 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBFA81628B88000BC5BE2 /* IOKit.framework */; };
		008FBFAB1628B8CB00BC5BE2 /* libobjc.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBFAA1628B8CB00BC5BE2 /* libobjc.dylib */; };
		008FBFEF1628BA3500BC5BE2 /* xusb.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBFED1628BA0E00BC5BE2 /* xusb.c */; };
		008FBFFF1628BB9600BC5BE2 /* dpfp.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBFD71628BA0E00BC5BE2 /* dpfp.c */; };
		008FC01F1628BC1500BC5BE2 /* fxload.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBFE11628BA0E00BC5BE2 /* fxload.c */; };
		008FC0211628BC5200BC5BE2 /* ezusb.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBFDC1628BA0E00BC5BE2 /* ezusb.c */; };
		008FC0301628BC7400BC5BE2 /* listdevs.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBFE71628BA0E00BC5BE2 /* listdevs.c */; };
		1438D77A17A2ED9F00166101 /* hotplug.c in Sources */ = {isa = PBXBuildFile; fileRef = 1438D77817A2ED9F00166101 /* hotplug.c */; };
		1438D77F17A2F0EA00166101 /* strerror.c in Sources */ = {isa = PBXBuildFile; fileRef = 1438D77E17A2F0EA00166101 /* strerror.c */; };
		2018D95F24E453BA001589B2 /* events_posix.c in Sources */ = {isa = PBXBuildFile; fileRef = 2018D95E24E453BA001589B2 /* events_posix.c */; };
		2018D96124E453D0001589B2 /* events_posix.h in Headers */ = {isa = PBXBuildFile; fileRef = 2018D96024E453D0001589B2 /* events_posix.h */; };
		20468D70243298C100650534 /* sam3u_benchmark.c in Sources */ = {isa = PBXBuildFile; fileRef = 20468D6E243298C100650534 /* sam3u_benchmark.c */; };
		20468D7E2432990100650534 /* testlibusb.c in Sources */ = {isa = PBXBuildFile; fileRef = 20468D7C2432990000650534 /* testlibusb.c */; };
		20951C0325630F5F00ED6351 /* dpfp.c in Sources */ = {isa = PBXBuildFile; fileRef = 008FBFD71628BA0E00BC5BE2 /* dpfp.c */; settings = {COMPILER_FLAGS = "-DDPFP_THREADED"; }; };
		20951C0625630F8F00ED6351 /* ezusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFDD1628BA0E00BC5BE2 /* ezusb.h */; };
		20951C0F25630FD300ED6351 /* libusb_testlib.h in Headers */ = {isa = PBXBuildFile; fileRef = 008A23CA236C849A004854AA /* libusb_testlib.h */; };
		20951C152563125200ED6351 /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F5EF26321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F5F026321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F5F126321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F5F226321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA0F5F326321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F5F426321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F5F526321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA0F5F626321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F5F726321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F5F826321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA0F5F926321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F5FA26321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F5FB26321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA0F5FC26321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F5FD26321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F5FE26321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA0F5FF26321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F60026321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F60126321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA0F60226321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F60326321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F60426321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA0F60526321FAA00ADF3EC /* config.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBFA41628B84200BC5BE2 /* config.h */; };
		CEA0F60626321FAA00ADF3EC /* libusb.h in Headers */ = {isa = PBXBuildFile; fileRef = 008FBF5A1628B7E800BC5BE2 /* libusb.h */; };
		CEA0F60726321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */; };
		CEA45DFB2634CDFA002FA97D /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CEDCEA6E2632200A00F7AA49 /* Security.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		006AD4271C8C5BBC007F8C6A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 006AD41B1C8C5A90007F8C6A;
			remoteInfo = hotplugtest;
		};
		008A23DD236C8619004854AA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008A23D2236C8594004854AA;
			remoteInfo = stress;
		};
		008FC0361628BC9A00BC5BE2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		008FC0381628BC9A00BC5BE2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBFBC1628B9FE00BC5BE2;
			remoteInfo = xusb;
		};
		008FC03A1628BC9A00BC5BE2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBFF41628BB8B00BC5BE2;
			remoteInfo = dpfp;
		};
		008FC03C1628BC9A00BC5BE2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FC0041628BBDB00BC5BE2;
			remoteInfo = dpfp_threaded;
		};
		008FC03E1628BC9A00BC5BE2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FC0141628BC0300BC5BE2;
			remoteInfo = fxload;
		};
		008FC0401628BC9A00BC5BE2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FC0251628BC6B00BC5BE2;
			remoteInfo = listdevs;
		};
		1443EE8B1641926D007E0579 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		1443EE8D16419273007E0579 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		1443EE8F16419276007E0579 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		1443EE911641927A007E0579 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		1443EE931641927D007E0579 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		20468D812432999C00650534 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		20468D83243299A900650534 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		20468D85243299B200650534 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		20468D87243299BA00650534 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 008FBF301628B79300BC5BE2;
			remoteInfo = libusb;
		};
		20468D8D24329E3800650534 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 20468D66243298AE00650534;
			remoteInfo = sam3u_benchmark;
		};
		20468D8F24329E3F00650534 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 008FBF281628B79300BC5BE2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 20468D74243298D300650534;
			remoteInfo = testlibusb;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		006AD41C1C8C5A90007F8C6A /* hotplugtest */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = hotplugtest; sourceTree = BUILT_PRODUCTS_DIR; };
		006AD4231C8C5AAE007F8C6A /* hotplugtest.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = hotplugtest.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		008A23C6236C8445004854AA /* stress.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = stress.c; sourceTree = "<group>"; usesTabs = 1; };
		008A23CA236C849A004854AA /* libusb_testlib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = libusb_testlib.h; sourceTree = "<group>"; usesTabs = 1; };
		008A23CB236C849A004854AA /* testlib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testlib.c; sourceTree = "<group>"; usesTabs = 1; };
		008A23D3236C8594004854AA /* stress */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = stress; sourceTree = BUILT_PRODUCTS_DIR; };
		008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = "libusb-1.0.0.dylib"; sourceTree = BUILT_PRODUCTS_DIR; };
		008FBF541628B7E800BC5BE2 /* core.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = core.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		008FBF551628B7E800BC5BE2 /* descriptor.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = descriptor.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		008FBF561628B7E800BC5BE2 /* io.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = io.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		008FBF5A1628B7E800BC5BE2 /* libusb.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = libusb.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBF671628B7E800BC5BE2 /* libusbi.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = libusbi.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBF6C1628B7E800BC5BE2 /* darwin_usb.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 2; lastKnownFileType = sourcecode.c.c; path = darwin_usb.c; sourceTree = "<group>"; tabWidth = 2; usesTabs = 0; };
		008FBF6D1628B7E800BC5BE2 /* darwin_usb.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 2; lastKnownFileType = sourcecode.c.h; path = darwin_usb.h; sourceTree = "<group>"; tabWidth = 2; usesTabs = 0; };
		008FBF741628B7E800BC5BE2 /* threads_posix.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = threads_posix.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBF751628B7E800BC5BE2 /* threads_posix.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = threads_posix.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBF7A1628B7E800BC5BE2 /* sync.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = sync.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		008FBF7B1628B7E800BC5BE2 /* version.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = version.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBF7C1628B7E800BC5BE2 /* version_nano.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = version_nano.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFA41628B84200BC5BE2 /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFA61628B87000BC5BE2 /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = System/Library/Frameworks/CoreFoundation.framework; sourceTree = SDKROOT; };
		008FBFA81628B88000BC5BE2 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		008FBFAA1628B8CB00BC5BE2 /* libobjc.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libobjc.dylib; path = usr/lib/libobjc.dylib; sourceTree = SDKROOT; };
		008FBFBD1628B9FE00BC5BE2 /* xusb */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = xusb; sourceTree = BUILT_PRODUCTS_DIR; };
		008FBFD71628BA0E00BC5BE2 /* dpfp.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = dpfp.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFDC1628BA0E00BC5BE2 /* ezusb.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = ezusb.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFDD1628BA0E00BC5BE2 /* ezusb.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = ezusb.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFE11628BA0E00BC5BE2 /* fxload.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = fxload.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFE71628BA0E00BC5BE2 /* listdevs.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = listdevs.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFED1628BA0E00BC5BE2 /* xusb.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = xusb.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		008FBFF51628BB8B00BC5BE2 /* dpfp */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dpfp; sourceTree = BUILT_PRODUCTS_DIR; };
		008FC0051628BBDB00BC5BE2 /* dpfp_threaded */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = dpfp_threaded; sourceTree = BUILT_PRODUCTS_DIR; };
		008FC0151628BC0300BC5BE2 /* fxload */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = fxload; sourceTree = BUILT_PRODUCTS_DIR; };
		008FC0261628BC6B00BC5BE2 /* listdevs */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = listdevs; sourceTree = BUILT_PRODUCTS_DIR; };
		1438D77817A2ED9F00166101 /* hotplug.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = hotplug.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		1438D77E17A2F0EA00166101 /* strerror.c */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = strerror.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		1443EE8416417E63007E0579 /* common.xcconfig */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = text.xcconfig; path = common.xcconfig; sourceTree = SOURCE_ROOT; tabWidth = 4; usesTabs = 1; };
		1443EE8516417E63007E0579 /* debug.xcconfig */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = text.xcconfig; path = debug.xcconfig; sourceTree = SOURCE_ROOT; tabWidth = 4; usesTabs = 1; };
		1443EE8616417E63007E0579 /* libusb_debug.xcconfig */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = text.xcconfig; path = libusb_debug.xcconfig; sourceTree = SOURCE_ROOT; tabWidth = 4; usesTabs = 1; };
		1443EE8716417E63007E0579 /* libusb.xcconfig */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = text.xcconfig; path = libusb.xcconfig; sourceTree = SOURCE_ROOT; tabWidth = 4; usesTabs = 1; };
		1443EE8816417E63007E0579 /* release.xcconfig */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = text.xcconfig; path = release.xcconfig; sourceTree = SOURCE_ROOT; tabWidth = 4; usesTabs = 1; };
		1443EE8916417EA6007E0579 /* libusb_release.xcconfig */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = text.xcconfig; path = libusb_release.xcconfig; sourceTree = SOURCE_ROOT; tabWidth = 4; usesTabs = 1; };
		14EC13E12B3D5BA600CF9AD0 /* emscripten_webusb.cpp */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.cpp.cpp; path = emscripten_webusb.cpp; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13E22B3D5BBE00CF9AD0 /* sunos_usb.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = sunos_usb.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13E32B3D5BBE00CF9AD0 /* openbsd_usb.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = openbsd_usb.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13E42B3D5BBE00CF9AD0 /* netbsd_usb.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = netbsd_usb.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13E52B3D5BBE00CF9AD0 /* events_windows.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = events_windows.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13E62B3D5BBE00CF9AD0 /* haiku_usb_raw.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = haiku_usb_raw.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13E72B3D5BBE00CF9AD0 /* linux_netlink.c */ = {isa = PBXFileReference; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = linux_netlink.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		14EC13E82B3D5BBE00CF9AD0 /* haiku_usb_backend.cpp */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.cpp.cpp; path = haiku_usb_backend.cpp; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13E92B3D5BBE00CF9AD0 /* haiku_usb_raw.cpp */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.cpp.cpp; path = haiku_usb_raw.cpp; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13EA2B3D5BBE00CF9AD0 /* linux_usbfs.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = linux_usbfs.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13EB2B3D5BBE00CF9AD0 /* sunos_usb.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = sunos_usb.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13EC2B3D5BBE00CF9AD0 /* linux_udev.c */ = {isa = PBXFileReference; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = linux_udev.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		14EC13ED2B3D5BBE00CF9AD0 /* haiku_usb.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = haiku_usb.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13EE2B3D5BBE00CF9AD0 /* events_windows.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = events_windows.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13EF2B3D5BBE00CF9AD0 /* null_usb.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = null_usb.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F02B3D5BBE00CF9AD0 /* haiku_pollfs.cpp */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.cpp.cpp; path = haiku_pollfs.cpp; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F12B3D5BBE00CF9AD0 /* linux_usbfs.c */ = {isa = PBXFileReference; indentWidth = 8; lastKnownFileType = sourcecode.c.c; path = linux_usbfs.c; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		14EC13F22B3D5BC800CF9AD0 /* windows_common.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = windows_common.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F32B3D5BC800CF9AD0 /* threads_windows.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = threads_windows.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F42B3D5BC800CF9AD0 /* windows_winusb.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = windows_winusb.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F52B3D5BC800CF9AD0 /* windows_common.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = windows_common.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F62B3D5BC800CF9AD0 /* windows_winusb.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = windows_winusb.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F72B3D5BC800CF9AD0 /* threads_windows.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = threads_windows.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F82B3D5BC800CF9AD0 /* windows_usbdk.h */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = windows_usbdk.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		14EC13F92B3D5BC800CF9AD0 /* windows_usbdk.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = windows_usbdk.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		1472E1592B43D66B00850BA3 /* init_context.c */ = {isa = PBXFileReference; indentWidth = 2; lastKnownFileType = sourcecode.c.c; path = init_context.c; sourceTree = "<group>"; tabWidth = 2; usesTabs = 0; };
		1472E15A2B43D68600850BA3 /* stress_mt.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = stress_mt.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		1472E15D2B43D68600850BA3 /* macos.c */ = {isa = PBXFileReference; indentWidth = 2; lastKnownFileType = sourcecode.c.c; path = macos.c; sourceTree = "<group>"; tabWidth = 2; usesTabs = 0; };
		1472E15F2B43D68600850BA3 /* set_option.c */ = {isa = PBXFileReference; indentWidth = 2; lastKnownFileType = sourcecode.c.c; path = set_option.c; sourceTree = "<group>"; tabWidth = 2; usesTabs = 0; };
		1472E1602B43D69800850BA3 /* umockdev.c */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.c.c; path = umockdev.c; sourceTree = "<group>"; tabWidth = 4; usesTabs = 0; };
		2018D95E24E453BA001589B2 /* events_posix.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = events_posix.c; sourceTree = "<group>"; };
		2018D96024E453D0001589B2 /* events_posix.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = events_posix.h; sourceTree = "<group>"; };
		20468D67243298AE00650534 /* sam3u_benchmark */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = sam3u_benchmark; sourceTree = BUILT_PRODUCTS_DIR; };
		20468D6E243298C100650534 /* sam3u_benchmark.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sam3u_benchmark.c; sourceTree = "<group>"; usesTabs = 1; };
		20468D75243298D300650534 /* testlibusb */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = testlibusb; sourceTree = BUILT_PRODUCTS_DIR; };
		20468D7C2432990000650534 /* testlibusb.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = testlibusb.c; sourceTree = "<group>"; usesTabs = 1; };
		CEDCEA6E2632200A00F7AA49 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		006AD4191C8C5A90007F8C6A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5F826321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008A23D0236C8594004854AA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F60126321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FBF2E1628B79300BC5BE2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008FBFAB1628B8CB00BC5BE2 /* libobjc.dylib in Frameworks */,
				008FBFA91628B88000BC5BE2 /* IOKit.framework in Frameworks */,
				CEA45DFB2634CDFA002FA97D /* Security.framework in Frameworks */,
				008FBFA71628B87000BC5BE2 /* CoreFoundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FBFBA1628B9FE00BC5BE2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F60726321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FBFF21628BB8B00BC5BE2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				006AD4261C8C5AD9007F8C6A /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FC0021628BBDB00BC5BE2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5F226321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FC0121628BC0300BC5BE2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5F526321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FC0231628BC6B00BC5BE2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5FB26321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20468D64243298AE00650534 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5FE26321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20468D72243298D300650534 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F60426321FAA00ADF3EC /* libusb-1.0.0.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		001B1F09236C836000F231DC /* tests */ = {
			isa = PBXGroup;
			children = (
				1472E1592B43D66B00850BA3 /* init_context.c */,
				008A23CA236C849A004854AA /* libusb_testlib.h */,
				1472E15D2B43D68600850BA3 /* macos.c */,
				1472E15F2B43D68600850BA3 /* set_option.c */,
				1472E15A2B43D68600850BA3 /* stress_mt.c */,
				008A23C6236C8445004854AA /* stress.c */,
				008A23CB236C849A004854AA /* testlib.c */,
				1472E1602B43D69800850BA3 /* umockdev.c */,
			);
			name = tests;
			path = ../tests;
			sourceTree = "<group>";
		};
		008FBF261628B79300BC5BE2 = {
			isa = PBXGroup;
			children = (
				1443EE8316417DE3007E0579 /* xcconfig */,
				008FBFA41628B84200BC5BE2 /* config.h */,
				008FBF3B1628B7E800BC5BE2 /* libusb */,
				008FBFC81628BA0E00BC5BE2 /* examples */,
				001B1F09236C836000F231DC /* tests */,
				1443EE8A16419057007E0579 /* Apple */,
				008FBF321628B79300BC5BE2 /* Products */,
			);
			sourceTree = "<group>";
		};
		008FBF321628B79300BC5BE2 /* Products */ = {
			isa = PBXGroup;
			children = (
				008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */,
				008FBFF51628BB8B00BC5BE2 /* dpfp */,
				008FC0051628BBDB00BC5BE2 /* dpfp_threaded */,
				008FC0151628BC0300BC5BE2 /* fxload */,
				008FC0261628BC6B00BC5BE2 /* listdevs */,
				006AD41C1C8C5A90007F8C6A /* hotplugtest */,
				008A23D3236C8594004854AA /* stress */,
				20468D67243298AE00650534 /* sam3u_benchmark */,
				20468D75243298D300650534 /* testlibusb */,
				008FBFBD1628B9FE00BC5BE2 /* xusb */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		008FBF3B1628B7E800BC5BE2 /* libusb */ = {
			isa = PBXGroup;
			children = (
				008FBF541628B7E800BC5BE2 /* core.c */,
				008FBF551628B7E800BC5BE2 /* descriptor.c */,
				1438D77817A2ED9F00166101 /* hotplug.c */,
				008FBF561628B7E800BC5BE2 /* io.c */,
				008FBF5A1628B7E800BC5BE2 /* libusb.h */,
				008FBF671628B7E800BC5BE2 /* libusbi.h */,
				008FBF6B1628B7E800BC5BE2 /* os */,
				1438D77E17A2F0EA00166101 /* strerror.c */,
				008FBF7A1628B7E800BC5BE2 /* sync.c */,
				008FBF7B1628B7E800BC5BE2 /* version.h */,
				008FBF7C1628B7E800BC5BE2 /* version_nano.h */,
			);
			name = libusb;
			path = ../libusb;
			sourceTree = "<group>";
		};
		008FBF6B1628B7E800BC5BE2 /* os */ = {
			isa = PBXGroup;
			children = (
				008FBF6C1628B7E800BC5BE2 /* darwin_usb.c */,
				008FBF6D1628B7E800BC5BE2 /* darwin_usb.h */,
				14EC13E12B3D5BA600CF9AD0 /* emscripten_webusb.cpp */,
				2018D95E24E453BA001589B2 /* events_posix.c */,
				2018D96024E453D0001589B2 /* events_posix.h */,
				14EC13E52B3D5BBE00CF9AD0 /* events_windows.c */,
				14EC13EE2B3D5BBE00CF9AD0 /* events_windows.h */,
				14EC13F02B3D5BBE00CF9AD0 /* haiku_pollfs.cpp */,
				14EC13E82B3D5BBE00CF9AD0 /* haiku_usb_backend.cpp */,
				14EC13E92B3D5BBE00CF9AD0 /* haiku_usb_raw.cpp */,
				14EC13E62B3D5BBE00CF9AD0 /* haiku_usb_raw.h */,
				14EC13ED2B3D5BBE00CF9AD0 /* haiku_usb.h */,
				14EC13E72B3D5BBE00CF9AD0 /* linux_netlink.c */,
				14EC13EC2B3D5BBE00CF9AD0 /* linux_udev.c */,
				14EC13F12B3D5BBE00CF9AD0 /* linux_usbfs.c */,
				14EC13EA2B3D5BBE00CF9AD0 /* linux_usbfs.h */,
				14EC13E42B3D5BBE00CF9AD0 /* netbsd_usb.c */,
				14EC13EF2B3D5BBE00CF9AD0 /* null_usb.c */,
				14EC13E32B3D5BBE00CF9AD0 /* openbsd_usb.c */,
				14EC13EB2B3D5BBE00CF9AD0 /* sunos_usb.c */,
				14EC13E22B3D5BBE00CF9AD0 /* sunos_usb.h */,
				008FBF741628B7E800BC5BE2 /* threads_posix.c */,
				008FBF751628B7E800BC5BE2 /* threads_posix.h */,
				14EC13F32B3D5BC800CF9AD0 /* threads_windows.c */,
				14EC13F72B3D5BC800CF9AD0 /* threads_windows.h */,
				14EC13F52B3D5BC800CF9AD0 /* windows_common.c */,
				14EC13F22B3D5BC800CF9AD0 /* windows_common.h */,
				14EC13F92B3D5BC800CF9AD0 /* windows_usbdk.c */,
				14EC13F82B3D5BC800CF9AD0 /* windows_usbdk.h */,
				14EC13F42B3D5BC800CF9AD0 /* windows_winusb.c */,
				14EC13F62B3D5BC800CF9AD0 /* windows_winusb.h */,
			);
			path = os;
			sourceTree = "<group>";
		};
		008FBFC81628BA0E00BC5BE2 /* examples */ = {
			isa = PBXGroup;
			children = (
				008FBFD71628BA0E00BC5BE2 /* dpfp.c */,
				008FBFDC1628BA0E00BC5BE2 /* ezusb.c */,
				008FBFDD1628BA0E00BC5BE2 /* ezusb.h */,
				008FBFE11628BA0E00BC5BE2 /* fxload.c */,
				006AD4231C8C5AAE007F8C6A /* hotplugtest.c */,
				008FBFE71628BA0E00BC5BE2 /* listdevs.c */,
				20468D6E243298C100650534 /* sam3u_benchmark.c */,
				20468D7C2432990000650534 /* testlibusb.c */,
				008FBFED1628BA0E00BC5BE2 /* xusb.c */,
			);
			name = examples;
			path = ../examples;
			sourceTree = "<group>";
		};
		1443EE8316417DE3007E0579 /* xcconfig */ = {
			isa = PBXGroup;
			children = (
				1443EE8416417E63007E0579 /* common.xcconfig */,
				1443EE8516417E63007E0579 /* debug.xcconfig */,
				1443EE8816417E63007E0579 /* release.xcconfig */,
				1443EE8716417E63007E0579 /* libusb.xcconfig */,
				1443EE8616417E63007E0579 /* libusb_debug.xcconfig */,
				1443EE8916417EA6007E0579 /* libusb_release.xcconfig */,
			);
			name = xcconfig;
			path = ../libusb;
			sourceTree = "<group>";
		};
		1443EE8A16419057007E0579 /* Apple */ = {
			isa = PBXGroup;
			children = (
				008FBFAA1628B8CB00BC5BE2 /* libobjc.dylib */,
				008FBFA81628B88000BC5BE2 /* IOKit.framework */,
				008FBFA61628B87000BC5BE2 /* CoreFoundation.framework */,
				CEDCEA6E2632200A00F7AA49 /* Security.framework */,
			);
			name = Apple;
			path = ../libusb;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		008FBF2F1628B79300BC5BE2 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008FBF891628B7E800BC5BE2 /* libusb.h in Headers */,
				008FBFA51628B84200BC5BE2 /* config.h in Headers */,
				008FBF931628B7E800BC5BE2 /* darwin_usb.h in Headers */,
				2018D96124E453D0001589B2 /* events_posix.h in Headers */,
				008FBF901628B7E800BC5BE2 /* libusbi.h in Headers */,
				008FBF9B1628B7E800BC5BE2 /* threads_posix.h in Headers */,
				008FBFA11628B7E800BC5BE2 /* version.h in Headers */,
				008FBFA21628B7E800BC5BE2 /* version_nano.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951BFF25630EBE00ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5EF26321FAA00ADF3EC /* config.h in Headers */,
				20951C152563125200ED6351 /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C0125630F4100ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5F026321FAA00ADF3EC /* config.h in Headers */,
				CEA0F5F126321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C0425630F7600ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5F326321FAA00ADF3EC /* config.h in Headers */,
				20951C0625630F8F00ED6351 /* ezusb.h in Headers */,
				CEA0F5F426321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C0725630F9D00ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5F626321FAA00ADF3EC /* config.h in Headers */,
				CEA0F5F726321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C0925630FA900ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5F926321FAA00ADF3EC /* config.h in Headers */,
				CEA0F5FA26321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C0B25630FB400ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5FC26321FAA00ADF3EC /* config.h in Headers */,
				CEA0F5FD26321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C0D25630FC000ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F5FF26321FAA00ADF3EC /* config.h in Headers */,
				20951C0F25630FD300ED6351 /* libusb_testlib.h in Headers */,
				CEA0F60026321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C1025630FE300ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F60226321FAA00ADF3EC /* config.h in Headers */,
				CEA0F60326321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20951C1225630FEE00ED6351 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CEA0F60526321FAA00ADF3EC /* config.h in Headers */,
				CEA0F60626321FAA00ADF3EC /* libusb.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		006AD41B1C8C5A90007F8C6A /* hotplugtest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 006AD4221C8C5A90007F8C6A /* Build configuration list for PBXNativeTarget "hotplugtest" */;
			buildPhases = (
				20951C0725630F9D00ED6351 /* Headers */,
				006AD4181C8C5A90007F8C6A /* Sources */,
				006AD4191C8C5A90007F8C6A /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				20468D822432999C00650534 /* PBXTargetDependency */,
			);
			name = hotplugtest;
			productName = hotplugtest;
			productReference = 006AD41C1C8C5A90007F8C6A /* hotplugtest */;
			productType = "com.apple.product-type.tool";
		};
		008A23D2236C8594004854AA /* stress */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 008A23D7236C8594004854AA /* Build configuration list for PBXNativeTarget "stress" */;
			buildPhases = (
				20951C0D25630FC000ED6351 /* Headers */,
				008A23CF236C8594004854AA /* Sources */,
				008A23D0236C8594004854AA /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				20468D86243299B200650534 /* PBXTargetDependency */,
			);
			name = stress;
			productName = stress;
			productReference = 008A23D3236C8594004854AA /* stress */;
			productType = "com.apple.product-type.tool";
		};
		008FBF301628B79300BC5BE2 /* libusb */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 008FBF351628B79300BC5BE2 /* Build configuration list for PBXNativeTarget "libusb" */;
			buildPhases = (
				008FBF2F1628B79300BC5BE2 /* Headers */,
				008FBF2D1628B79300BC5BE2 /* Sources */,
				008FBF2E1628B79300BC5BE2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = libusb;
			productName = libusb;
			productReference = 008FBF311628B79300BC5BE2 /* libusb-1.0.0.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
		008FBFBC1628B9FE00BC5BE2 /* xusb */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 008FBFC61628B9FE00BC5BE2 /* Build configuration list for PBXNativeTarget "xusb" */;
			buildPhases = (
				20951C1225630FEE00ED6351 /* Headers */,
				008FBFB91628B9FE00BC5BE2 /* Sources */,
				008FBFBA1628B9FE00BC5BE2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				1443EE941641927D007E0579 /* PBXTargetDependency */,
			);
			name = xusb;
			productName = xusb;
			productReference = 008FBFBD1628B9FE00BC5BE2 /* xusb */;
			productType = "com.apple.product-type.tool";
		};
		008FBFF41628BB8B00BC5BE2 /* dpfp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 008FBFFC1628BB8C00BC5BE2 /* Build configuration list for PBXNativeTarget "dpfp" */;
			buildPhases = (
				20951BFF25630EBE00ED6351 /* Headers */,
				008FBFF11628BB8B00BC5BE2 /* Sources */,
				008FBFF21628BB8B00BC5BE2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				1443EE921641927A007E0579 /* PBXTargetDependency */,
			);
			name = dpfp;
			productName = dpfp;
			productReference = 008FBFF51628BB8B00BC5BE2 /* dpfp */;
			productType = "com.apple.product-type.tool";
		};
		008FC0041628BBDB00BC5BE2 /* dpfp_threaded */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 008FC00C1628BBDB00BC5BE2 /* Build configuration list for PBXNativeTarget "dpfp_threaded" */;
			buildPhases = (
				20951C0125630F4100ED6351 /* Headers */,
				008FC0011628BBDB00BC5BE2 /* Sources */,
				008FC0021628BBDB00BC5BE2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				1443EE9016419276007E0579 /* PBXTargetDependency */,
			);
			name = dpfp_threaded;
			productName = dpfp_threaded;
			productReference = 008FC0051628BBDB00BC5BE2 /* dpfp_threaded */;
			productType = "com.apple.product-type.tool";
		};
		008FC0141628BC0300BC5BE2 /* fxload */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 008FC01C1628BC0300BC5BE2 /* Build configuration list for PBXNativeTarget "fxload" */;
			buildPhases = (
				20951C0425630F7600ED6351 /* Headers */,
				008FC0111628BC0300BC5BE2 /* Sources */,
				008FC0121628BC0300BC5BE2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				1443EE8E16419273007E0579 /* PBXTargetDependency */,
			);
			name = fxload;
			productName = fxload;
			productReference = 008FC0151628BC0300BC5BE2 /* fxload */;
			productType = "com.apple.product-type.tool";
		};
		008FC0251628BC6B00BC5BE2 /* listdevs */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 008FC02D1628BC6B00BC5BE2 /* Build configuration list for PBXNativeTarget "listdevs" */;
			buildPhases = (
				20951C0925630FA900ED6351 /* Headers */,
				008FC0221628BC6B00BC5BE2 /* Sources */,
				008FC0231628BC6B00BC5BE2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				1443EE8C1641926D007E0579 /* PBXTargetDependency */,
			);
			name = listdevs;
			productName = listdevs;
			productReference = 008FC0261628BC6B00BC5BE2 /* listdevs */;
			productType = "com.apple.product-type.tool";
		};
		20468D66243298AE00650534 /* sam3u_benchmark */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 20468D6B243298AE00650534 /* Build configuration list for PBXNativeTarget "sam3u_benchmark" */;
			buildPhases = (
				20951C0B25630FB400ED6351 /* Headers */,
				20468D63243298AE00650534 /* Sources */,
				20468D64243298AE00650534 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				20468D84243299A900650534 /* PBXTargetDependency */,
			);
			name = sam3u_benchmark;
			productName = sam3u_benchmark;
			productReference = 20468D67243298AE00650534 /* sam3u_benchmark */;
			productType = "com.apple.product-type.tool";
		};
		20468D74243298D300650534 /* testlibusb */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 20468D79243298D300650534 /* Build configuration list for PBXNativeTarget "testlibusb" */;
			buildPhases = (
				20951C1025630FE300ED6351 /* Headers */,
				20468D71243298D300650534 /* Sources */,
				20468D72243298D300650534 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				20468D88243299BA00650534 /* PBXTargetDependency */,
			);
			name = testlibusb;
			productName = testlibusb;
			productReference = 20468D75243298D300650534 /* testlibusb */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		008FBF281628B79300BC5BE2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1120;
				ORGANIZATIONNAME = libusb;
				TargetAttributes = {
					006AD41B1C8C5A90007F8C6A = {
						CreatedOnToolsVersion = 7.2.1;
					};
					008A23D2236C8594004854AA = {
						CreatedOnToolsVersion = 11.2;
					};
					20468D66243298AE00650534 = {
						CreatedOnToolsVersion = 10.1;
					};
					20468D74243298D300650534 = {
						CreatedOnToolsVersion = 10.1;
					};
				};
			};
			buildConfigurationList = 008FBF2B1628B79300BC5BE2 /* Build configuration list for PBXProject "libusb" */;
			compatibilityVersion = "Xcode 3.1";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 008FBF261628B79300BC5BE2;
			productRefGroup = 008FBF321628B79300BC5BE2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				008FBF301628B79300BC5BE2 /* libusb */,
				008FBFF41628BB8B00BC5BE2 /* dpfp */,
				008FC0041628BBDB00BC5BE2 /* dpfp_threaded */,
				008FC0141628BC0300BC5BE2 /* fxload */,
				006AD41B1C8C5A90007F8C6A /* hotplugtest */,
				008FC0251628BC6B00BC5BE2 /* listdevs */,
				20468D66243298AE00650534 /* sam3u_benchmark */,
				008A23D2236C8594004854AA /* stress */,
				20468D74243298D300650534 /* testlibusb */,
				008FBFBC1628B9FE00BC5BE2 /* xusb */,
				008FC0321628BC9400BC5BE2 /* all */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		006AD4181C8C5A90007F8C6A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				006AD4251C8C5AC4007F8C6A /* hotplugtest.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008A23CF236C8594004854AA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008A23DA236C85AF004854AA /* stress.c in Sources */,
				008A23DB236C85AF004854AA /* testlib.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FBF2D1628B79300BC5BE2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008FBF861628B7E800BC5BE2 /* core.c in Sources */,
				008FBF921628B7E800BC5BE2 /* darwin_usb.c in Sources */,
				008FBF871628B7E800BC5BE2 /* descriptor.c in Sources */,
				2018D95F24E453BA001589B2 /* events_posix.c in Sources */,
				1438D77A17A2ED9F00166101 /* hotplug.c in Sources */,
				008FBF881628B7E800BC5BE2 /* io.c in Sources */,
				1438D77F17A2F0EA00166101 /* strerror.c in Sources */,
				008FBFA01628B7E800BC5BE2 /* sync.c in Sources */,
				008FBF9A1628B7E800BC5BE2 /* threads_posix.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FBFB91628B9FE00BC5BE2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008FBFEF1628BA3500BC5BE2 /* xusb.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FBFF11628BB8B00BC5BE2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008FBFFF1628BB9600BC5BE2 /* dpfp.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FC0011628BBDB00BC5BE2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				20951C0325630F5F00ED6351 /* dpfp.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FC0111628BC0300BC5BE2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008FC0211628BC5200BC5BE2 /* ezusb.c in Sources */,
				008FC01F1628BC1500BC5BE2 /* fxload.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		008FC0221628BC6B00BC5BE2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				008FC0301628BC7400BC5BE2 /* listdevs.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20468D63243298AE00650534 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				20468D70243298C100650534 /* sam3u_benchmark.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		20468D71243298D300650534 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				20468D7E2432990100650534 /* testlibusb.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		006AD4281C8C5BBC007F8C6A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 006AD41B1C8C5A90007F8C6A /* hotplugtest */;
			targetProxy = 006AD4271C8C5BBC007F8C6A /* PBXContainerItemProxy */;
		};
		008A23DE236C8619004854AA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008A23D2236C8594004854AA /* stress */;
			targetProxy = 008A23DD236C8619004854AA /* PBXContainerItemProxy */;
		};
		008FC0371628BC9A00BC5BE2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 008FC0361628BC9A00BC5BE2 /* PBXContainerItemProxy */;
		};
		008FC0391628BC9A00BC5BE2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBFBC1628B9FE00BC5BE2 /* xusb */;
			targetProxy = 008FC0381628BC9A00BC5BE2 /* PBXContainerItemProxy */;
		};
		008FC03B1628BC9A00BC5BE2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBFF41628BB8B00BC5BE2 /* dpfp */;
			targetProxy = 008FC03A1628BC9A00BC5BE2 /* PBXContainerItemProxy */;
		};
		008FC03D1628BC9A00BC5BE2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FC0041628BBDB00BC5BE2 /* dpfp_threaded */;
			targetProxy = 008FC03C1628BC9A00BC5BE2 /* PBXContainerItemProxy */;
		};
		008FC03F1628BC9A00BC5BE2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FC0141628BC0300BC5BE2 /* fxload */;
			targetProxy = 008FC03E1628BC9A00BC5BE2 /* PBXContainerItemProxy */;
		};
		008FC0411628BC9A00BC5BE2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FC0251628BC6B00BC5BE2 /* listdevs */;
			targetProxy = 008FC0401628BC9A00BC5BE2 /* PBXContainerItemProxy */;
		};
		1443EE8C1641926D007E0579 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 1443EE8B1641926D007E0579 /* PBXContainerItemProxy */;
		};
		1443EE8E16419273007E0579 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 1443EE8D16419273007E0579 /* PBXContainerItemProxy */;
		};
		1443EE9016419276007E0579 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 1443EE8F16419276007E0579 /* PBXContainerItemProxy */;
		};
		1443EE921641927A007E0579 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 1443EE911641927A007E0579 /* PBXContainerItemProxy */;
		};
		1443EE941641927D007E0579 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 1443EE931641927D007E0579 /* PBXContainerItemProxy */;
		};
		20468D822432999C00650534 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 20468D812432999C00650534 /* PBXContainerItemProxy */;
		};
		20468D84243299A900650534 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 20468D83243299A900650534 /* PBXContainerItemProxy */;
		};
		20468D86243299B200650534 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 20468D85243299B200650534 /* PBXContainerItemProxy */;
		};
		20468D88243299BA00650534 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 008FBF301628B79300BC5BE2 /* libusb */;
			targetProxy = 20468D87243299BA00650534 /* PBXContainerItemProxy */;
		};
		20468D8E24329E3800650534 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 20468D66243298AE00650534 /* sam3u_benchmark */;
			targetProxy = 20468D8D24329E3800650534 /* PBXContainerItemProxy */;
		};
		20468D9024329E3F00650534 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 20468D74243298D300650534 /* testlibusb */;
			targetProxy = 20468D8F24329E3F00650534 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		006AD4201C8C5A90007F8C6A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		006AD4211C8C5A90007F8C6A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		008A23D8236C8594004854AA /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		008A23D9236C8594004854AA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		008FBF331628B79300BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		008FBF341628B79300BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
		008FBF361628B79300BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8616417E63007E0579 /* libusb_debug.xcconfig */;
			buildSettings = {
			};
			name = Debug;
		};
		008FBF371628B79300BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8916417EA6007E0579 /* libusb_release.xcconfig */;
			buildSettings = {
			};
			name = Release;
		};
		008FBFC41628B9FE00BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		008FBFC51628B9FE00BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		008FBFFD1628BB8C00BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		008FBFFE1628BB8C00BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		008FC00D1628BBDB00BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				OTHER_CFLAGS = "-pthread";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		008FC00E1628BBDB00BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				OTHER_CFLAGS = "-pthread";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		008FC01D1628BC0300BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		008FC01E1628BC0300BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		008FC02E1628BC6B00BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		008FC02F1628BC6B00BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		008FC0341628BC9400BC5BE2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Debug;
		};
		008FC0351628BC9400BC5BE2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Release;
		};
		20468D6C243298AE00650534 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		20468D6D243298AE00650534 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		20468D7A243298D300650534 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8516417E63007E0579 /* debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		20468D7B243298D300650534 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1443EE8816417E63007E0579 /* release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "-";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		006AD4221C8C5A90007F8C6A /* Build configuration list for PBXNativeTarget "hotplugtest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				006AD4201C8C5A90007F8C6A /* Debug */,
				006AD4211C8C5A90007F8C6A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008A23D7236C8594004854AA /* Build configuration list for PBXNativeTarget "stress" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008A23D8236C8594004854AA /* Debug */,
				008A23D9236C8594004854AA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FBF2B1628B79300BC5BE2 /* Build configuration list for PBXProject "libusb" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FBF331628B79300BC5BE2 /* Debug */,
				008FBF341628B79300BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FBF351628B79300BC5BE2 /* Build configuration list for PBXNativeTarget "libusb" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FBF361628B79300BC5BE2 /* Debug */,
				008FBF371628B79300BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FBFC61628B9FE00BC5BE2 /* Build configuration list for PBXNativeTarget "xusb" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FBFC41628B9FE00BC5BE2 /* Debug */,
				008FBFC51628B9FE00BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FBFFC1628BB8C00BC5BE2 /* Build configuration list for PBXNativeTarget "dpfp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FBFFD1628BB8C00BC5BE2 /* Debug */,
				008FBFFE1628BB8C00BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FC00C1628BBDB00BC5BE2 /* Build configuration list for PBXNativeTarget "dpfp_threaded" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FC00D1628BBDB00BC5BE2 /* Debug */,
				008FC00E1628BBDB00BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FC01C1628BC0300BC5BE2 /* Build configuration list for PBXNativeTarget "fxload" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FC01D1628BC0300BC5BE2 /* Debug */,
				008FC01E1628BC0300BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FC02D1628BC6B00BC5BE2 /* Build configuration list for PBXNativeTarget "listdevs" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FC02E1628BC6B00BC5BE2 /* Debug */,
				008FC02F1628BC6B00BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		008FC0331628BC9400BC5BE2 /* Build configuration list for PBXAggregateTarget "all" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				008FC0341628BC9400BC5BE2 /* Debug */,
				008FC0351628BC9400BC5BE2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		20468D6B243298AE00650534 /* Build configuration list for PBXNativeTarget "sam3u_benchmark" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				20468D6C243298AE00650534 /* Debug */,
				20468D6D243298AE00650534 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		20468D79243298D300650534 /* Build configuration list for PBXNativeTarget "testlibusb" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				20468D7A243298D300650534 /* Debug */,
				20468D7B243298D300650534 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 008FBF281628B79300BC5BE2 /* Project object */;
}
