Installation Instructions for Windows
*************************************

If you are compiling for MinGW or cygwin, please refer to the INSTALL file,
which is automatically generated by autotools (e.g. running bootstrap.sh).

If you are using Microsoft Visual Studio:
- Using Visual Studio 2022, open /msvc/libusb.sln
- If you want to debug the library, uncomment the ENABLE_DEBUG_LOGGING define
  in msvc/config.h
- Select your configuration and compile the project.
- To target a specific toolset (previous version of Visual Studio), either
  edit PlatformToolset in /msvc/Configuration.Base.props, or supply the value
  to msbuild on the command line.
- For example, to build for VS2015 (from a different version):
  msbuild -p:PlatformToolset=v140,Platform=x64,Configuration=Release libusb.sln

Installing and building libusb via vcpkg
****************************************

You can download and install libusb using the vcpkg dependency manager:

    git clone https://github.com/Microsoft/vcpkg.git
    cd vcpkg
    ./bootstrap-vcpkg.bat
    ./vcpkg integrate install
    vcpkg install libusb

The libusb port in vcpkg is kept up to date by Microsoft team members and
community contributors. If the version is out of date, please create an issue
or pull request (https://github.com/Microsoft/vcpkg) on the vcpkg repository.

Destination directories
***********************

The binaries are located at:
/build/<PlatformToolset>/<Platform>/<Configuration>/(lib|dll)/libusb-1.0.(lib|dll)
For example: /build/v143/x64/Release/dll/libusb-1.0.dll

Troubleshooting
***************

If the compilation process complains about missing libraries, ensure that the
default library paths for your project points to the relevant directories.
If needed, these libraries can be obtained by installing the latest Windows
SDK.

Links
*****

Additional information related to the Windows backend:
  http://windows.libusb.info
