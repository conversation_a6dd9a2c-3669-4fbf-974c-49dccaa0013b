<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="Configuration.Base.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile Condition="'$(Configuration)'=='Debug'">
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
	<ClCompile Condition="'$(Configuration)'=='Debug-MT'">
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <ClCompile Condition="'$(Configuration)'=='Release'">
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
	<ClCompile Condition="'$(Configuration)'=='Release-MT'">
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
  </ItemDefinitionGroup>
</Project>