﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="ProjectConfigurations.Base.props" />
  <PropertyGroup Label="Globals">
    <ProjectGuid>{349EE8F9-7D25-4909-AAF5-FF3FADE72187}</ProjectGuid>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <Import Project="Configuration.StaticLibrary.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="Base.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <OutDir>$(IntDir)..\lib\</OutDir>
    <TargetName>libusb-1.0</TargetName>
  </PropertyGroup>
  <ItemGroup>
    <ClCompile Include="..\libusb\core.c" />
    <ClCompile Include="..\libusb\descriptor.c" />
    <ClCompile Include="..\libusb\os\events_windows.c" />
    <ClCompile Include="..\libusb\hotplug.c" />
    <ClCompile Include="..\libusb\io.c" />
    <ClCompile Include="..\libusb\strerror.c" />
    <ClCompile Include="..\libusb\sync.c" />
    <ClCompile Include="..\libusb\os\threads_windows.c" />
    <ClCompile Include="..\libusb\os\windows_common.c" />
    <ClCompile Include="..\libusb\os\windows_usbdk.c" />
    <ClCompile Include="..\libusb\os\windows_winusb.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include=".\config.h" />
    <ClInclude Include="..\libusb\os\events_windows.h" />
    <ClInclude Include="..\libusb\libusb.h" />
    <ClInclude Include="..\libusb\libusbi.h" />
    <ClInclude Include="..\libusb\os\threads_windows.h" />
    <ClInclude Include="..\libusb\version.h" />
    <ClInclude Include="..\libusb\version_nano.h" />
    <ClInclude Include="..\libusb\os\windows_common.h" />
    <ClInclude Include="..\libusb\os\windows_usbdk.h" />
    <ClInclude Include="..\libusb\os\windows_winusb.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>