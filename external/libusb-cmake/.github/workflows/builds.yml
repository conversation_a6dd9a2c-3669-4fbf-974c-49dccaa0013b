name: CI

on:
  push:
    branches: [main, v1.0.*]
  pull_request:
    branches: [main, v1.0.*]

jobs:
  ubuntu-build:
    runs-on: ubuntu-latest
    env:
      # set_option known to fail on Github Actions
      COMMON_CTEST_ARGS: --no-compress-output --output-on-failure -E "^set_option$"

    steps:
    - uses: actions/checkout@v3
      with:
        path: src
    - name: Install dependencies
      run: |
        sudo apt update
        sudo apt install libudev-dev ninja-build
    - name: Configure
      run: |
        rm -rf build install
        cmake -B build/shared -S src -GNinja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/shared -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=ON
        cmake -B build/static -S src -GNinja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/static -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=OFF
    - name: Build Shared
      working-directory: build/shared
      run: ninja install
    - name: Build Static
      working-directory: build/static
      run: ninja install
    - name: Check artifacts
      uses: andstor/file-existence-action@v2
      with:
        files: "install/shared/lib/libusb-1.0.so, \
                install/shared/include/libusb-1.0/libusb.h, \
                install/static/lib/libusb-1.0.a, \
                install/static/include/libusb-1.0/libusb.h"
        fail: true
    - name: Test Shared
      if: always()
      working-directory: build/shared
      run: ctest --output-junit test_shared.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Test Static
      if: always()
      working-directory: build/static
      run: ctest --output-junit test_static.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Upload Test Results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: Test Results Ubuntu
        path: |
          build/shared/test_shared.xml
          build/static/test_static.xml


  macos-buid:
    runs-on: macos-latest
    env:
      COMMON_CTEST_ARGS: --no-compress-output --output-on-failure

    steps:
    - uses: actions/checkout@v3
      with:
        path: src
    - name: Install dependencies
      run: brew install ninja
    - name: Configure
      run: |
        rm -rf build install
        cmake -B build/shared -S src -GNinja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/shared -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=ON
        cmake -B build/static -S src -GNinja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/static -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=OFF
    - name: Build Shared
      working-directory: build/shared
      run: ninja install
    - name: Build Static
      working-directory: build/static
      run: ninja install
    - name: Check artifacts
      uses: andstor/file-existence-action@v2
      with:
        files: "install/shared/lib/libusb-1.0.dylib, \
                install/shared/include/libusb-1.0/libusb.h, \
                install/static/lib/libusb-1.0.a, \
                install/static/include/libusb-1.0/libusb.h"
        fail: true
    - name: Test Shared
      if: always()
      working-directory: build/shared
      run: ctest --output-junit test_shared.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Test Static
      if: always()
      working-directory: build/static
      run: ctest --output-junit test_static.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Upload Test Results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: Test Results macOS
        path: |
          build/shared/test_shared.xml
          build/static/test_static.xml


  windows-msvc-buid:
    runs-on: windows-latest
    env:
      COMMON_CTEST_ARGS: --no-compress-output --output-on-failure
      VCVARS64_PATH: 'C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat'

    steps:
    - uses: actions/checkout@v3
      with:
        path: src
    - name: Install dependencies
      run: choco install ninja
    - name: Configure Visual Studio
      run: |
        cmake -B build/msvc -S src -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/msvc -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=ON
    - name: Configure NMake/Ninja
      shell: cmd
      run: |
        call "${{ env.VCVARS64_PATH }}"
        cmake -G"NMake Makefiles" -B build\nmake_shared -DCMAKE_INSTALL_PREFIX=install/nmake_shared -S src -DCMAKE_BUILD_TYPE=RelWithDebInfo -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=ON
        cmake -G"NMake Makefiles" -B build\nmake_static -DCMAKE_INSTALL_PREFIX=install/nmake_static -S src -DCMAKE_BUILD_TYPE=RelWithDebInfo -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=OFF
        cmake -GNinja -B build\ninja_shared -DCMAKE_INSTALL_PREFIX=install/ninja_shared -S src -DCMAKE_BUILD_TYPE=RelWithDebInfo -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=ON
        cmake -GNinja -B build\ninja_static -DCMAKE_INSTALL_PREFIX=install/ninja_static -S src -DCMAKE_BUILD_TYPE=RelWithDebInfo -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=OFF
    - name: Build Visual Studio
      working-directory: build/msvc
      run: cmake --build . --config RelWithDebInfo --target install
    - name: Build NMake Shared
      working-directory: build/nmake_shared
      shell: cmd
      run: |
        call "${{ env.VCVARS64_PATH }}"
        nmake install
    - name: Build NMake Static
      working-directory: build/nmake_static
      shell: cmd
      run: |
        call "${{ env.VCVARS64_PATH }}"
        nmake install
    - name: Build Ninja Shared
      working-directory: build/ninja_shared
      shell: cmd
      run: |
        call "${{ env.VCVARS64_PATH }}"
        ninja install
    - name: Build Ninja Static
      working-directory: build/ninja_static
      shell: cmd
      run: |
        call "${{ env.VCVARS64_PATH }}"
        ninja install
    - name: Check artifacts
      uses: andstor/file-existence-action@v2
      with:
        files: "install/msvc/lib/usb-1.0.lib, \
                install/msvc/bin/libusb-1.0.dll, \
                install/msvc/include/libusb-1.0/libusb.h, \
                install/nmake_shared/lib/usb-1.0.lib, \
                install/nmake_shared/bin/libusb-1.0.dll, \
                install/nmake_shared/include/libusb-1.0/libusb.h, \
                install/nmake_static/lib/libusb-1.0.lib, \
                install/nmake_static/include/libusb-1.0/libusb.h, \
                install/ninja_shared/lib/usb-1.0.lib, \
                install/ninja_shared/bin/libusb-1.0.dll, \
                install/ninja_shared/include/libusb-1.0/libusb.h, \
                install/ninja_static/lib/libusb-1.0.lib, \
                install/ninja_static/include/libusb-1.0/libusb.h"
        fail: true
    - name: Test Visual Studio
      if: always()
      working-directory: build/msvc
      shell: cmd
      run: |
        set PATH=%PATH%;${{ github.workspace }}\install\msvc\bin
        ctest -C RelWithDebInfo --output-junit test_msvc.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Test NMake Shared
      if: always()
      working-directory: build/nmake_shared
      shell: cmd
      run: |
        set PATH=%PATH%;${{ github.workspace }}\install\nmake_shared\bin
        ctest --output-junit test_nmake_shared.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Test NMake Static
      if: always()
      working-directory: build/nmake_static
      run: ctest --output-junit test_nmake_static.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Test Ninja Shared
      if: always()
      working-directory: build/ninja_shared
      shell: cmd
      run: |
        set PATH=%PATH%;${{ github.workspace }}\install\ninja_shared\bin
        ctest --output-junit test_ninja_shared.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Test Ninja Static
      if: always()
      working-directory: build/ninja_static
      run: ctest --output-junit test_ninja_static.xml ${{ env.COMMON_CTEST_ARGS }}
    - name: Upload Test Results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: Test Results MSVC
        path: |
          build/msvc/test_msvc.xml
          build/nmake_shared/test_nmake_shared.xml
          build/nmake_static/test_nmake_static.xml
          build/ninja_shared/test_ninja_shared.xml
          build/ninja_static/test_ninja_static.xml


  android-build:
    runs-on: ubuntu-latest
    env:
      # ANDROID_NATIVE_API_LEVEL may be useful to set, but by default it is set to lowest supported by the NDK
      # and that is enough for the purpose of this script
      ANDROID_ARM32_CMAKE_OPTIONS: -DCMAKE_TOOLCHAIN_FILE=${ANDROID_NDK_ROOT}/build/cmake/android.toolchain.cmake -DANDROID_ABI=armeabi-v7a
      ANDROID_ARM64_CMAKE_OPTIONS: -DCMAKE_TOOLCHAIN_FILE=${ANDROID_NDK_ROOT}/build/cmake/android.toolchain.cmake -DANDROID_ABI=arm64-v8a

    steps:
    - uses: actions/checkout@v3
      with:
        path: src
    - name: Install dependencies
      run: |
        sudo apt update
        sudo apt install ninja-build
    - name: Configure
      run: |
        rm -rf build install
        cmake -B build/shared_32 -S src -GNinja ${{ env.ANDROID_ARM32_CMAKE_OPTIONS }} -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/shared_32 -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=ON
        cmake -B build/static_32 -S src -GNinja ${{ env.ANDROID_ARM32_CMAKE_OPTIONS }} -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/static_32 -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=OFF
        cmake -B build/shared_64 -S src -GNinja ${{ env.ANDROID_ARM64_CMAKE_OPTIONS }} -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/shared_64 -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=ON
        cmake -B build/static_64 -S src -GNinja ${{ env.ANDROID_ARM64_CMAKE_OPTIONS }} -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=install/static_64 -DLIBUSB_BUILD_EXAMPLES=ON -DLIBUSB_BUILD_TESTING=ON -DLIBUSB_BUILD_SHARED_LIBS=OFF
    - name: Build Shared ARM32
      working-directory: build/shared_32
      run: ninja install
    - name: Build Static ARM32
      working-directory: build/static_32
      run: ninja install
    - name: Build Shared ARM64
      working-directory: build/shared_64
      run: ninja install
    - name: Build Static ARM64
      working-directory: build/static_64
      run: ninja install
    - name: Check artifacts
      uses: andstor/file-existence-action@v2
      with:
        files: "install/shared_32/lib/libusb-1.0.so, \
                install/shared_32/include/libusb-1.0/libusb.h, \
                install/static_32/lib/libusb-1.0.a, \
                install/static_32/include/libusb-1.0/libusb.h, \
                install/shared_64/lib/libusb-1.0.so, \
                install/shared_64/include/libusb-1.0/libusb.h, \
                install/static_64/lib/libusb-1.0.a, \
                install/static_64/include/libusb-1.0/libusb.h"
        fail: true
    # No test runs/results: cross-compiled Android binaries are not runnable on a host machine

  emscripten-build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        path: src
    - name: Setup emsdk
      uses: mymindstorm/setup-emsdk@v14
    - name: Verify emsdk
      run: emcc -v
    - name: Install dependencies
      run: |
        sudo apt update
        sudo apt install ninja-build
    - name: Configure
      run: |
        rm -rf build install
        echo "EMSDK:" $EMSDK
        cmake -B build/static -S src -GNinja -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=install/static -DLIBUSB_BUILD_EXAMPLES=OFF -DLIBUSB_BUILD_TESTING=OFF -DLIBUSB_BUILD_SHARED_LIBS=OFF -DCMAKE_TOOLCHAIN_FILE=$EMSDK/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake
    - name: Build Static
      working-directory: build/static
      run: ninja install
    - name: Check artifacts
      uses: andstor/file-existence-action@v2
      with:
        files: "install/static/lib/libusb-1.0.a, \
                install/static/include/libusb-1.0/libusb.h"
        fail: true

  publish-test-results:
    name: "Publish Tests Results"
    needs: [ubuntu-build, macos-buid, windows-msvc-buid, android-build]
    runs-on: ubuntu-latest
    permissions:
      checks: write
      pull-requests: write
    if: always()

    steps:
    - name: Download Artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts
    - name: Publish Test Results
      uses: EnricoMi/publish-unit-test-result-action@v2
      with:
        files: "artifacts/**/*.xml"
