project(external C CXX)

if (NOT <PERSON>rlinux)



if (WIN32)
  SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-pragma-pack")
endif()
  SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-unused-but-set-variable")

  add_subdirectory(libusb-cmake)

if(UNIX AND NOT APPLE)
  message(STATUS "This is a Linux system")
  SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-implicit-function-declaration")
  SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-int-conversion")
  if(NOT ANDROID)
    add_compile_definitions(_GNU_SOURCE)
    set(HIDAPI_WITH_HIDRAW ON CACHE BOOL "Build with hidraw backend" FORCE)
    set(HIDAPI_WITH_LIBUSB OFF CACHE BOOL "Disable libusb backend" FORCE)
  endif()
endif()

  set(BUILD_SHARED_LIBS OFF CACHE BOOL "Build static library only" FORCE)

  add_subdirectory(hidapi)

if(NOT USE_FRAMEWORK)
    message(STATUS "USE_FRAMEWORK is OFF or not set")
    add_subdirectory(fmt)

    set(JSONCPP_WITH_TESTS OFF CACHE BOOL "Disable jsoncpp tests")
    set(JSONCPP_WITH_POST_BUILD_UNITTEST OFF CACHE BOOL "Disable jsoncpp post build unittest")

    add_subdirectory(jsoncpp)
    add_definitions(-DJSONCPP_ENABLE_STRING_VIEW)

    set_target_properties(jsoncpp_static PROPERTIES
      CXX_STANDARD 17
      CXX_STANDARD_REQUIRED YES
      CXX_EXTENSIONS NO
    )
endif()

endif()
