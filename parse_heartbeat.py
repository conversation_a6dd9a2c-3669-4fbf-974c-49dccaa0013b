#!/usr/bin/env python3
# encoding: utf-8

import sys
import os
import re
import pandas as pd
import matplotlib.pyplot as plt

# 命令行参数检查，支持默认参数
if len(sys.argv) == 1:
    txt_filename = "heartbeat.log"
    legend_y = "t4 - t1"
elif len(sys.argv) == 3:
    txt_filename = sys.argv[1]
    legend_y = sys.argv[2]
else:
    print("用法:")
    print(f"  {sys.argv[0]} <txt_filename> <legend_y>")
    print("或者不带参数，使用默认 heartbeat.log 和 t4 - t1")
    sys.exit(1)

basename = os.path.basename(txt_filename)

# 正则提取 t1, t2, t3, t4 四个大整数
pattern = re.compile(r"(\d{10,}),(\d{10,}),(\d{10,}),(\d{10,})")
t1_list, delta_list = [], []

# 读取 log 文件并提取数据
with open(txt_filename, "r") as f:
    for line in f:
        match = pattern.search(line)
        if match:
            t1, t2, t3, t4 = map(int, match.groups())
            t1_list.append(t1)
            delta_list.append(t4 - t1)

# 转换为 DataFrame
df = pd.DataFrame({
    "t1": t1_list,
    "delta": delta_list,
})

# 转换 t1 为秒级时间戳（更适合横轴可视化）
df["t1_sec"] = df["t1"] / 1e9

# 打印基本信息，mean 和 max 单位换成 ms（毫秒）
print("x (t1_sec):\n", df["t1_sec"].head())
print("y (t4 - t1) ns:\n", df["delta"].head())
print("====")
print(f"统计: mean={df['delta'].mean() / 1e6:.3f} ms, max={df['delta'].max() / 1e6:.3f} ms, count={len(df)}")

# 设置图像大小为 2011x304 像素
fig = plt.figure(figsize=(2011 / 100, 304 / 100), dpi=100)
ax = fig.add_subplot(111)

# 绘图
ax.plot(df["t1_sec"], df["delta"], label=legend_y)
ax.legend()
fig.suptitle(basename)
ax.set_xlabel('t1 timestamp (s)')
ax.set_ylabel(legend_y)

plt.tight_layout()
plt.show()
