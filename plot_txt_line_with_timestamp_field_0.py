import sys
import os
import pandas as pd
import matplotlib.pyplot as plt

print("usage:")
print(sys.argv[0], "txt_filename legend_y")
print("")
print("")
print("")
txt_filename = sys.argv[1]
legend_y = sys.argv[2]

# 读取txt文件
df = pd.read_csv(txt_filename, sep='\s+', header=None)

basename=os.path.basename(txt_filename)


x = df[0]
y = df[1]


print(x,y, "====")

#fig = plt.figure()
# 指定图形的宽度和高度（以像素为单位）
fig = plt.figure(figsize=(2011/100, 304/100), dpi=100)
ax = fig.add_subplot(111)

print("mean:", y.mean())
print("max:", y.max())


# 绘制直线
ax.plot(x, y, label=legend_y)
ax.legend()
fig.suptitle(basename)

ax.set_xlabel('timestamp_s')
ax.set_ylabel(legend_y)
plt.show()
