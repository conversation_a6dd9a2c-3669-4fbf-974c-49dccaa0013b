diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2cdb454..45ca8c3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -9,14 +9,14 @@ project(test
 # Define relative dir for ${CMAKE_SOURCE_DIR}
 set(PROJECT_SRC_DIR "test")
 
-option(USE_FRAMEWORK	"Use framework"	ON)
+option(USE_FRAMEWORK	"Use framework"	OFF)
 
 if(USE_FRAMEWORK)
     find_package(framework CONFIG REQUIRED)
 endif()
 
 find_package(gRPC CONFIG REQUIRED)
-
+find_package(perfetto REQUIRED)
 
 ######################################################################################
 ##                              Compiler											##
diff --git a/conanfile.py b/conanfile.py
index 90504f9..492f742 100644
--- a/conanfile.py
+++ b/conanfile.py
@@ -18,6 +18,7 @@ class Project(ConanFile):
         self.requires("grpc/1.72.1")
         #self.requires("flatbuffers/24.12.23")
         #self.requires("framework/feature-protobuf_5.27.0")
+        self.requires("perfetto/48.1", transitive_headers=True, transitive_libs=True)
 
 
     def build_requirements(self):
diff --git a/test/usb/CMakeLists.txt b/test/usb/CMakeLists.txt
index f4d2d7f..d62f83c 100644
--- a/test/usb/CMakeLists.txt
+++ b/test/usb/CMakeLists.txt
@@ -60,7 +60,8 @@ else()
     )
     list(APPEND LINK_ITEMS
         fmt::fmt
-        jsoncpp_static)
+        jsoncpp_static
+        perfetto::perfetto)
 endif()
 
 if (WIN32)
