// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: route_guide.proto
// Protobuf C++ Version: 5.27.0

#include "route_guide.pb.h"

#include <algorithm>
#include <type_traits>
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/generated_message_tctable_impl.h"
#include "google/protobuf/extension_set.h"
#include "google/protobuf/wire_format_lite.h"
#include "google/protobuf/descriptor.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/reflection_ops.h"
#include "google/protobuf/wire_format.h"
#include "absl/strings/internal/string_constant.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"
PROTOBUF_PRAGMA_INIT_SEG
namespace _pb = ::google::protobuf;
namespace _pbi = ::google::protobuf::internal;
namespace _fl = ::google::protobuf::internal::field_layout;
namespace routeguide {

inline constexpr Point::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : latitude_{0},
        longitude_{0},
        _cached_size_{0} {}

template <typename>
PROTOBUF_CONSTEXPR Point::Point(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct PointDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PointDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~PointDefaultTypeInternal() {}
  union {
    Point _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PointDefaultTypeInternal _Point_default_instance_;

inline constexpr Rectangle::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        lo_{nullptr},
        hi_{nullptr} {}

template <typename>
PROTOBUF_CONSTEXPR Rectangle::Rectangle(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct RectangleDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RectangleDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~RectangleDefaultTypeInternal() {}
  union {
    Rectangle _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RectangleDefaultTypeInternal _Rectangle_default_instance_;

inline constexpr Feature::Impl_::Impl_(
    ::_pbi::ConstantInitialized) noexcept
      : _cached_size_{0},
        name_(
            &::google::protobuf::internal::fixed_address_empty_string,
            ::_pbi::ConstantInitialized()),
        big_data_{},
        location_{nullptr} {}

template <typename>
PROTOBUF_CONSTEXPR Feature::Feature(::_pbi::ConstantInitialized)
    : _impl_(::_pbi::ConstantInitialized()) {}
struct FeatureDefaultTypeInternal {
  PROTOBUF_CONSTEXPR FeatureDefaultTypeInternal() : _instance(::_pbi::ConstantInitialized{}) {}
  ~FeatureDefaultTypeInternal() {}
  union {
    Feature _instance;
  };
};

PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT
    PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 FeatureDefaultTypeInternal _Feature_default_instance_;
}  // namespace routeguide
static constexpr const ::_pb::EnumDescriptor**
    file_level_enum_descriptors_route_5fguide_2eproto = nullptr;
static constexpr const ::_pb::ServiceDescriptor**
    file_level_service_descriptors_route_5fguide_2eproto = nullptr;
const ::uint32_t
    TableStruct_route_5fguide_2eproto::offsets[] ABSL_ATTRIBUTE_SECTION_VARIABLE(
        protodesc_cold) = {
        ~0u,  // no _has_bits_
        PROTOBUF_FIELD_OFFSET(::routeguide::Point, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::routeguide::Point, _impl_.latitude_),
        PROTOBUF_FIELD_OFFSET(::routeguide::Point, _impl_.longitude_),
        PROTOBUF_FIELD_OFFSET(::routeguide::Rectangle, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::routeguide::Rectangle, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::routeguide::Rectangle, _impl_.lo_),
        PROTOBUF_FIELD_OFFSET(::routeguide::Rectangle, _impl_.hi_),
        0,
        1,
        PROTOBUF_FIELD_OFFSET(::routeguide::Feature, _impl_._has_bits_),
        PROTOBUF_FIELD_OFFSET(::routeguide::Feature, _internal_metadata_),
        ~0u,  // no _extensions_
        ~0u,  // no _oneof_case_
        ~0u,  // no _weak_field_map_
        ~0u,  // no _inlined_string_donated_
        ~0u,  // no _split_
        ~0u,  // no sizeof(Split)
        PROTOBUF_FIELD_OFFSET(::routeguide::Feature, _impl_.name_),
        PROTOBUF_FIELD_OFFSET(::routeguide::Feature, _impl_.location_),
        PROTOBUF_FIELD_OFFSET(::routeguide::Feature, _impl_.big_data_),
        ~0u,
        0,
        ~0u,
};

static const ::_pbi::MigrationSchema
    schemas[] ABSL_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
        {0, -1, -1, sizeof(::routeguide::Point)},
        {10, 20, -1, sizeof(::routeguide::Rectangle)},
        {22, 33, -1, sizeof(::routeguide::Feature)},
};
static const ::_pb::Message* const file_default_instances[] = {
    &::routeguide::_Point_default_instance_._instance,
    &::routeguide::_Rectangle_default_instance_._instance,
    &::routeguide::_Feature_default_instance_._instance,
};
const char descriptor_table_protodef_route_5fguide_2eproto[] ABSL_ATTRIBUTE_SECTION_VARIABLE(
    protodesc_cold) = {
    "\n\021route_guide.proto\022\nrouteguide\",\n\005Point"
    "\022\020\n\010latitude\030\001 \001(\005\022\021\n\tlongitude\030\002 \001(\005\"I\n"
    "\tRectangle\022\035\n\002lo\030\001 \001(\0132\021.routeguide.Poin"
    "t\022\035\n\002hi\030\002 \001(\0132\021.routeguide.Point\"R\n\007Feat"
    "ure\022\014\n\004name\030\001 \001(\t\022#\n\010location\030\002 \001(\0132\021.ro"
    "uteguide.Point\022\024\n\010big_data\030\003 \001(\014B\002\010\0012L\n\n"
    "RouteGuide\022>\n\014ListFeatures\022\025.routeguide."
    "Rectangle\032\023.routeguide.Feature\"\0000\001B6\n\033io"
    ".grpc.examples.routeguideB\017RouteGuidePro"
    "toP\001\242\002\003RTGb\006proto3"
};
static ::absl::once_flag descriptor_table_route_5fguide_2eproto_once;
PROTOBUF_CONSTINIT const ::_pbi::DescriptorTable descriptor_table_route_5fguide_2eproto = {
    false,
    false,
    378,
    descriptor_table_protodef_route_5fguide_2eproto,
    "route_guide.proto",
    &descriptor_table_route_5fguide_2eproto_once,
    nullptr,
    0,
    3,
    schemas,
    file_default_instances,
    TableStruct_route_5fguide_2eproto::offsets,
    file_level_enum_descriptors_route_5fguide_2eproto,
    file_level_service_descriptors_route_5fguide_2eproto,
};
namespace routeguide {
// ===================================================================

class Point::_Internal {
 public:
};

Point::Point(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:routeguide.Point)
}
Point::Point(
    ::google::protobuf::Arena* arena, const Point& from)
    : Point(arena) {
  MergeFrom(from);
}
inline PROTOBUF_NDEBUG_INLINE Point::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void Point::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, latitude_),
           0,
           offsetof(Impl_, longitude_) -
               offsetof(Impl_, latitude_) +
               sizeof(Impl_::longitude_));
}
Point::~Point() {
  // @@protoc_insertion_point(destructor:routeguide.Point)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Point::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.~Impl_();
}

const ::google::protobuf::MessageLite::ClassData*
Point::GetClassData() const {
  PROTOBUF_CONSTINIT static const ::google::protobuf::MessageLite::
      ClassDataFull _data_ = {
          {
              &_table_.header,
              nullptr,  // OnDemandRegisterArenaDtor
              nullptr,  // IsInitialized
              PROTOBUF_FIELD_OFFSET(Point, _impl_._cached_size_),
              false,
          },
          &Point::MergeImpl,
          &Point::kDescriptorMethods,
          &descriptor_table_route_5fguide_2eproto,
          nullptr,  // tracker
      };
  ::google::protobuf::internal::PrefetchToLocalCache(&_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_data_.tc_table);
  return _data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 0, 0, 2> Point::_table_ = {
  {
    0,  // no _has_bits_
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    0,  // num_aux_entries
    offsetof(decltype(_table_), field_names),  // no aux_entries
    &_Point_default_instance_._instance,
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::routeguide::Point>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // int32 longitude = 2;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(Point, _impl_.longitude_), 63>(),
     {16, 63, 0, PROTOBUF_FIELD_OFFSET(Point, _impl_.longitude_)}},
    // int32 latitude = 1;
    {::_pbi::TcParser::SingularVarintNoZag1<::uint32_t, offsetof(Point, _impl_.latitude_), 63>(),
     {8, 63, 0, PROTOBUF_FIELD_OFFSET(Point, _impl_.latitude_)}},
  }}, {{
    65535, 65535
  }}, {{
    // int32 latitude = 1;
    {PROTOBUF_FIELD_OFFSET(Point, _impl_.latitude_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
    // int32 longitude = 2;
    {PROTOBUF_FIELD_OFFSET(Point, _impl_.longitude_), 0, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kInt32)},
  }},
  // no aux_entries
  {{
  }},
};

PROTOBUF_NOINLINE void Point::Clear() {
// @@protoc_insertion_point(message_clear_start:routeguide.Point)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.latitude_, 0, static_cast<::size_t>(
      reinterpret_cast<char*>(&_impl_.longitude_) -
      reinterpret_cast<char*>(&_impl_.latitude_)) + sizeof(_impl_.longitude_));
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

::uint8_t* Point::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:routeguide.Point)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // int32 latitude = 1;
  if (this->_internal_latitude() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<1>(
            stream, this->_internal_latitude(), target);
  }

  // int32 longitude = 2;
  if (this->_internal_longitude() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::
        WriteInt32ToArrayWithField<2>(
            stream, this->_internal_longitude(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:routeguide.Point)
  return target;
}

::size_t Point::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:routeguide.Point)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::_pbi::Prefetch5LinesFrom7Lines(reinterpret_cast<const void*>(this));
  // int32 latitude = 1;
  if (this->_internal_latitude() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_latitude());
  }

  // int32 longitude = 2;
  if (this->_internal_longitude() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(
        this->_internal_longitude());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}


void Point::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<Point*>(&to_msg);
  auto& from = static_cast<const Point&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:routeguide.Point)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_latitude() != 0) {
    _this->_impl_.latitude_ = from._impl_.latitude_;
  }
  if (from._internal_longitude() != 0) {
    _this->_impl_.longitude_ = from._impl_.longitude_;
  }
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Point::CopyFrom(const Point& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:routeguide.Point)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void Point::InternalSwap(Point* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Point, _impl_.longitude_)
      + sizeof(Point::_impl_.longitude_)
      - PROTOBUF_FIELD_OFFSET(Point, _impl_.latitude_)>(
          reinterpret_cast<char*>(&_impl_.latitude_),
          reinterpret_cast<char*>(&other->_impl_.latitude_));
}

::google::protobuf::Metadata Point::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class Rectangle::_Internal {
 public:
  using HasBits =
      decltype(std::declval<Rectangle>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(Rectangle, _impl_._has_bits_);
};

Rectangle::Rectangle(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);
  // @@protoc_insertion_point(arena_constructor:routeguide.Rectangle)
}
inline PROTOBUF_NDEBUG_INLINE Rectangle::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::routeguide::Rectangle& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0} {}

Rectangle::Rectangle(
    ::google::protobuf::Arena* arena,
    const Rectangle& from)
    : ::google::protobuf::Message(arena) {
  Rectangle* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.lo_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::routeguide::Point>(
                              arena, *from._impl_.lo_)
                        : nullptr;
  _impl_.hi_ = (cached_has_bits & 0x00000002u) ? ::google::protobuf::Message::CopyConstruct<::routeguide::Point>(
                              arena, *from._impl_.hi_)
                        : nullptr;

  // @@protoc_insertion_point(copy_constructor:routeguide.Rectangle)
}
inline PROTOBUF_NDEBUG_INLINE Rectangle::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0} {}

inline void Rectangle::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  ::memset(reinterpret_cast<char *>(&_impl_) +
               offsetof(Impl_, lo_),
           0,
           offsetof(Impl_, hi_) -
               offsetof(Impl_, lo_) +
               sizeof(Impl_::hi_));
}
Rectangle::~Rectangle() {
  // @@protoc_insertion_point(destructor:routeguide.Rectangle)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Rectangle::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  delete _impl_.lo_;
  delete _impl_.hi_;
  _impl_.~Impl_();
}

const ::google::protobuf::MessageLite::ClassData*
Rectangle::GetClassData() const {
  PROTOBUF_CONSTINIT static const ::google::protobuf::MessageLite::
      ClassDataFull _data_ = {
          {
              &_table_.header,
              nullptr,  // OnDemandRegisterArenaDtor
              nullptr,  // IsInitialized
              PROTOBUF_FIELD_OFFSET(Rectangle, _impl_._cached_size_),
              false,
          },
          &Rectangle::MergeImpl,
          &Rectangle::kDescriptorMethods,
          &descriptor_table_route_5fguide_2eproto,
          nullptr,  // tracker
      };
  ::google::protobuf::internal::PrefetchToLocalCache(&_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_data_.tc_table);
  return _data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<1, 2, 2, 0, 2> Rectangle::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(Rectangle, _impl_._has_bits_),
    0, // no _extensions_
    2, 8,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967292,  // skipmap
    offsetof(decltype(_table_), field_entries),
    2,  // num_field_entries
    2,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_Rectangle_default_instance_._instance,
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::routeguide::Rectangle>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    // .routeguide.Point hi = 2;
    {::_pbi::TcParser::FastMtS1,
     {18, 1, 1, PROTOBUF_FIELD_OFFSET(Rectangle, _impl_.hi_)}},
    // .routeguide.Point lo = 1;
    {::_pbi::TcParser::FastMtS1,
     {10, 0, 0, PROTOBUF_FIELD_OFFSET(Rectangle, _impl_.lo_)}},
  }}, {{
    65535, 65535
  }}, {{
    // .routeguide.Point lo = 1;
    {PROTOBUF_FIELD_OFFSET(Rectangle, _impl_.lo_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // .routeguide.Point hi = 2;
    {PROTOBUF_FIELD_OFFSET(Rectangle, _impl_.hi_), _Internal::kHasBitsOffset + 1, 1,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
  }}, {{
    {::_pbi::TcParser::GetTable<::routeguide::Point>()},
    {::_pbi::TcParser::GetTable<::routeguide::Point>()},
  }}, {{
  }},
};

PROTOBUF_NOINLINE void Rectangle::Clear() {
// @@protoc_insertion_point(message_clear_start:routeguide.Rectangle)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(_impl_.lo_ != nullptr);
      _impl_.lo_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(_impl_.hi_ != nullptr);
      _impl_.hi_->Clear();
    }
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

::uint8_t* Rectangle::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:routeguide.Rectangle)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  // .routeguide.Point lo = 1;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
        1, *_impl_.lo_, _impl_.lo_->GetCachedSize(), target, stream);
  }

  // .routeguide.Point hi = 2;
  if (cached_has_bits & 0x00000002u) {
    target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
        2, *_impl_.hi_, _impl_.hi_->GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:routeguide.Rectangle)
  return target;
}

::size_t Rectangle::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:routeguide.Rectangle)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::_pbi::Prefetch5LinesFrom7Lines(reinterpret_cast<const void*>(this));
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // .routeguide.Point lo = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size +=
          1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.lo_);
    }

    // .routeguide.Point hi = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size +=
          1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.hi_);
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}


void Rectangle::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<Rectangle*>(&to_msg);
  auto& from = static_cast<const Rectangle&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:routeguide.Rectangle)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      ABSL_DCHECK(from._impl_.lo_ != nullptr);
      if (_this->_impl_.lo_ == nullptr) {
        _this->_impl_.lo_ =
            ::google::protobuf::Message::CopyConstruct<::routeguide::Point>(arena, *from._impl_.lo_);
      } else {
        _this->_impl_.lo_->MergeFrom(*from._impl_.lo_);
      }
    }
    if (cached_has_bits & 0x00000002u) {
      ABSL_DCHECK(from._impl_.hi_ != nullptr);
      if (_this->_impl_.hi_ == nullptr) {
        _this->_impl_.hi_ =
            ::google::protobuf::Message::CopyConstruct<::routeguide::Point>(arena, *from._impl_.hi_);
      } else {
        _this->_impl_.hi_->MergeFrom(*from._impl_.hi_);
      }
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Rectangle::CopyFrom(const Rectangle& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:routeguide.Rectangle)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void Rectangle::InternalSwap(Rectangle* PROTOBUF_RESTRICT other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::google::protobuf::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Rectangle, _impl_.hi_)
      + sizeof(Rectangle::_impl_.hi_)
      - PROTOBUF_FIELD_OFFSET(Rectangle, _impl_.lo_)>(
          reinterpret_cast<char*>(&_impl_.lo_),
          reinterpret_cast<char*>(&other->_impl_.lo_));
}

::google::protobuf::Metadata Rectangle::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// ===================================================================

class Feature::_Internal {
 public:
  using HasBits =
      decltype(std::declval<Feature>()._impl_._has_bits_);
  static constexpr ::int32_t kHasBitsOffset =
      8 * PROTOBUF_FIELD_OFFSET(Feature, _impl_._has_bits_);
};

Feature::Feature(::google::protobuf::Arena* arena)
    : ::google::protobuf::Message(arena) {
  SharedCtor(arena);if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &Feature::ArenaDtor);
  }
  // @@protoc_insertion_point(arena_constructor:routeguide.Feature)
}
inline PROTOBUF_NDEBUG_INLINE Feature::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility, ::google::protobuf::Arena* arena,
    const Impl_& from, const ::routeguide::Feature& from_msg)
      : _has_bits_{from._has_bits_},
        _cached_size_{0},
        name_(arena, from.name_),
        big_data_{from.big_data_} {}

Feature::Feature(
    ::google::protobuf::Arena* arena,
    const Feature& from)
    : ::google::protobuf::Message(arena) {
  Feature* const _this = this;
  (void)_this;
  _internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(
      from._internal_metadata_);
  new (&_impl_) Impl_(internal_visibility(), arena, from._impl_, from);
  ::uint32_t cached_has_bits = _impl_._has_bits_[0];
  _impl_.location_ = (cached_has_bits & 0x00000001u) ? ::google::protobuf::Message::CopyConstruct<::routeguide::Point>(
                              arena, *from._impl_.location_)
                        : nullptr;
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &Feature::ArenaDtor);
  }

  // @@protoc_insertion_point(copy_constructor:routeguide.Feature)
}
inline PROTOBUF_NDEBUG_INLINE Feature::Impl_::Impl_(
    ::google::protobuf::internal::InternalVisibility visibility,
    ::google::protobuf::Arena* arena)
      : _cached_size_{0},
        name_(arena),
        big_data_{} {}

inline void Feature::SharedCtor(::_pb::Arena* arena) {
  new (&_impl_) Impl_(internal_visibility(), arena);
  _impl_.location_ = {};
}
Feature::~Feature() {
  // @@protoc_insertion_point(destructor:routeguide.Feature)
  _internal_metadata_.Delete<::google::protobuf::UnknownFieldSet>();
  SharedDtor();
}
inline void Feature::SharedDtor() {
  ABSL_DCHECK(GetArena() == nullptr);
  _impl_.name_.Destroy();
  delete _impl_.location_;
  _impl_.~Impl_();
}
void Feature::ArenaDtor(void* object) {
  Feature* _this = reinterpret_cast<Feature*>(object);
  _this->_impl_.big_data_. ::absl::Cord::~Cord ();
}

const ::google::protobuf::MessageLite::ClassData*
Feature::GetClassData() const {
  PROTOBUF_CONSTINIT static const ::google::protobuf::MessageLite::
      ClassDataFull _data_ = {
          {
              &_table_.header,
              nullptr,  // OnDemandRegisterArenaDtor
              nullptr,  // IsInitialized
              PROTOBUF_FIELD_OFFSET(Feature, _impl_._cached_size_),
              false,
          },
          &Feature::MergeImpl,
          &Feature::kDescriptorMethods,
          &descriptor_table_route_5fguide_2eproto,
          nullptr,  // tracker
      };
  ::google::protobuf::internal::PrefetchToLocalCache(&_data_);
  ::google::protobuf::internal::PrefetchToLocalCache(_data_.tc_table);
  return _data_.base();
}
PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1
const ::_pbi::TcParseTable<2, 3, 1, 31, 2> Feature::_table_ = {
  {
    PROTOBUF_FIELD_OFFSET(Feature, _impl_._has_bits_),
    0, // no _extensions_
    3, 24,  // max_field_number, fast_idx_mask
    offsetof(decltype(_table_), field_lookup_table),
    4294967288,  // skipmap
    offsetof(decltype(_table_), field_entries),
    3,  // num_field_entries
    1,  // num_aux_entries
    offsetof(decltype(_table_), aux_entries),
    &_Feature_default_instance_._instance,
    nullptr,  // post_loop_handler
    ::_pbi::TcParser::GenericFallback,  // fallback
    #ifdef PROTOBUF_PREFETCH_PARSE_TABLE
    ::_pbi::TcParser::GetTable<::routeguide::Feature>(),  // to_prefetch
    #endif  // PROTOBUF_PREFETCH_PARSE_TABLE
  }, {{
    {::_pbi::TcParser::MiniParse, {}},
    // string name = 1;
    {::_pbi::TcParser::FastUS1,
     {10, 63, 0, PROTOBUF_FIELD_OFFSET(Feature, _impl_.name_)}},
    // .routeguide.Point location = 2;
    {::_pbi::TcParser::FastMtS1,
     {18, 0, 0, PROTOBUF_FIELD_OFFSET(Feature, _impl_.location_)}},
    // bytes big_data = 3 [ctype = CORD];
    {::_pbi::TcParser::FastBcS1,
     {26, 63, 0, PROTOBUF_FIELD_OFFSET(Feature, _impl_.big_data_)}},
  }}, {{
    65535, 65535
  }}, {{
    // string name = 1;
    {PROTOBUF_FIELD_OFFSET(Feature, _impl_.name_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kUtf8String | ::_fl::kRepAString)},
    // .routeguide.Point location = 2;
    {PROTOBUF_FIELD_OFFSET(Feature, _impl_.location_), _Internal::kHasBitsOffset + 0, 0,
    (0 | ::_fl::kFcOptional | ::_fl::kMessage | ::_fl::kTvTable)},
    // bytes big_data = 3 [ctype = CORD];
    {PROTOBUF_FIELD_OFFSET(Feature, _impl_.big_data_), -1, 0,
    (0 | ::_fl::kFcSingular | ::_fl::kBytes | ::_fl::kRepCord)},
  }}, {{
    {::_pbi::TcParser::GetTable<::routeguide::Point>()},
  }}, {{
    "\22\4\0\0\0\0\0\0"
    "routeguide.Feature"
    "name"
  }},
};

PROTOBUF_NOINLINE void Feature::Clear() {
// @@protoc_insertion_point(message_clear_start:routeguide.Feature)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.name_.ClearToEmpty();
  _impl_.big_data_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(_impl_.location_ != nullptr);
    _impl_.location_->Clear();
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::google::protobuf::UnknownFieldSet>();
}

::uint8_t* Feature::_InternalSerialize(
    ::uint8_t* target,
    ::google::protobuf::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:routeguide.Feature)
  ::uint32_t cached_has_bits = 0;
  (void)cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    const std::string& _s = this->_internal_name();
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
        _s.data(), static_cast<int>(_s.length()), ::google::protobuf::internal::WireFormatLite::SERIALIZE, "routeguide.Feature.name");
    target = stream->WriteStringMaybeAliased(1, _s, target);
  }

  cached_has_bits = _impl_._has_bits_[0];
  // .routeguide.Point location = 2;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::InternalWriteMessage(
        2, *_impl_.location_, _impl_.location_->GetCachedSize(), target, stream);
  }

  // bytes big_data = 3 [ctype = CORD];
  if (!this->_internal_big_data().empty()) {
    target = stream->WriteBytes(3, this->_internal_big_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target =
        ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
            _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:routeguide.Feature)
  return target;
}

::size_t Feature::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:routeguide.Feature)
  ::size_t total_size = 0;

  ::uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::_pbi::Prefetch5LinesFrom7Lines(reinterpret_cast<const void*>(this));
  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 + ::google::protobuf::internal::WireFormatLite::StringSize(
                                    this->_internal_name());
  }

  // bytes big_data = 3 [ctype = CORD];
  if (!this->_internal_big_data().empty()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->_internal_big_data());
  }

  // .routeguide.Point location = 2;
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size +=
        1 + ::google::protobuf::internal::WireFormatLite::MessageSize(*_impl_.location_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}


void Feature::MergeImpl(::google::protobuf::MessageLite& to_msg, const ::google::protobuf::MessageLite& from_msg) {
  auto* const _this = static_cast<Feature*>(&to_msg);
  auto& from = static_cast<const Feature&>(from_msg);
  ::google::protobuf::Arena* arena = _this->GetArena();
  // @@protoc_insertion_point(class_specific_merge_from_start:routeguide.Feature)
  ABSL_DCHECK_NE(&from, _this);
  ::uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_big_data().empty()) {
    _this->_internal_set_big_data(from._internal_big_data());
  }
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    ABSL_DCHECK(from._impl_.location_ != nullptr);
    if (_this->_impl_.location_ == nullptr) {
      _this->_impl_.location_ =
          ::google::protobuf::Message::CopyConstruct<::routeguide::Point>(arena, *from._impl_.location_);
    } else {
      _this->_impl_.location_->MergeFrom(*from._impl_.location_);
    }
  }
  _this->_impl_._has_bits_[0] |= cached_has_bits;
  _this->_internal_metadata_.MergeFrom<::google::protobuf::UnknownFieldSet>(from._internal_metadata_);
}

void Feature::CopyFrom(const Feature& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:routeguide.Feature)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}


void Feature::InternalSwap(Feature* PROTOBUF_RESTRICT other) {
  using std::swap;
  auto* arena = GetArena();
  ABSL_DCHECK_EQ(arena, other->GetArena());
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::_pbi::ArenaStringPtr::InternalSwap(&_impl_.name_, &other->_impl_.name_, arena);
  _impl_.big_data_.swap(other->_impl_.big_data_);
  swap(_impl_.location_, other->_impl_.location_);
}

::google::protobuf::Metadata Feature::GetMetadata() const {
  return ::google::protobuf::Message::GetMetadataImpl(GetClassData()->full());
}
// @@protoc_insertion_point(namespace_scope)
}  // namespace routeguide
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google
// @@protoc_insertion_point(global_scope)
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::std::false_type
    _static_init2_ PROTOBUF_UNUSED =
        (::_pbi::AddDescriptors(&descriptor_table_route_5fguide_2eproto),
         ::std::false_type{});
#include "google/protobuf/port_undef.inc"
