from conan import ConanFile

class Project(ConanFile):

    # all project are the same:
    python_requires = "project_base/1.0"
    python_requires_extend = "project_base.ProjectBase"

    def init(self):
        base = self.python_requires["project_base"].module.ProjectBase
        self.settings = base.settings
        self.options.update(base.options, base.default_options)
        self.revision_mode = base.revision_mode

    def requirements(self):
        self.requires("grpc/1.72.1")
        #self.requires("flatbuffers/24.12.23")
        #self.requires("framework/feature-protobuf_5.27.0")
        #self.requires("framework/jenkins")
        self.requires(super().override_require("framework/develop"), run=True)

    def build_requirements(self):
        self.build_requires("protobuf/5.27.0")
        self.build_requires("grpc/1.72.1")
        self.build_requires("flatbuffers/24.12.23")


    # def package_info(self):
