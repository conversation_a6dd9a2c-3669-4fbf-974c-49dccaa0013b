namespace stream_demo;

table StartImuRequest {
  session_id: string;
}

table ImuData {
  hmd_time_nanos_system: ulong;
  hmd_time_nanos_device: ulong;
  hmd_time_nanos_sensor: ulong;
  data_mask: int;
  gyroscope_x: float;
  gyroscope_y: float;
  gyroscope_z: float;
  accelerometer_x: float;
  accelerometer_y: float;
  accelerometer_z: float;
  magnetometer_x: float;
  magnetometer_y: float;
  magnetometer_z: float;
  temperature: float;
  imu_id: int;
  frame_id: uint;
  gyroscope_numerator: int;
  accelerometer_numerator: int;
  magnetometer_numerator: int;
  out_numerator_mask: uint;
}

table StopImuRequest {
  session_id: string;
}

table StopImuResponse {
  session_id: string;
  result: int;
}

rpc_service ImuStreamService {
  StartAndSubmit(StartImuRequest): ImuData (streaming: "server");
  Stop(StopImuRequest): StopImuResponse;
}
