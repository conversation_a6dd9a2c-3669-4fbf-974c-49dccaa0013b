#include <grpcpp/grpcpp.h>
#include <grpcpp/alarm.h>
#include <flatbuffers/grpc.h>
#include "stream_demo.grpc.fb.h"
#include "grpc_common.h"

#include <framework/util/fileutil.h>

#include <string>
#include <iostream>
#include <thread>
#include <mutex>
#include <chrono>
#include <time.h>
#include <signal.h>
#include <iostream>
#include <functional>
#include <condition_variable>


// using stream_demo::ImuStreamService;
// using stream_demo::StartImuRequest;
// using stream_demo::ImuData;

// using stream_demo::StopImuRequest;
// using stream_demo::StopImuResponse;

using namespace stream_demo;

using namespace framework::util;

enum {
    SUBMIT_DATA_IMU = 1<<0,
    SUBMIT_DATA_VSYNC = 1<<1,
    SUBMIT_DATA_GRAYSCALE_CAMERA = 1<<2,
    SUBMIT_DATA_RGB_CAMERA = 1 << 3,
    SUBMIT_DATA_AMBIENT_LIGHT = 1 << 4,

    SUBMIT_DATA_IMU_FRAMEWORK = 1<<5,
    SUBMIT_DATA_VSYNC_FRAMEWORK = 1<<6,
    SUBMIT_DATA_GRAYSCALE_CAMERA_FRAMEWORK = 1<<7,
    SUBMIT_DATA_RGB_CAMERA_FRAMEWORK = 1 << 8,
    SUBMIT_DATA_AMBIENT_LIGHT_FRAMEWORK = 1 << 9,
};

static bool s_running = true;
static int32_t s_test_data = 0;
static int32_t s_param1 = 0;


class ImuAsyncStreamClient {
public:
    ImuAsyncStreamClient(std::shared_ptr<grpc::Channel> channel)
            : stub_(stream_demo::ImuStreamService::NewStub(channel)),
              cq_(),  // CompletionQueue
              shutdown_(false) {
        // 启动 CQ 驱动线程
        cq_thread_ = std::thread(&ImuAsyncStreamClient::ProcessCQ, this);
    }

    ~ImuAsyncStreamClient() {
        // 通知退出
        // 1. 发起取消流
        ctx_stream_.TryCancel();
        ctx_stop_.TryCancel();

        // 2. 延迟 Shutdown，等 gRPC 完成最后的事件
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 3. 安全关闭 CQ
        shutdown_ = true;
        cq_.Shutdown();
        cq_thread_.join();
    }

    // 发起 StartAndSubmit 流式调用
    void StartStream(const std::string& session_id) {
        flatbuffers::grpc::MessageBuilder builder;

        // 创建 FlatBuffer 字段
        auto session_id_offset = builder.CreateString(session_id);
        
        // 构建请求对象
        auto request_offset = stream_demo::CreateStartImuRequest(builder, session_id_offset);
        
        // 完成构建
        builder.Finish(request_offset);
        
        // 构造用于发送的 gRPC 消息
        auto request_msg = builder.ReleaseMessage<stream_demo::StartImuRequest>();

        stream_reader_ = stub_->AsyncStartAndSubmit(&ctx_stream_, request_msg, &cq_, reinterpret_cast<void*>(kTagStreamStart));
    }

    // 发起 Stop 调用
    void Stop(const std::string& session_id) {

        flatbuffers::grpc::MessageBuilder builder;

        // 创建 FlatBuffer 字段
        auto session_id_offset = builder.CreateString(session_id);
        
        // 构建请求对象
        auto request_offset = stream_demo::CreateStopImuRequest(builder, session_id_offset);
        
        // 完成构建
        builder.Finish(request_offset);
        
        // 构造用于发送的 gRPC 消息
        auto request_msg = builder.ReleaseMessage<stream_demo::StopImuRequest>();

        stop_reader_ = stub_->AsyncStop(&ctx_stop_, request_msg, &cq_);

        // 发起 Finish 操作
        stop_reader_->Finish(&stop_response_, &stop_status_, reinterpret_cast<void*>(kTagStopFinish));
    }

private:
    // 标记不同事件的 tag 枚举
    enum Tag : int64_t {
        kTagStreamStart = 1,
        kTagStreamRead  = 2,
        kTagStreamFinish= 3,
        kTagStopFinish  = 4
    };

    std::unique_ptr<stream_demo::ImuStreamService::Stub> stub_;
    grpc::CompletionQueue cq_;
    std::thread cq_thread_;

    // 流式 RPC 相关
    grpc::ClientContext ctx_stream_;
    std::unique_ptr<grpc::ClientAsyncReader<flatbuffers::grpc::Message<ImuData>>> stream_reader_;
    flatbuffers::grpc::Message<ImuData> stream_event_;

    // Stop RPC 相关
    grpc::ClientContext ctx_stop_;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< flatbuffers::grpc::Message<StopImuResponse>>> stop_reader_;
    flatbuffers::grpc::Message<StopImuResponse> stop_response_;

    std::atomic<bool> shutdown_;

    // CompletionQueue 驱动循环
    void ProcessCQ() {
        void* tag;
        bool ok;
        while (s_running && cq_.Next(&tag, &ok)) {
            if (shutdown_) break;
            // 区分不同 tag
            switch ((int64_t)tag) {
                case kTagStreamStart:
                    // 流式调用已启动，继续等待 Read 完成
                    LOG_INFO("[Imu Client] Stream started");
                    stream_reader_->Read(&stream_event_, reinterpret_cast<void*>(kTagStreamRead));
                    break;
                case kTagStreamRead:
                    if (ok) {
                        // 继续读取下一条
                        if (auto imu = stream_event_.GetRoot()) {
                            std::cout << "Received IMU frame_id: " << imu->frame_id() << "\n";
                        }
                        stream_reader_->Read(&stream_event_, (void*)kTagStreamRead);
                    } else {
                        // 流结束，发起 Finish
                        stream_reader_->Finish(&stream_status_, (void*)kTagStreamFinish);
                    }
                    break;
                case kTagStreamFinish:
                    if (stream_status_.ok()) {
                        LOG_INFO("[Imu Client] Stream finished cleanly");
                    } else {
                        LOG_INFO("[Imu Client] Stream failed: {}", stream_status_.error_message());
                        s_running = false;
                    }
                    break;
                case kTagStopFinish:
                    if (stop_status_.ok()) {
                        if (auto imu_stop_response = stop_response_.GetRoot()) {
                            std::string session_id_str = imu_stop_response->session_id()->str();
                            LOG_INFO("[Imu Client] StopResponse: session_id={}, result={}", session_id_str, imu_stop_response->result());
                        }                        
                    } else {
                        LOG_INFO("[Imu Client] Stop failed: {}", stop_status_.error_message());
                        s_running = false;
                    }
                    shutdown_ = true;
                    break;
                default:
                    LOG_ERROR("[Imu Client] Unknown tag, {}", (void*)tag);
                    break;
            }
        }
    }

    grpc::Status stream_status_;
    grpc::Status stop_status_;
};

void signal_handler(int signum) {
    std::cout << "Ctrl+C received. Exiting..." << std::endl;
    s_running = false;
}

int main(int argc, char* argv[]) {
	Logger::defaultLogger()->set_level(LogLevel::trace);

    signal(SIGINT, signal_handler);

    s_test_data = 1; //imu
    s_param1 = 1000; //1000fps

    if (s_test_data & SUBMIT_DATA_IMU) {
        std::shared_ptr<grpc::Channel> channel;
        channel = grpc::CreateChannel("localhost:50051", grpc::InsecureChannelCredentials());
        ImuAsyncStreamClient client(channel);
        // 1. 发起流式 StartAndSubmit
        client.StartStream("session-imu");
        // 2. 主线程可以做其他事，随后在需要时调用 Stop
        while (s_running) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        client.Stop("session-imu");
        // 3. 退出时析构 client，自动关闭 CompletionQueue
    }
    return 0;
}

