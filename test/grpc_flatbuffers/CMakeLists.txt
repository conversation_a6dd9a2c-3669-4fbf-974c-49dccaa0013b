# 查找 Protobuf 与 gRPC（假设已通过包管理安装或编译安装）
find_package(Protobuf CONFIG REQUIRED)
find_package(gRPC CONFIG REQUIRED)
find_package(FlatBuffers REQUIRED)

# Proto 文件路径
set(FBS_FILE ${CMAKE_CURRENT_SOURCE_DIR}/stream_demo.fbs)

# 生成的源文件路径
set(GEN_CPP_DIR ${CMAKE_CURRENT_BINARY_DIR})

set(FBS_HDR       ${GEN_CPP_DIR}/stream_demo_generated.h)
set(GRPC_SRC      ${GEN_CPP_DIR}/stream_demo.grpc.fb.cc)
set(GRPC_HDR      ${GEN_CPP_DIR}/stream_demo.grpc.fb.h)

# 自定义命令：调用 flac 生成代码
add_custom_command(
        OUTPUT ${FBS_HDR} ${GRPC_SRC} ${GRPC_HDR}
        COMMAND ${FLATBUFFERS_FLATC_EXECUTABLE}
        ARGS -c --grpc --cpp --gen-object-api --filename-suffix "_generated" -o ${GEN_CPP_DIR} ${FBS_FILE}
        DEPENDS ${FBS_FILE}
        COMMENT "Generating FlatBuffers and gRPC sources"
)

add_library( stream_base INTERFACE )
target_include_directories( stream_base
        INTERFACE
        "$<BUILD_INTERFACE:${GEN_CPP_DIR}>"
)
target_link_libraries( stream_base
        INTERFACE
        framework::framework
        gRPC::grpc++
        flatbuffers::flatbuffers
        protobuf::libprotobuf
        gRPC::grpc++_reflection
        pthread
)

if(FALSE)
add_executable(fb_stream_server
        fb_server.cc
        ${GRPC_SRC}
)
target_link_libraries(fb_stream_server
        stream_base
)
add_executable(fb_stream_server_all_symbols
        fb_server.cc
        ${GRPC_SRC}
)
target_link_libraries(fb_stream_server_all_symbols
        stream_base
)
PROJECT_STRIP_LINK_OPTIONS(fb_stream_server fb_stream_server_all_symbols "symbol_type_null" FALSE)


add_executable(fb_stream_client
        fb_client.cc
        ${GRPC_SRC}
)
target_link_libraries(fb_stream_client
        stream_base
)

endif()

if(TRUE)
add_executable(cq_fb_stream_client
        cq_fb_stream_client_async.cc
        ${GRPC_SRC}
)
target_link_libraries(cq_fb_stream_client
        stream_base
)
endif()

if(TRUE)
add_executable(cq_fb_stream_server
        cq_fb_stream_server_async.cc
        ${GRPC_SRC}
)
target_link_libraries(cq_fb_stream_server
        stream_base
)
endif()

