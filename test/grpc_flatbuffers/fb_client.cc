#include <grpcpp/grpcpp.h>
#include <grpcpp/alarm.h>
#include <flatbuffers/grpc.h>
#include "stream_demo.grpc.fb.h"
#include "grpc_common.h"

using stream_demo::ImuStreamService;
using stream_demo::StartImuRequest;
using stream_demo::ImuData;

class AsyncClient {
public:
    AsyncClient(std::shared_ptr<grpc::Channel> channel, const std::string& session_id)
        : stub_(ImuStreamService::NewStub(channel)), session_id_(session_id) {}


    using REQUEST_TYPE = flatbuffers::grpc::Message<StartImuRequest>;
    using RESPONSE_TYPE = flatbuffers::grpc::Message<ImuData>;

    void StartStream() {
        flatbuffers::grpc::MessageBuilder builder;

        // 创建 FlatBuffer 字段
        auto session_id_offset = builder.CreateString(session_id_);
        
        // 构建请求对象
        auto request_offset = stream_demo::CreateStartImuRequest(builder, session_id_offset);
        
        // 完成构建
        builder.Finish(request_offset);
        
        // 构造用于发送的 gRPC 消息
        auto request_msg = builder.ReleaseMessage<stream_demo::StartImuRequest>();

        stream_ = stub_->AsyncStartAndSubmit(&ctx_, request_msg, &cq_, reinterpret_cast<void*>(Tag::START));
    }

    void RunEventLoop() {
        void* tag;
        bool ok;

        while (cq_.Next(&tag, &ok)) {
            if (!ok) {
                std::cout << "Stream closed or error.\n";
                break;
            }

            switch (static_cast<TagType>(reinterpret_cast<intptr_t>(tag))) {
                case Tag::START:
                    std::cout << "Stream started.\n";
                    stream_->Read(&response_, reinterpret_cast<void*>(Tag::READ));
                    break;

                case Tag::READ:
                    if (auto imu = response_.GetRoot()) {
                        std::cout << "Received IMU frame_id: " << imu->frame_id() << "\n";
                    }
                    stream_->Read(&response_, reinterpret_cast<void*>(Tag::READ));
                    break;

                case Tag::FINISH:
                    std::cout << "Finished: " << status_.error_message() << "\n";
                    return;
            }
        }
    }

    void FinishStream() {
        stream_->Finish(&status_, reinterpret_cast<void*>(Tag::FINISH));
    }

private:
    enum class Tag : intptr_t { START = 1, READ = 2, FINISH = 3 };
    using TagType = Tag;

    grpc::ClientContext ctx_;
    grpc::CompletionQueue cq_;
    flatbuffers::grpc::Message<StartImuRequest> request_;
    flatbuffers::grpc::Message<ImuData> response_;
    grpc::Status status_;
    std::unique_ptr<grpc::ClientAsyncReader<flatbuffers::grpc::Message<ImuData>>> stream_;
    std::unique_ptr<ImuStreamService::Stub> stub_;

    std::string session_id_;

};

int main() {
    AsyncClient client(grpc::CreateChannel("localhost:50051", grpc::InsecureChannelCredentials()),"session_001");
    client.StartStream();

    client.RunEventLoop();  // 阻塞直到流结束
    return 0;
}