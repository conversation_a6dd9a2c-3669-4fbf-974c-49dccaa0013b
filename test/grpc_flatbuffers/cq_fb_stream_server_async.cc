#include <grpcpp/grpcpp.h>
#include <grpcpp/server.h>
#include <grpcpp/server_builder.h>
#include <grpcpp/server_context.h>
#include <grpcpp/alarm.h>
#include <flatbuffers/grpc.h>
#include "stream_demo.grpc.fb.h"

#include <framework/util/os_time.h>
#include "grpc_common.h"

#include <string>
#include <list>
#include <iostream>
#include <thread>
#include <chrono>
#include <time.h>
#include <signal.h>
#include <iostream>
#include <functional>
#include <queue>

using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::Status;
using grpc::ServerWriter;
using grpc::ServerAsyncWriter;
using grpc::ServerCompletionQueue;

// using stream_demo::ImuStreamService;
// using stream_demo::StartImuRequest;
// using stream_demo::ImuData;

// using stream_demo::StopImuRequest;
// using stream_demo::StopImuResponse;


using namespace stream_demo;

enum {
    SUBMIT_DATA_IMU = 1<<0,
    SUBMIT_DATA_VSYNC = 1<<1,
    SUBMIT_DATA_GRAYSCALE_CAMERA = 1<<2,
    SUBMIT_DATA_RGB_CAMERA = 1 << 3,
    SUBMIT_DATA_AMBIENT_LIGHT = 1 << 4,


    SUBMIT_DATA_IMU_FRAMEWORK = 1<<5,
    SUBMIT_DATA_VSYNC_FRAMEWORK = 1<<6,
    SUBMIT_DATA_GRAYSCALE_CAMERA_FRAMEWORK = 1<<7,
    SUBMIT_DATA_RGB_CAMERA_FRAMEWORK = 1 << 8,
    SUBMIT_DATA_AMBIENT_LIGHT_FRAMEWORK = 1 << 9,
};

static bool s_running = true;
static int32_t s_test_data = 0;
static int32_t s_param1 = 0;
static int32_t s_param2 = 0;
static int32_t s_param3 = 0;

class CallDataBase {
public:
    virtual ~CallDataBase() = default;
    // 所有派生类都要实现这个方法来处理 CompletionQueue 的事件
    virtual void HandleEvent(bool ok) = 0;
};

template <typename ASYNC_SERVICE,
          typename REQUEST,
          typename RESPONSE>
using RequestStartFunc = void (ASYNC_SERVICE::*)(
    grpc::ServerContext*,
    flatbuffers::grpc::Message<REQUEST>*,
    grpc::ServerAsyncWriter<flatbuffers::grpc::Message<RESPONSE>>*,
    grpc::CompletionQueue*,
    grpc::ServerCompletionQueue*,
    void*);
template<class ASYNC_SERVICE, class REQUEST, class RESPONSE, class RAW_DATA, class DerivedType, RequestStartFunc<ASYNC_SERVICE, REQUEST, RESPONSE> StartFunc>
class StreamCallData : public CallDataBase {
public:
    StreamCallData(ASYNC_SERVICE* service,
                 ServerCompletionQueue* cq,
                 std::list<std::shared_ptr<DerivedType>>& calls,
                 std::mutex& calls_mu)
            : service_(service)
            , cq_(cq)
            , responder_(&ctx_)
            , status_(CREATE)
            , write_in_flight_(false)
            , calls_(calls)
            , calls_mu_(calls_mu) {
        Proceed(true);
    }

public:
    virtual void HandleEvent(bool ok) {
        LOG_TRACE("StreamCallData::HandleEvent, status={}, ok={}", status_, ok);
        Proceed(ok);
    }

    virtual std::shared_ptr<DerivedType> GetDerivedClassSharedPtr() = 0;

    void EnqueueMessage(const RAW_DATA& data) {
        if (status_ != PROCESS) {
            return;
        }

        LOG_TRACE("StreamCallData::EnqueueMessage");
        {
            std::lock_guard<std::mutex> lock(mu_);
            message_queue_.push(std::move(data));
        }
        TryWrite();
    }
protected:
    virtual void ConstructEvent(flatbuffers::grpc::Message<RESPONSE>& data, const RAW_DATA & frame_data) = 0;

private:
    void TryWrite() {
        RAW_DATA msg;
        {
            std::lock_guard<std::mutex> lk(mu_);
            LOG_TRACE("StreamCallData::TryWrite, message_queue_size:{}", message_queue_.size());
            if (write_in_flight_ || message_queue_.empty() || status_ != PROCESS) return;
            msg = std::move(message_queue_.front());
            message_queue_.pop();
            write_in_flight_ = true;
        }

        flatbuffers::grpc::Message<RESPONSE> response;
        ConstructEvent(response, msg);
        responder_.Write(response, this);
    }

    void Finish() {
        LOG_INFO("FbImuCallData::Finish, status={}", status_);
        {
            std::lock_guard<std::mutex> lk(calls_mu_);
            auto self = GetDerivedClassSharedPtr();
            calls_.remove_if([self](auto& p){ return p.get()==self.get(); });
        }
        //responder_.Finish(Status::OK, this);
    }

    void Proceed(bool ok) {
        LOG_TRACE("StreamCallData::Proceed, status={}, ok={}", status_, ok);

        if (!ok) {
            // 客户端断开或错误
            Finish();
            return;
        }

        if (status_ == CREATE) {
            // 第一次调用，注册新的 StartAndSubmit RPC
            status_ = INIT;
            (service_->*StartFunc)(&ctx_, &request_, &responder_, cq_, cq_, this);
        } else if (status_ == INIT) {
            auto new_call = std::make_shared<DerivedType>(service_, cq_, calls_, calls_mu_);
            LOG_INFO("StreamCallData::Proceed, new CallData {}", (void*)new_call.get());

            // 2) 将自己加入活跃列表
            {
                LOG_INFO("StreamCallData::Proceed, push call data");
                std::lock_guard<std::mutex> lk(calls_mu_);
                calls_.push_back(GetDerivedClassSharedPtr());
            }
            status_ = PROCESS;
            //TryWrite();
        } else if (status_ == PROCESS) {
            // 一帧写入完成
            {
                std::lock_guard<std::mutex> lk(mu_);
                write_in_flight_ = false;
            }
            TryWrite();
        } else {
            // FINISH 阶段: 写完成或客户端断开
            status_ = FINISH;
            Finish();
        }
    }

private:
    enum CallStatus { CREATE, INIT, PROCESS, FINISH };
    ASYNC_SERVICE * service_;
    ServerCompletionQueue* cq_;
    ServerContext ctx_;
    flatbuffers::grpc::Message<REQUEST> request_;
    ServerAsyncWriter<flatbuffers::grpc::Message<RESPONSE>> responder_;
    CallStatus status_;
    std::queue<RAW_DATA> message_queue_;
    bool write_in_flight_;
    std::mutex mu_;
    std::list<std::shared_ptr<DerivedType>>& calls_;
    std::mutex& calls_mu_;
};


class ImuCallData;
using ImuResponseType = flatbuffers::grpc::Message<ImuData>;
using ImuCallDataBase = StreamCallData<ImuStreamService::AsyncService,StartImuRequest,ImuData,NRImuData,ImuCallData,&ImuStreamService::AsyncService::RequestStartAndSubmit>;
class ImuCallData : public ImuCallDataBase, public std::enable_shared_from_this<ImuCallData> {
public:
    using ImuCallDataBase::ImuCallDataBase;

    std::shared_ptr<ImuCallData> GetDerivedClassSharedPtr() override {
        return shared_from_this(); 
    }

    void ConstructEvent(ImuResponseType& response, const NRImuData& frame_data) override {
        flatbuffers::grpc::MessageBuilder builder;
        auto imu_offset = stream_demo::CreateImuData(builder,
            frame_data.hmd_time_nanos_system,
            frame_data.hmd_time_nanos_device,
            frame_data.hmd_time_nanos_sensor,
            frame_data.data_mask,
            frame_data.gyroscope.x,
            frame_data.gyroscope.y,
            frame_data.gyroscope.z,
            frame_data.accelerometer.x,
            frame_data.accelerometer.y,
            frame_data.accelerometer.z,
            frame_data.magnetometer.x,
            frame_data.magnetometer.y,
            frame_data.magnetometer.z,
            frame_data.temperature,
            frame_data.imu_id,
            frame_data.frame_id,
            frame_data.gyroscope_numerator,
            frame_data.accelerometer_numerator,
            frame_data.magnetometer_numerator,
            frame_data.out_numerator_mask
        );
        builder.Finish(imu_offset);
        response = builder.ReleaseMessage<ImuData>();
    }
};

class StopImuCallData : public CallDataBase {
public:
    StopImuCallData(ImuStreamService::AsyncService* service, ServerCompletionQueue* cq)
            : service_(service), cq_(cq), responder_(&ctx_), status_(CREATE) {
        LOG_INFO("StopImuCallData::StopImuCallData")
        Proceed();
    }

public:
    // CompletionQueue 回调入口
    void HandleEvent(bool ok) override {
        if (!ok) {
            // 遇到错误也进入 FINISH
            status_ = FINISH;
            Proceed();
            return;
        }
        Proceed();
    }

private:
    void Proceed() {
        if (status_ == CREATE) {
            // 第一次：注册对 Stop RPC 的监听
            status_ = PROCESS;
            service_->RequestStop(&ctx_, &request_, &responder_, cq_, cq_, this);
        } else if (status_ == PROCESS) {
            // 客户端调用了 Stop，request_ 已填充
            flatbuffers::grpc::MessageBuilder builder;

            std::string session_id_str = request_.GetRoot()->session_id()->str();
            auto session_id_offset = builder.CreateString(session_id_str);
            auto response_offset = stream_demo::CreateStopImuResponse(builder,session_id_offset,0);
            builder.Finish(response_offset);
            response_ = builder.ReleaseMessage<StopImuResponse>();

            // 发送响应并进入 FINISH
            status_ = FINISH;
            responder_.Finish(response_, grpc::Status::OK, this);
        } else {
            // FINISH：完成后自删，释放资源
           // delete this; //加上此行会crash，double free
        }
    }

private:
    enum CallStatus { CREATE, PROCESS, FINISH };
    ImuStreamService::AsyncService* service_;
    ServerCompletionQueue* cq_;
    ServerContext ctx_;

    flatbuffers::grpc::Message<StopImuRequest> request_;
    flatbuffers::grpc::Message<StopImuResponse> response_;

    grpc::ServerAsyncResponseWriter<flatbuffers::grpc::Message<StopImuResponse>> responder_;
    CallStatus status_;
};

void ImuRunRpcLoop(ImuStreamService::AsyncService* service, ServerCompletionQueue* cq, std::list<std::shared_ptr<ImuCallData>>& calls, std::mutex& calls_mu) {
    LOG_INFO("ImuRunRpcLoop, Start");
    auto call = std::make_shared<ImuCallData>(service, cq, calls, calls_mu);  // 预注册第一个 RPC
    LOG_INFO("ImuRunRpcLoop, call={}", (void*)call.get());
    auto stop_call = std::make_shared<StopImuCallData>(service, cq);
    LOG_INFO("ImuRunRpcLoop, stop_call={}", (void*)stop_call.get());
    void* tag; bool ok;
    while (cq->Next(&tag, &ok)) {
        static_cast<CallDataBase*>(tag)->HandleEvent(ok);
    }
    LOG_INFO("ImuRunRpcLoop, Exit");
}

class TestImu {
public:
    TestImu() {
        std::string server_addr("0.0.0.0:50051");
        ServerBuilder builder;
        builder.AddListeningPort(server_addr, grpc::InsecureServerCredentials());
        builder.RegisterService(&service_);

        completion_queue_ = builder.AddCompletionQueue();

        server_ = builder.BuildAndStart();
        LOG_INFO("Imu Async server listening on {}", server_addr);

        // 启动 RPC 驱动线程
        rpc_thread_ = std::thread(ImuRunRpcLoop, &service_, completion_queue_.get(), std::ref(calls_), std::ref(calls_mu_));
    }

    virtual ~TestImu() {
        Shutdown();
    }

    void Update() {
        while (s_running) {
            if (s_param1 > 0) {
                uint64_t begin_time = FMonotonicGetNs();
                SendEvent();
                uint64_t end_time = FMonotonicGetNs();
                int64_t delta = (int64_t)(1000*1000*1000)/s_param1-(int64_t)(end_time-begin_time);
                if (delta > 0) {
                    std::this_thread::sleep_for(std::chrono::nanoseconds(delta));
                } else {
                    LOG_ERROR("TestImu::Update, delta={}, begin={}, end={}", delta, begin_time, end_time);
                }
            } else {
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            }
        }
    }

    void Shutdown() {
        LOG_INFO("TestImu::Shutdown");
        if (server_) {
            server_->Shutdown(); // 触发 Wait() 返回
        }
        completion_queue_->Shutdown();
        rpc_thread_.join();
    }

    void SendEvent() {
        ::NRImuData data;
        memset(&data, 0, sizeof(data));
        static int32_t s_frame_id = 0;
        data.frame_id = (s_param3 > 0) ? 1 : ++s_frame_id;
        data.imu_id = 2;

        //LOG_INFO("TestImu::SendEvent， frame_id={}", data.frame_id);
        std::lock_guard<std::mutex> lk(calls_mu_);
        for (auto& call : calls_) {
            call->EnqueueMessage(data);
        }
    }

private:
    ImuStreamService::AsyncService service_;
    std::unique_ptr<Server> server_;
    std::unique_ptr<grpc::ServerCompletionQueue> completion_queue_;
    // 存储所有活跃的流
    std::list<std::shared_ptr<ImuCallData>> calls_;
    std::mutex calls_mu_;
    std::thread rpc_thread_;
};


void signal_handler(int signum) {
    std::cout << "Ctrl+C received. Exiting..." << std::endl;
    s_running = false;
}


int main(int argc, char* argv[]) {
	Logger::defaultLogger()->set_level(LogLevel::trace);
    signal(SIGINT, signal_handler);
    s_test_data = 1; //imu
    s_param1 = 1000; //1000fps
    if (s_test_data & SUBMIT_DATA_IMU)
    {
        static TestImu testInstance;
        testInstance.Update();
    }
    return 0;
}
