#include <grpcpp/grpcpp.h>
#include <grpcpp/server.h>
#include <grpcpp/server_builder.h>
#include <grpcpp/server_context.h>
#include <grpcpp/alarm.h>
#include <flatbuffers/grpc.h>
#include "stream_demo.grpc.fb.h"

using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerCompletionQueue;
using grpc::ServerAsyncWriter;
using grpc::ServerContext;
using grpc::Status;

using stream_demo::ImuStreamService;
using stream_demo::StartImuRequest;
using stream_demo::ImuData;

class AsyncCallHandler {
public:
    AsyncCallHandler(ImuStreamService::AsyncService* service, ServerCompletionQueue* cq)
        : service_(service), cq_(cq), writer_(&ctx_) {
        Proceed(true);
    }

    void Proceed(bool ok) {
        if (state_ == CREATE) {
            state_ = READ;
            service_->RequestStartAndSubmit(&ctx_, &request_, &writer_, cq_, cq_, this);
        } else if (state_ == READ) {
            new AsyncCallHandler(service_, cq_); // 创建下一个 handler

            state_ = WRITE;
            SendImuData();  // 发送第一条数据
        } else if (state_ == WRITE) {
            if (!ok) {
                delete this;
                return;
            }
            ++frame_id_;
            if (frame_id_ >= 10) {  // 模拟10帧数据后完成
                state_ = FINISH;
                writer_.Finish(Status::OK, this);
            } else {
                SendImuData();
            }
        } else {
            delete this;
        }
    }

private:
    void SendImuData() {
        flatbuffers::grpc::MessageBuilder builder;
        auto imu_offset = stream_demo::CreateImuData(builder,
            /*hmd_time_nanos_system=*/1000 + frame_id_,
            /*hmd_time_nanos_device=*/2000 + frame_id_,
            /*hmd_time_nanos_sensor=*/3000 + frame_id_,
            /*data_mask=*/1,
            /*gyroscope_x=*/0.1f,
            /*gyroscope_y=*/0.2f,
            /*gyroscope_z=*/0.3f,
            /*accelerometer_x=*/1.0f,
            /*accelerometer_y=*/1.1f,
            /*accelerometer_z=*/1.2f,
            /*magnetometer_x=*/0.0f,
            /*magnetometer_y=*/0.0f,
            /*magnetometer_z=*/0.0f,
            /*temperature=*/36.5f,
            /*imu_id=*/0,
            /*frame_id=*/frame_id_,
            /*gyroscope_numerator=*/1,
            /*accelerometer_numerator=*/1,
            /*magnetometer_numerator=*/1,
            /*out_numerator_mask=*/0
        );
        builder.Finish(imu_offset);
        response_msg_ = builder.ReleaseMessage<ImuData>();
        writer_.Write(response_msg_, this);
    }

    ImuStreamService::AsyncService* service_;
    ServerCompletionQueue* cq_;
    ServerContext ctx_;

    flatbuffers::grpc::Message<StartImuRequest> request_;
    flatbuffers::grpc::Message<ImuData> response_msg_;
    ServerAsyncWriter<flatbuffers::grpc::Message<ImuData>> writer_;

    enum CallStatus { CREATE, READ, WRITE, FINISH };
    CallStatus state_ = CREATE;

    int frame_id_ = 0;
};

void RunServer() {
    std::string server_address("0.0.0.0:50051");
    ImuStreamService::AsyncService service;
    ServerBuilder builder;

    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    builder.RegisterService(&service);

    std::unique_ptr<ServerCompletionQueue> cq = builder.AddCompletionQueue();
    std::unique_ptr<Server> server = builder.BuildAndStart();

    std::cout << "Server listening on " << server_address << std::endl;

    // 创建第一个 handler 实例
    new AsyncCallHandler(&service, cq.get());

    void* tag;
    bool ok;
    while (cq->Next(&tag, &ok)) {
        static_cast<AsyncCallHandler*>(tag)->Proceed(ok);
    }
}

int main(int argc, char** argv) {
    RunServer();
    return 0;
}
