#pragma once
#include <fstream>
#include <iostream>
#include <stdio.h>
#include <errno.h>

class FileWriter {
public:
    explicit FileWriter(const std::string& filename)
        : filename_(filename), first_write_(true), file_opened_(false) {}

    ~FileWriter() {
        close();
    }

    void Write(const uint8_t* data, uint32_t data_size) {
        if (first_write_) {
            if (remove(filename_.c_str()) == 0) {
                LOG_INFO("File {} deleted successfully", filename_);
            } else {
                LOG_ERROR("Failed to delete file {}: {}", filename_, strerror(errno));
            }
            first_write_ = false;
        }

        if (!file_opened_) {
            file_.open(filename_, std::ios::binary | std::ios::app);
            if (!file_.is_open()) {
                std::cerr << "Failed to open file for append: " << filename_ << std::endl;
                return;
            }
            file_opened_ = true;
        }

        file_.write(reinterpret_cast<const char*>(data), data_size);
        if (!file_) {
            std::cerr << "Failed to write all data" << std::endl;
        }

        file_.flush();  // Optional: flush to disk after each write
    }

    void close() {
        if (file_opened_) {
            file_.close();
            file_opened_ = false;
        }
    }

private:
    std::string filename_;
    std::ofstream file_;
    bool first_write_;
    bool file_opened_;
};
