#include "async_usb.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <common.h>
#include <data_types.h>
#include "tcp_heartbeat.h"

int main() {

	Logger::defaultLogger()->set_level(LogLevel::trace);
    
    std::string interface_string = FRAMES_INTERFACE_STRING;
    std::string imu_interface_string;

 #if USE_NEW_USB
    LOG_INFO("USE_NEW_USB");
    interface_string = FRAMES_INTERFACE_STRING_CAMERA;
    imu_interface_string = FRAMES_INTERFACE_STRING_IMU;
 #endif 

    std::shared_ptr<AsyncUSBDevice> device = std::make_shared<AsyncUSBDevice>();

    if(!device->FindUsbDevice(interface_string)){
        LOG_ERROR("Can not find frames device");
        return -1;  
    }
    if (!device->open()) {
        std::cerr << "Failed to open device" << std::endl;
        return -1;
    }

    std::shared_ptr<AsyncUSBDevice> device_imu;

    if(!imu_interface_string.empty()) {
        device_imu = std::make_shared<AsyncUSBDevice>();
        if(!device_imu->FindUsbDevice(imu_interface_string)){
            LOG_ERROR("Can not find frames device");
            return -1;  
        }
        if (!device_imu->open()) {
            std::cerr << "Failed to open device" << std::endl;
            return -1;
        }
    }

#if USE_HEART_BEAT
    std::thread tcp_heartbeat_thread(RunTcpHeartbeat);
    tcp_heartbeat_thread.detach();
#endif

    std::cout << "Device opened successfully" << std::endl;

    std::this_thread::sleep_for(std::chrono::seconds(100000000));
    
    return 0;
}