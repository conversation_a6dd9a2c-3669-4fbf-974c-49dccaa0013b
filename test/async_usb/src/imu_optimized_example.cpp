#include "async_usb.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <atomic>
#include <common.h>
#include <data_types.h>
#include "imu_optimization.h"

// 全局变量用于统计
std::atomic<uint64_t> g_imu_frame_count{0};
std::atomic<uint64_t> g_last_print_time{0};

// IMU数据回调函数
void ImuDataCallbackFunction(const ImuRawData* imu_data, uint64_t timestamp) {
    g_imu_frame_count++;
    
    // 每秒打印一次统计信息
    uint64_t current_time = FMonotonicGetNs();
    uint64_t last_time = g_last_print_time.load();
    
    if (current_time - last_time >= 1000000000ULL) { // 1秒
        if (g_last_print_time.compare_exchange_strong(last_time, current_time)) {
            uint64_t fps = g_imu_frame_count.load();
            g_imu_frame_count = 0;
            
            std::cout << "IMU FPS: " << fps 
                      << ", Frame ID: " << imu_data->frame_id
                      << ", Latency: " << (timestamp - imu_data->timestamp_system) / 1000 << "us"
                      << ", Gyro: [" << imu_data->gyro[0] << ", " << imu_data->gyro[1] << ", " << imu_data->gyro[2] << "]"
                      << ", Accel: [" << imu_data->accel[0] << ", " << imu_data->accel[1] << ", " << imu_data->accel[2] << "]"
                      << std::endl;
        }
    }
}

int main() {
    Logger::defaultLogger()->set_level(LogLevel::info);  // 减少日志输出以提高性能
    
    std::cout << "=== IMU优化示例程序 ===" << std::endl;
    std::cout << "此程序演示如何使用IMU优化配置来提高IMU数据接收速度" << std::endl;
    
    // 创建IMU优化配置
    ImuOptimizationConfig imu_config = DEFAULT_IMU_CONFIG;
    imu_config.enable_high_priority_thread = true;
    imu_config.enable_immediate_processing = true;
    imu_config.enable_reduced_logging = true;  // 减少日志输出
    imu_config.num_transfer_buffers = 60;      // 增加传输缓冲区数量
    imu_config.transfer_buffer_size = 4 * 1024; // 使用较小的传输缓冲区
    imu_config.usb_timeout_ms = 500;           // 更短的超时时间
    imu_config.data_callback = ImuDataCallbackFunction;
    
    std::cout << "IMU优化配置:" << std::endl;
    std::cout << "  - 传输缓冲区数量: " << imu_config.num_transfer_buffers << std::endl;
    std::cout << "  - 传输缓冲区大小: " << imu_config.transfer_buffer_size << " bytes" << std::endl;
    std::cout << "  - 帧缓冲区大小: " << imu_config.frame_buffer_size << " bytes" << std::endl;
    std::cout << "  - USB超时时间: " << imu_config.usb_timeout_ms << " ms" << std::endl;
    std::cout << "  - 立即处理: " << (imu_config.enable_immediate_processing ? "启用" : "禁用") << std::endl;
    std::cout << "  - 减少日志: " << (imu_config.enable_reduced_logging ? "启用" : "禁用") << std::endl;
    
    // 创建优化的USB设备
    std::shared_ptr<AsyncUSBDevice> device = std::make_shared<AsyncUSBDevice>(imu_config);
    
    std::string interface_string = FRAMES_INTERFACE_STRING;
    std::string imu_interface_string;

#if USE_NEW_USB
    LOG_INFO("使用新的USB接口配置");
    interface_string = FRAMES_INTERFACE_STRING_CAMERA;
    imu_interface_string = FRAMES_INTERFACE_STRING_IMU;
#endif 

    // 查找并打开相机设备
    if(!device->FindUsbDevice(interface_string)){
        LOG_ERROR("无法找到相机设备");
        return -1;  
    }
    if (!device->open()) {
        std::cerr << "无法打开相机设备" << std::endl;
        return -1;
    }

    // 查找并打开IMU设备
    std::shared_ptr<AsyncUSBDevice> device_imu;
    if(!imu_interface_string.empty()) {
        device_imu = std::make_shared<AsyncUSBDevice>(imu_config);
        if(!device_imu->FindUsbDevice(imu_interface_string)){
            LOG_ERROR("无法找到IMU设备");
            return -1;  
        }
        if (!device_imu->open()) {
            std::cerr << "无法打开IMU设备" << std::endl;
            return -1;
        }
        std::cout << "IMU设备已成功打开并配置优化参数" << std::endl;
    } else {
        std::cout << "使用单一设备接口，IMU数据将通过主设备接收" << std::endl;
    }

    std::cout << "设备已成功打开，开始接收数据..." << std::endl;
    std::cout << "按 Ctrl+C 退出程序" << std::endl;

    // 运行一段时间来测试性能
    auto start_time = std::chrono::steady_clock::now();
    std::this_thread::sleep_for(std::chrono::seconds(30));  // 运行30秒
    
    // 打印最终统计信息
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);
    
    std::cout << "\n=== 性能统计 ===" << std::endl;
    std::cout << "运行时间: " << duration.count() << " 秒" << std::endl;
    
    if (device_imu) {
        ImuPerformanceStats stats = device_imu->GetImuPerformanceStats();
        std::cout << "IMU性能统计:" << std::endl;
        std::cout << "  - 总接收帧数: " << stats.total_frames_received << std::endl;
        std::cout << "  - 平均延迟: " << stats.average_latency_ns / 1000 << " us" << std::endl;
        std::cout << "  - 最小延迟: " << stats.min_latency_ns / 1000 << " us" << std::endl;
        std::cout << "  - 最大延迟: " << stats.max_latency_ns / 1000 << " us" << std::endl;
        std::cout << "  - 丢帧数: " << stats.dropped_frames << std::endl;
    }
    
    return 0;
}
