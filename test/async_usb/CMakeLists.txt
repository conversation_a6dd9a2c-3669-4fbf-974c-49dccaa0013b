
message(STATUS "CMAKE_SOURCE_DIR = ${CMAKE_SOURCE_DIR}")
message(STATUS "CMAKE_BINARY_DIR = ${CMAKE_BINARY_DIR}")

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR})

find_path(LIBUSB_INCLUDE_DIR
    NAMES libusb.h
    PATHS 
        "${CMAKE_SOURCE_DIR}/external/libusb-cmake/libusb/libusb"
    REQUIRED
)

# 包含目录
include_directories(
    "${CMAKE_BINARY_DIR}/test/env"
    ${LIBUSB_INCLUDE_DIR}
    include
)

# 添加可执行文件
add_executable(RawUsbClient
    src/async_usb.cpp
    src/main.cpp
    src/tcp_heartbeat.cpp
)

# 链接静态库和必要的 Windows 依赖
target_link_libraries(RawUsbClient
    usb-1.0
    framework::framework
)

if (WIN32)
    target_link_libraries(RawUsbClient
        advapi32.lib
        setupapi.lib
    )
    if(MINGW)
        target_link_libraries(RawUsbClient
            ws2_32
        )
    endif()
endif()
