#pragma once
#include <framework/util/log.h>

#define GRAY_FRAME_SIZE 768*512


enum FrameType {
	None = 0,
	RequestToFrames = 1,
	ResponseFromFrames = 2,
    ImuFromFrames = 10294,   
    GraycameraFromFrames = 10056,
    RgbFromFrames = 10117,
    Heartbeat = 10000,
};

#pragma pack(1)

struct FrameHeader {
    uint32_t len;
    uint32_t type;
};

struct TimeResonse{
    uint64_t t1;
    uint64_t t2;
    uint64_t t3;
};

struct TimeRequest{
    uint64_t t1;
};

typedef struct ImuRawData {
    union {
        struct {
			uint64_t timestamp_system;
			uint64_t timestamp_device;
			uint64_t timestamp_sensor;
			int32_t data_mask;
			
			float gyro[3];        // 陀螺仪数据 (x, y, z)
			float accel[3];       // 加速度计数据 (x, y, z)
			float mag[3];         // 磁力计数据 (x, y, z)
			float temperature;    // 温度
			
			int8_t imu_id;      // IMU设备ID
			uint32_t frame_id;    // 帧ID
			
			int32_t gyro_numerator;      // 陀螺仪分子值
			int32_t accel_numerator;     // 加速度计分子值
			int32_t mag_numerator;       // 磁力计分子值
			
			uint32_t out_numerator_mask;  // 超量程掩码
        };
        uint8_t padding[128];
    };

} ImuRawData;


enum NRGrayscaleCameraPixelFormat : uint8_t{
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_UNKNOWN = 0,
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_YUV_420_888,
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_RGB_BAYER_8BPP,
};


enum NRGrayscaleCameraID : uint8_t{
    NR_GRAYSCALE_CAMERA_ID_0 = 0x0001,
    NR_GRAYSCALE_CAMERA_ID_1 = 0x0002,
    NR_GRAYSCALE_CAMERA_ID_2 = 0x0004,
    NR_GRAYSCALE_CAMERA_ID_3 = 0x0008,
};

typedef struct NRGrayscaleCameraUnitData {
    union {
        struct {
            uint32_t offset;
            NRGrayscaleCameraID camera_id;
            uint32_t width;
            uint32_t height;
            uint32_t stride;
            uint64_t exposure_start_time_device;
            uint32_t exposure_duration;
            uint32_t rolling_shutter_time;
            uint32_t gain;
            uint64_t exposure_start_time_system;
        };
        uint8_t padding[64];
    };

} NRGrayscaleCameraUnitData;

typedef struct NRGrayscaleCameraFrameData {
    union {
        struct {
            NRGrayscaleCameraUnitData cameras[4];
            uint64_t data_ptr;
            uint32_t data_bytes;
            uint8_t camera_count;
            uint32_t frame_id;
            NRGrayscaleCameraPixelFormat pixel_format;
        };
        uint8_t padding[320];
    };

} NRGrayscaleCameraFrameData;

enum NRRgbCameraPixelFormat : uint8_t{
    NR_RGB_CAMERA_PIXEL_FORMAT_UNKNOWN = 0,
    NR_RGB_CAMERA_PIXEL_FORMAT_YUV_420_888,
    NR_RGB_CAMERA_PIXEL_FORMAT_RGB_888,
};

typedef struct NRRgbCameraFrameData {
    union {
        struct {
            uint64_t data_ptr;
            uint32_t data_bytes;
            uint32_t width;
            uint32_t height;
            uint32_t stride;
            uint64_t exposure_start_time_device;
            uint32_t exposure_duration;
            uint32_t rolling_shutter_time;
            uint32_t gain;
            uint32_t frame_id;
            NRRgbCameraPixelFormat pixel_format;
            uint64_t exposure_start_time_system;
        };
        uint8_t padding[96];
    };
	uint8_t transmit_state;

} NRRgbCameraFrameData;

#pragma pack()



template <>
struct fmt::formatter<ImuRawData> : fmt::formatter<std::string>
{
    auto format(const ImuRawData& v, format_context& ctx) -> decltype(ctx.out())
    {
        return fmt::format_to(
            ctx.out(),
            "[timestamp_system: {}, timestamp_device: {}, timestamp_sensor: {}, data_mask: {}, "
            "gyro: [{}, {}, {}], accel: [{}, {}, {}], mag: [{}, {}, {}], temperature: {}, "
            "imu_id: {}, frame_id: {}, gyro_numerator: {}, accel_numerator: {}, mag_numerator: {}, "
            "out_numerator_mask: {}]",
            v.timestamp_system,
            v.timestamp_device,
            v.timestamp_sensor,
            v.data_mask,
            v.gyro[0], v.gyro[1], v.gyro[2],
            v.accel[0], v.accel[1], v.accel[2],
            v.mag[0], v.mag[1], v.mag[2],
            v.temperature,
            static_cast<int>(v.imu_id),
            v.frame_id,
            v.gyro_numerator,
            v.accel_numerator,
            v.mag_numerator,
            v.out_numerator_mask
        );
    }
};

template <>
struct fmt::formatter<NRGrayscaleCameraFrameData> : fmt::formatter<std::string>
{
    auto format(const NRGrayscaleCameraFrameData& v, format_context& ctx) -> decltype(ctx.out())
    {
        std::string cameras_info;
        for (size_t i = 0; i < v.camera_count && i < 4; ++i)
        {
            const auto& cam = v.cameras[i];
            fmt::format_to(
                std::back_inserter(cameras_info),
                "  [camera_id: {}, offset: {}, width: {}, height: {}, stride: {}, "
                "exposure_start_time_device: {}, exposure_duration: {}, "
                "rolling_shutter_time: {}, gain: {}, exposure_start_time_system: {}]\n",
                static_cast<int>(cam.camera_id),
                cam.offset,
                cam.width,
                cam.height,
                cam.stride,
                cam.exposure_start_time_device,
                cam.exposure_duration,
                cam.rolling_shutter_time,
                cam.gain,
                cam.exposure_start_time_system
            );
        }

        return fmt::format_to(
            ctx.out(),
            "[frame_id: {}, data_ptr: {}, data_bytes: {}, camera_count: {}, pixel_format: {}, cameras:\n{}]",
            v.frame_id,
            v.data_ptr,
            v.data_bytes,
            static_cast<int>(v.camera_count),
            static_cast<int>(v.pixel_format),
            cameras_info
        );
    }
};


template <>
struct fmt::formatter<NRRgbCameraFrameData> : fmt::formatter<std::string>
{
    auto format(const NRRgbCameraFrameData& v, format_context& ctx) -> decltype(ctx.out())
    {
        return fmt::format_to(
            ctx.out(),
            "[frame_id: {}, data_ptr: {}, data_bytes: {}, width: {}, height: {}, stride: {}, "
            "exposure_start_time_device: {}, exposure_duration: {}, rolling_shutter_time: {}, "
            "gain: {}, pixel_format: {}, exposure_start_time_system: {}, transmit_state: {}]",
            v.frame_id,
            v.data_ptr,
            v.data_bytes,
            v.width,
            v.height,
            v.stride,
            v.exposure_start_time_device,
            v.exposure_duration,
            v.rolling_shutter_time,
            v.gain,
            static_cast<int>(v.pixel_format),
            v.exposure_start_time_system,
            static_cast<int>(v.transmit_state)
        );
    }
};
