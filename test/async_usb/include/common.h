#pragma once
#include <vector>
#include <libusb.h>

#include "system_config.h"

#include <framework/util/log.h>
using namespace framework::util::log;

#define USE_HEART_BEAT 0

#define USE_NEW_USB  1

#define NEED_PUBLISH_FRAME 0


#define FRAMES_INTERFACE_STRING "com.google.frames-protocol"

#define FRAMES_INTERFACE_STRING_CAMERA "com.google.frames-protocol-camera"
#define FRAMES_INTERFACE_STRING_IMU "com.google.frames-protocol-imu"


struct EndpointInfo {
    uint8_t address;
    uint8_t attributes;
    uint16_t maxPacketSize;
    uint8_t interval;
};

struct InterfaceInfo {
    int interfaceNumber;
    std::vector<EndpointInfo> endpoints;
};

enum EndpointTransferType {
    CONTROL = 0x00,
    ISOCHRONOUS = 0x01,
    BULK = 0x02,
    INTERRUPT = 0x03
};


inline bool is_in_endpoint(uint8_t address) {
    return (address & LIBUSB_ENDPOINT_IN) != 0; // LIBUSB_ENDPOINT_IN == 0x80
}

inline bool is_out_endpoint(uint8_t address) {
    return (address & LIBUSB_ENDPOINT_DIR_MASK) == LIBUSB_ENDPOINT_OUT; // LIBUSB_ENDPOINT_OUT == 0x00
}

inline EndpointTransferType get_transfer_type(const EndpointInfo& ep) {
    return static_cast<EndpointTransferType>(ep.attributes & 0x03);
}



