#pragma once

// IMU优化配置
// 这些配置专门用于提高IMU数据接收速度

// IMU传输优化参数
#define IMU_OPTIMIZATION_ENABLED 1

// IMU线程优先级设置
#define IMU_THREAD_PRIORITY_ENABLED 1
#define IMU_THREAD_PRIORITY_LEVEL 99  // 实时调度优先级

// IMU缓冲区优化
#define IMU_BUFFER_OPTIMIZATION_ENABLED 1

// IMU数据处理优化选项
#define IMU_IMMEDIATE_PROCESSING 1  // 立即处理IMU数据，不等待
#define IMU_REDUCED_LOGGING 1       // 减少IMU相关的日志输出以提高性能

// IMU USB传输优化
#define IMU_USB_OPTIMIZATION_ENABLED 1
#define IMU_SHORTER_TIMEOUT 1       // 使用更短的USB传输超时

// IMU数据回调接口
typedef void (*ImuDataCallback)(const ImuRawData* imu_data, uint64_t timestamp);

// IMU性能监控
struct ImuPerformanceStats {
    uint64_t total_frames_received;
    uint64_t frames_per_second;
    uint64_t average_latency_ns;
    uint64_t max_latency_ns;
    uint64_t min_latency_ns;
    uint64_t dropped_frames;
};

// IMU优化配置结构
struct ImuOptimizationConfig {
    bool enable_high_priority_thread;
    bool enable_immediate_processing;
    bool enable_reduced_logging;
    uint32_t num_transfer_buffers;
    uint32_t transfer_buffer_size;
    uint32_t frame_buffer_size;
    uint32_t usb_timeout_ms;
    ImuDataCallback data_callback;
};

// 默认IMU优化配置
static const ImuOptimizationConfig DEFAULT_IMU_CONFIG = {
    .enable_high_priority_thread = true,
    .enable_immediate_processing = true,
    .enable_reduced_logging = false,  // 可以根据需要调整
    .num_transfer_buffers = 50,
    .transfer_buffer_size = 8 * 1024,
    .frame_buffer_size = 32 * 1024,
    .usb_timeout_ms = 1000,
    .data_callback = nullptr
};
