#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <sys/socket.h>
#include <unistd.h>
#include <chrono>
#include <cstring>
#include <iostream>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <signal.h>
#include <time.h>

struct TimeReply {
    uint64_t t2;
    uint64_t t3;
};

#define NS_PER_SEC (1000000000)

static inline uint64_t GetMonotonicTimeNs()
{
    struct timespec ts;
    int ret = clock_gettime(CLOCK_MONOTONIC, &ts);
    if (ret != 0) {
        return 0;
    }
    uint64_t ns = 0;
    ns += (uint64_t)ts.tv_sec * NS_PER_SEC;
    ns += (uint64_t)ts.tv_nsec;
    return ns;
}

static inline void NanoSleep(int64_t nsec)
{
    struct timespec spec;
    spec.tv_sec = (nsec / NS_PER_SEC);
    spec.tv_nsec = (nsec % NS_PER_SEC);
    nanosleep(&spec, NULL);
}

static constexpr uint64_t DELTA_NS = NS_PER_SEC / 90;

std::queue<uint64_t> t1_queue;
std::mutex queue_mutex;
std::condition_variable queue_cv;
bool stop = false;

int ConnectSocket(const char* ip, uint16_t port) {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == -1) {
        perror("socket");
        return -1;
    }

    sockaddr_in addr {};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    inet_pton(AF_INET, ip, &addr.sin_addr);

    if (connect(sock, (sockaddr*)&addr, sizeof(addr)) == -1) {
        perror("connect");
        close(sock);
        return -1;
    }

    int flag = 1;
    setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, &flag, sizeof(flag));
    // Set send and receive buffer size to 128 bytes
    int bufsize = 128;
    setsockopt(sock, SOL_SOCKET, SO_SNDBUF, &bufsize, sizeof(bufsize));
    setsockopt(sock, SOL_SOCKET, SO_RCVBUF, &bufsize, sizeof(bufsize));

    return sock;
}

void SenderThread(int sock) {
    while (!stop) {
        uint64_t t1 = 0;

        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            queue_cv.wait(lock, [] { return !t1_queue.empty() || stop; });
            if (stop) break;

            t1 = t1_queue.front();
            if(t1_queue.size() > 1) {
                std::cout << "t1:" << t1 << ",t1_queue.size:" << t1_queue.size() << std::endl;
            }
            t1_queue.pop();
        }

        uint64_t start_time = GetMonotonicTimeNs();
        if (write(sock, &t1, sizeof(t1)) != sizeof(t1)) {
            std::cout << "write failed" << std::endl;
            break;
        }
        else{
            std::cout << "write success,t1:" << t1 << ",cost_time:" << GetMonotonicTimeNs() - start_time << std::endl;
        }

        start_time = GetMonotonicTimeNs();
        TimeReply reply;
        if (read(sock, &reply, sizeof(reply)) != sizeof(reply)) {
            std::cout << "read failed" << std::endl;
            break;
        }
        else{
            std::cout << "read success,t1:" << t1 << ",cost_time:" << GetMonotonicTimeNs() - start_time << std::endl;
        }

        uint64_t t2 = reply.t2;
        uint64_t t3 = reply.t3;
        uint64_t t4 = GetMonotonicTimeNs();

        std::cout << t1 << "," << t2 << "," << t3 << "," << t4 << std::endl;
        std::cout << "t4-t1=" << (t4-t1) << std::endl;
    }
}

int main(int argc, char* argv[]) {
    signal(SIGPIPE, SIG_IGN);
    const char* ip = "";

    if (argc >= 2) {
        if (std::strcmp(argv[1], "1") == 0) {
            ip = "***********";
        } else if (std::strcmp(argv[1], "2") == 0) {
            ip = "***********";
        } else {
            std::cerr << "Unknown argument: " << argv[1] << "\n";
            std::cerr << "Usage: " << argv[0] << " [1|2]\n";
            return 1;
        }
    } else {
        std::cerr << "Missing argument, please specify target IP:\n";
        std::cerr << "Usage: " << argv[0] << " [1|2]\n";
        return 1;
    }

    std::cout << "The server IP is " << ip << std::endl;
    int sock = ConnectSocket(ip, 50051);
    if (sock < 0) return 1;

    uint64_t app_ns = GetMonotonicTimeNs();

    std::thread sender(SenderThread, sock);

    std::cout << "START LOOP" << std::endl;

    while (true) {
        uint64_t real_ns = GetMonotonicTimeNs();
        app_ns += DELTA_NS;
        uint64_t t1 = real_ns;
        {
            std::lock_guard<std::mutex> lock(queue_mutex);
            t1_queue.push(t1);
        }
        queue_cv.notify_one();
        NanoSleep(app_ns - real_ns);
    }

    stop = true;
    queue_cv.notify_one();
    sender.join();

    close(sock);
    return 0;
}
