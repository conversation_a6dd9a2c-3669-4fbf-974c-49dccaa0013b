cmake_minimum_required(VERSION 3.10)
project(SocketTimeSync)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# If you have headers in include/ directory, uncomment and adjust as needed
# include_directories(${PROJECT_SOURCE_DIR}/include)

if (Xrlinux)
    # Build server-side executables for Xrlinux
    add_executable(sync_raw_socket_server server.cc)
    target_link_libraries(sync_raw_socket_server pthread)

    add_executable(async_raw_socket_server server_async.cc)
    target_link_libraries(async_raw_socket_server pthread)

else()
    # Build client-side executables for other platforms
    set(LINK_LIB pthread)
    if(ANDROID)
        # Android typically does not need explicit pthread linkage
        set(LINK_LIB "")
    endif()

    add_executable(sync_raw_socket_client client.cc)
    target_link_libraries(sync_raw_socket_client ${LINK_LIB})

    add_executable(async_raw_socket_client client_async.cc)
    target_link_libraries(async_raw_socket_client ${LINK_LIB})
endif()
