// === server.cc ===
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <sys/socket.h>
#include <unistd.h>
#include <cstring>
#include <iostream>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <signal.h>

struct TimeReply {
    uint64_t t2;
    uint64_t t3;
};

#define NS_PER_SEC (1000000000)

static inline uint64_t GetMonotonicTimeNs()
{
    struct timespec ts;
    int ret = clock_gettime(CLOCK_MONOTONIC, &ts);
    if (ret != 0) {
        return 0;
    }
    uint64_t ns = 0;
    ns += (uint64_t)ts.tv_sec * NS_PER_SEC;
    ns += (uint64_t)ts.tv_nsec;
    return ns;
}

int main() {
    signal(SIGPIPE, SIG_IGN);

    int server_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (server_fd == -1) {
        perror("socket");
        return 1;
    }

    int opt = 1;
    setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));

    // Set send and receive buffer size to 128 bytes
    int bufsize = 128;
    setsockopt(server_fd, SOL_SOCKET, SO_SNDBUF, &bufsize, sizeof(bufsize));
    setsockopt(server_fd, SOL_SOCKET, SO_RCVBUF, &bufsize, sizeof(bufsize));

    sockaddr_in addr {};
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = inet_addr("0.0.0.0");
    addr.sin_port = htons(50051);

    if (bind(server_fd, (sockaddr*)&addr, sizeof(addr)) == -1) {
        perror("bind");
        return 1;
    }

    if (listen(server_fd, 1) == -1) {
        perror("listen");
        return 1;
    }

    std::cout << "Server waiting ...\n";
    int client_fd = accept(server_fd, nullptr, nullptr);
    if (client_fd == -1) {
        perror("accept");
        return 1;
    }

    int flag = 1;
    setsockopt(client_fd, IPPROTO_TCP, TCP_NODELAY, &flag, sizeof(flag));
    // Also set buffer size for the connected socket
    setsockopt(client_fd, SOL_SOCKET, SO_SNDBUF, &bufsize, sizeof(bufsize));
    setsockopt(client_fd, SOL_SOCKET, SO_RCVBUF, &bufsize, sizeof(bufsize));

    while (true) {
        uint64_t t1_net;
        ssize_t bytes_read = read(client_fd, &t1_net, sizeof(t1_net));
        if (bytes_read != sizeof(t1_net)) {
            std::cout << "read failed, bytes_read=" << bytes_read << std::endl;
            break;
        }

        uint64_t t2 = GetMonotonicTimeNs();
        uint64_t t3 = GetMonotonicTimeNs();

        TimeReply reply = {t2, t3};
        ssize_t bytes_written = write(client_fd, &reply, sizeof(reply));
        if (bytes_written != sizeof(reply)) {
            std::cout << "write failed, size=" << bytes_written << std::endl;
            break;
        }
    }

    close(client_fd);
    close(server_fd);
    return 0;
}
