set(EXPORT_BASENAME TEST)
set(PLATFORM_CONFIG_BASENAME TEST)
set(SYSTEM_CONFIG_BASENAME TEST)
set(VERSION_BASENAME TEST)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti")

add_subdirectory(../external/project/cmake/env env)

if(USE_FRAMEWORK)
    add_definitions(-DUSE_FRAMEWORK)
endif()

#grpc+protobuffer callback方式异步传输
#add_subdirectory(source)

#裸socket传输数据
#add_subdirectory(test_socket)

#简单的grpc+flatbuffers demo
#add_subdirectory(test_flatbuffers) 

#grpc+protobuf 模拟传imu
#add_subdirectory(grpc_pbs)

#grpc+flatbuffers 模拟传imu
#add_subdirectory(grpc_flatbuffers)

if (NOT Xrlinux)
  add_subdirectory(usb)
endif()

add_subdirectory(axr)

