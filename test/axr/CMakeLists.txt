include(../cmake/common.cmake)

# 设置 proto 文件目录
set(PROTO_DIR "${CMAKE_CURRENT_SOURCE_DIR}/proto")
file(GLOB PROTO_FILES "${PROTO_DIR}/*.proto")

set(GENERATED_SRCS "")
set(GENERATED_HDRS "")

set(GENERATED_SRC_DIR "${CMAKE_CURRENT_BINARY_DIR}/proto")

# 确保生成目录存在
file(MAKE_DIRECTORY "${GENERATED_SRC_DIR}")

foreach(proto_file ${PROTO_FILES})
  get_filename_component(proto_name ${proto_file} NAME_WE)

  set(proto_src "${GENERATED_SRC_DIR}/${proto_name}.pb.cc")
  set(proto_hdr "${GENERATED_SRC_DIR}/${proto_name}.pb.h")
  set(grpc_src "${GENERATED_SRC_DIR}/${proto_name}.grpc.pb.cc")
  set(grpc_hdr "${GENERATED_SRC_DIR}/${proto_name}.grpc.pb.h")

  add_custom_command(
    OUTPUT "${proto_src}" "${proto_hdr}" "${grpc_src}" "${grpc_hdr}"
    COMMAND ${_PROTOBUF_PROTOC}
    ARGS --grpc_out "${GENERATED_SRC_DIR}"
         --cpp_out "${GENERATED_SRC_DIR}"
         -I "${PROTO_DIR}"
         --plugin=protoc-gen-grpc="${_GRPC_CPP_PLUGIN_EXECUTABLE}"
         "${proto_file}"
    DEPENDS "${proto_file}"
  )

  list(APPEND GENERATED_SRCS "${proto_src}" "${grpc_src}")
  list(APPEND GENERATED_HDRS "${proto_hdr}" "${grpc_hdr}")
endforeach()

# Include generated *.pb.h files
include_directories("${GENERATED_SRC_DIR}")

# 创建 proto 相关的静态库
add_library(hw_grpc_proto ${GENERATED_SRCS} ${GENERATED_HDRS})
target_link_libraries(hw_grpc_proto
  absl::check
  ${_REFLECTION}
  ${_GRPC_GRPCPP}
  ${_PROTOBUF_LIBPROTOBUF})

# 依赖列表
set(AXR_DEPS
    hw_grpc_proto
    absl::check
    absl::flags
    absl::flags_parse
    absl::log
    absl::log_initialize
    ${_REFLECTION}
    ${_GRPC_GRPCPP}
    ${_PROTOBUF_LIBPROTOBUF}
)



# 生成 server 可执行文件
if (false)
add_executable(axr_server axr_grpc_server.cc)
target_link_libraries(axr_server ${AXR_DEPS})

add_executable(axr_server_all_symbols axr_grpc_server.cc)
target_link_libraries(axr_server_all_symbols ${AXR_DEPS})
PROJECT_STRIP_LINK_OPTIONS(axr_server axr_server_all_symbols "symbol_type_null" FALSE)
endif()

if (true)
# 生成 client 可执行文件
add_executable(axr_client axr_grpc_client.cc)
target_link_libraries(axr_client ${AXR_DEPS} RawUsbClientLib)

add_executable(axr_client_all_symbols axr_grpc_client.cc)
target_link_libraries(axr_client_all_symbols ${AXR_DEPS} RawUsbClientLib)
PROJECT_STRIP_LINK_OPTIONS(axr_client axr_client_all_symbols "symbol_type_null" FALSE)
endif()
