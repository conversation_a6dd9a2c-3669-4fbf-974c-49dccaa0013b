syntax = "proto3";

package frames.audio;

// Placeholder for audio protocol. Allows transition from TCP/IP to protobuf
// based protocol

// *** Request Messages (Host to Receiver) ***

// Request the audio capabilities of the Receiver.
//
// Next ID = 1
message GetCapabilitiesRequest {}

// *** Response Messages (Receiver to Host) ***

// Return the capabilities of the Receiver's audio.
//
// Next ID = 3
message GetCapabilitiesResponse {
  enum AudioTransport {
    AUDIO_TRANSPORT_UNDEFINED = 0;
    // To support the bringup utilization of the TCP_IP protocol
    AUDIO_TRANSPORT_USB_AUDIO = 1;
    // Long term operation
    AUDIO_TRANSPORT_USES_PROTOCOL = 2;
  }
  AudioTransport audio_transport_playback = 1;
  AudioTransport audio_transport_capture = 2;
}