syntax = "proto3";

package frames.time;

// *** IEEE-1588 Precision Time Protocol (PTP) ***
// The Puck (Frames Host) serves as the primary.

// The frames can either alter their clock or just maintain an internal offset

// TODO (b/*********): We need to test the accuracy of this PTP implementation
// particularly because these timestamps are set at the app layer and thus
// less accurate than hardware timestamps which are used in more accurate
// implementations.

// Request from Host to Frames initiating the PTP sync. PTP calls this "Sync".
// T1 is not included because the host can simply store it in memory for now.
// The "effective" T1 is often sent in a second followup Sync message in other
// PTP implementations. Here, the host sends the effective T1 in DelayRequest
// so we don't need a SyncFollow message.
// Epoch is 1970-01-01 00:00:00 UTC.
message SyncRequest {}

// The response from the Frames to SyncRequest. PTP calls this "Delay_Req".
// Epoch is 1970-01-01 00:00:00 UTC.
// Next ID: 3
message SyncResponse {
  int64 t2_time_since_epoch_ns = 1; // UTC; time when frames received SyncRequest
  int64 t3_time_since_epoch_ns = 2; // UTC; time when frames sent SyncResponse
}

// Request from Host to Frames containing all timestamps.
// PTP calls this the "Delay_Resp".
// Frames calculates offset = ((t2 - t1) - (t4 - t3)) / 2
// Epoch is 1970-01-01 00:00:00 UTC.
// Next ID: 5
message DelayRequest {
  int64 t1_time_since_epoch_ns = 1; // UTC; time when puck sent SyncRequest
  int64 t2_time_since_epoch_ns = 2; // UTC; time when frames received SyncRequest
  int64 t3_time_since_epoch_ns = 3; // UTC; time when frames sent SyncResponse
  int64 t4_time_since_epoch_ns = 4; // UTC; time when puck received SyncResponse
}

// The response from the Frames to DelayRequest.
message DelayResponse {}

// Contains all sub-message types related to synchronizing time via PTP
// Next ID: 5
message PrecisionTimeProtocolMessage {
  oneof precision_time_protocol_message {
    SyncRequest sync_request = 1;
    SyncResponse sync_response = 2;
    DelayRequest delay_request = 3;
    DelayResponse delay_response = 4;
  }
}
