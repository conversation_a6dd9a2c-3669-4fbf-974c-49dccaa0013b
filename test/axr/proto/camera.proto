syntax = "proto3";

package frames.camera;

// Placeholder for camera protocol. Allows transition from TCP/IP to protobuf
// based protocol

// *** Request Messages (Host to Receiver) ***

// Request the camera capabilities of the Receiver.
//
// Next ID = 1
message GetCapabilitiesRequest {}

// *** Response Messages (Receiver to Host) ***

// Return the capabilities of the Receiver's camera.
//
// Next ID = 2
message GetCapabilitiesResponse {
  enum CameraTransport {
    CAMERA_TRANSPORT_UNDEFINED = 0;
    // To support the bringup utilization of the TCP_IP protocol
    CAMERA_TRANSPORT_TCP_IP = 1;
    // Long term operation
    CAMERA_TRANSPORT_USES_PROTOCOL = 2;
  }
  CameraTransport camera_transport = 1;
}