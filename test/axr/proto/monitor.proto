syntax = "proto3";

package frames.monitor;

// A ping request message with optional timing information and sample payload.
// Next ID = 3
message PingRequest {
  // Host domain time in ns that the request was sent.
  int64 request_timestamp_ns = 1;

  // Optional sample payload that will be returned in the response.
  bytes sample_payload = 2;
}

// A ping response message with optional timing information and sample payload.
// Next ID = 4
message PingResponse {
  // Fields copied from the ping request.
  PingRequest request_fields = 1;

  // Receiver domain time in ns that the request was received.
  int64 request_received_timestamp_ns = 2;

  // Receiver domain time in ns that the response was sent.
  int64 response_timestamp_ns = 3;
}
