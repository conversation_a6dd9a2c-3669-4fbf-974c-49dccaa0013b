syntax = "proto3";

package frames.blob;

// A request sent to receive a blob.
// Next ID: 3
message ReceiveBlobRequest {
  // ID of the blob to return.
  BlobId id = 2;
}

// Response containing the requested blob.
// Next ID: 3
message ReceiveBlobResponse {
  BlobInfo blob_info = 1;
  bytes payload = 2;
}

// A request sent to delete one or more blobs.
// Next ID: 3
message DeleteBlobsRequest {
  // IDs of the blobs to delete.
  repeated BlobId ids = 2;
}

// Response after sending DeleteBlobRequest
// Next ID: 1
message DeleteBlobsResponse {}

// Uniquely identifies a blob.
// Next ID: 3
message BlobId {
  // Table the blob belongs to.
  Table table = 1;
  // Uniquely identifies the blob within `table`.
  // Local IDs are not unique across tables.
  int64 local_id = 2;
}

// Information describing a Blob.
// Next ID: 10
message BlobInfo {
  reserved 6;

  // Blob identifier.
  BlobId id = 7;
  // The human readable name for the blob.
  string name = 2;
  // The size of the Blob in bytes. Zero if unknown.
  int64 size = 3;
  // Timestamp when this blob was created relative to the unix epoch.
  int64 created_timestamp_ms = 4;
  // Timestamp when this blob was modified relative to the unix epoch.
  int64 modified_timestamp_ms = 5;
  // MIME type of the blob content.
  string mime_type = 8;
  // Opaque extra bytes to allow OEM extensibility.
  bytes extra = 9;
}

// Tables that contain blobs.
// Next ID: 5
enum Table {
  TABLE_UNKNOWN = 0;
  TABLE_BUGREPORTS = 3;
}

