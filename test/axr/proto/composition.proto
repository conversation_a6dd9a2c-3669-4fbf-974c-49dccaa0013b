syntax = "proto3";

// Messages related to composition, particularly the frame's
// capabilities.  This includes on-frame late-stage reprojection,
// and built-in head-tracking support.  Along with the display
// messages, this conveys enough information for the compositor
// to operate.

package frames.composition;

// Next ID: 1
message GetCapabilitiesRequest {}

// Next ID: 4
message GetCapabilitiesResponse {
  // Late state reprojection
  enum LSR {
    LSR_UNDEFINED = 0;
    LSR_UNSUPPROTED = 1;
    LSR_3DOF_REPROJECTION = 2;
    LSR_6DOF_REPROJECTION = 3; // TODO: Requires more information for its capabilities.
  }
  LSR lsr = 1;

  enum LensCorrection {
    LENS_CORRECTION_UNDEFINED = 0;
    LENS_CORRECTION_NOT_NEEDED = 1; // For testing, or devices requiring little correction.
    LENS_CORRECTION_ON_FRAMES_UNSUPPORTED = 2;
    LENS_CORRECTION_ON_FRAMES_SUPPORTED = 3;
  }
  LensCorrection lens_correction = 2;

  repeated HeadTracking head_tracking = 3;
}

// TODO: Consider moving head tracking to its own proto and
// GetCapabilitiesRequest / Response set HeadTrackingReadMethod only relevant if
// using protocol
enum HeadTrackingReadMethod {
  HEAD_TRACKING_READ_UNDEFINED = 0;
  // Frames will push IMU samples every minimum_period_us
  HEAD_TRACKING_READ_PUSH = 1;
  // Puck will request the latest IMU values as needed (preferred)
  HEAD_TRACKING_READ_PULL = 2;
}

// Next ID: 6
message HeadTracking {
  enum Type {
    HEAD_TRACKING_UNDEFINED = 0;
    HEAD_TRACKING_3DOF = 1;
    HEAD_TRACKING_6DOF =
        2;         // TODO: requires more information for its capabilities.
    IMU_ONLY = 3;  // No processing other than sending raw IMU data
  }

  Type type = 1;
  int64 minimum_period_us = 2; // Minimum time between samples in microseconds.
  int64 maximum_period_us = 3; // Maximum time between samples in microseconds.

  enum HeadTrackingTransport {
    HEAD_TRACKING_TRANSPORT_UNDEFINED = 0;
    // To support the bringup utilization of the TCP_IP protocol
    HEAD_TRACKING_TRANSPORT_TCP_IP = 1;
    // Long term operation
    HEAD_TRACKING_TRANSPORT_USES_PROTOCOL = 2;
  }

  HeadTrackingTransport head_tracking_transport = 4;
  HeadTrackingReadMethod head_tracking_read_method = 5;
}

// NEXT ID: 3
// Sets read method if using protocol
message SetHeadTrackingReadMethod {
  HeadTrackingReadMethod head_tracking_read_method = 1;
  int64 sampling_period_us =
      2;  // Frequency at which frames should send IMU data to headset (if push)
}