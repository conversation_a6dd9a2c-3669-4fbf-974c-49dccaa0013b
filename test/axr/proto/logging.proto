syntax = "proto3";

package frames.logging;

// Next ID = 1
message GetCapabilitiesRequest {}

// Next ID = 3
message WatchLogsRequest {
  // Minimum log severity for this stream of log entries.
  // If not set (UNSPECIFIED) - all logs will be sent.
  LogSeverity min_log_severity = 1;
  // Minimum timestamp in milliseconds from epoch time for log entry to be sent
  // through this stream. If not set (or set to zero) - stream will start from
  // the point when the receiver got the message. Timestamp is in the device
  // time.
  int64 min_timestamp_ms = 2;
}

// *** Responses (receiver to host) ***

// Return the logging capabilities of the receiver.
// Next ID = 2
message GetCapabilitiesResponse {
  repeated LogSeverity supported_log_severities = 1;
}

// Next ID = 2
message WatchLogsResponse {
  LogEntry entry = 1;
}

// *** Logging Internal Messages ***

// List is inspired by system/libbase/include/android-base/logging.h.
// No direct cast through the underlying integer should be done.
enum LogSeverity {
  LOG_SEVERITY_UNSPECIFIED = 0;
  LOG_SEVERITY_VERBOSE = 1;
  LOG_SEVERITY_DEBUG = 2;
  LOG_SEVERITY_INFO = 3;
  LOG_SEVERITY_WARNING = 4;
  LOG_SEVERITY_ERROR = 5;
  LOG_SEVERITY_FATAL = 6;
}

// Next ID = 5
message LogEntry {
  string tag = 1;
  int64 timestamp_ms = 2;  // Timestamp is in the device time.
  LogSeverity log_severity = 3;
  string msg = 4;
}
