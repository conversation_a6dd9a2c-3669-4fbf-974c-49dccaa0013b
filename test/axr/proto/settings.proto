syntax = "proto3";

package frames.settings;

// Contains the time and timezone that to set the device's system time.
// Next ID: 3
message SystemTime {
  /// The time in milliseconds since epoch.
  int64 timestamp_ms = 1;
  // The ID of the timezone, see java.util.TimeZone#getID().
  string time_zone = 2;
}

// Contains the system clock state to set on the device.
// Next ID: 2
message SetSystemTimeRequest {
  SystemTime system_time = 1;
}

// Response for SetSystemTimeRequest.
message SetSystemTimeResponse {}

