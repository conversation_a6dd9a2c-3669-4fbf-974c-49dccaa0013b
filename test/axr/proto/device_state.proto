syntax = "proto3";

package frames.device_state;

// *** Requests (host to device) ***

// Next ID = 1
message GetCapabilitiesRequest {}

// Next ID = 1
message ObserveStateRequest {}

// *** Responses (Receiver to Host) ***

// Response from DeviceState::GetCapabilities.
//
// Contains the list of keys supported by the device.
//
// Next ID = 2
message GetCapabilitiesResponse {
  // The list of keys supported by the device, along with their metadata.
  // This map is constant over the lifetime of a connection.
  map<uint32, KeyMetadata> keys = 1;
}

// Response from DeviceState::ObserveState.
//
// Contains a key and its updated value.
//
// Next ID = 3
message ObserveStateResponse {
  // The key that this update is for.
  uint32 key = 1;

  // The new value associated with the key.
  Value value = 2;
}

// *** Other Messages ***

// Metadata about a specific key. Does not change over the lifetime of a
// connection.
//
// Next ID = 2
message KeyMetadata {
  // True if the value of this key will remain constant during the connection.
  bool is_constant = 1;
}

// Enumeration of all "well-known" keys. These are the keys that are defined
// in the protocol.
//
// Next ID = 9
enum WellKnownKey {
  WELL_KNOWN_KEY_UNSPECIFIED = 0;

  // Current state of the device battery with respect to charging.
  WELL_KNOWN_KEY_BATTERY_CHARGE = 1;

  // Constant data about the device hardware.
  WELL_KNOWN_KEY_PRODUCT_INFO = 2;

  // Current brightness level for the display.
  WELL_KNOWN_KEY_DISPLAY_BRIGHTNESS = 3;

  // Current system audio volume level.
  WELL_KNOWN_KEY_AUDIO_VOLUME = 4;

  // Current system audio mute setting.
  WELL_KNOWN_KEY_AUDIO_MUTE = 5;

  // Current device worn/not worn state.
  WELL_KNOWN_KEY_DEVICE_WEAR_STATE = 6;

  // Constant data about the device software version.
  WELL_KNOWN_KEY_SOFTWARE_INFO = 7;

  // Current state of auto brightness.
  WELL_KNOWN_KEY_AUTO_BRIGHTNESS_MODE = 8;

  // All device-specific keys must be greater than or equal to this value.
  WELL_KNOWN_KEY_MIN_DEVICE_SPECIFIC = 100000;
}

// A specific value associated with a key. The actual filled-in message is
// fully dependent on the key it is associated with.
//
// Next ID = 10
message Value {
  oneof value {
    // Catch-all value for keys that don't have a protocol-defined message
    // associated with them. This is used for device-specific keys.
    DeviceValue other = 1;

    // Well-known keys, with a message type defined in the protocol.
    BatteryChargeValue battery_charge = 2;
    ProductInfoValue product_info = 3;
    DisplayBrightnessValue display_brightness = 4;
    AudioVolumeValue audio_volume = 5;
    AudioMuteValue audio_mute = 6;
    DeviceWearStateValue device_wear_state = 7;
    SoftwareInfoValue software_info = 8;
    AutoBrightnessModeValue auto_brightness_mode = 9;
  }
}

// *** Well-known key value messages ***

// BATTERY_CHARGE: Contains the current state of the battery with respect
// to charging.
//
// Next ID = 3
message BatteryChargeValue {
  // The current battery charge level, as a percentage (from 0 to 100).
  uint32 charge_level = 1;

  // Whether the battery is currently charging.
  bool is_charging = 2;
}

// PRODUCT_INFO: Constant, unchanging information about the device hardware.
//
// Next ID = 5
message ProductInfoValue {
  // The name of the device hardware.
  //
  // This should be used only for identification, and not for determining
  // device capabilities.
  string product_name = 1;

  // The serial number of the device.
  string serial_number = 2;  // [(datapol.semantic_type) = ST_HARDWARE_ID]

  // The human-readable model name of the device.
  //
  // This should be used only for identification, and not for determining
  // device capabilities.
  string model_name = 3;

  // The fingerprint string that uniquely identifies the build running on the
  // device.
  string fingerprint = 4; // [(datapol.semantic_type) = ST_SOFTWARE_ID]
}

// DISPLAY_BRIGHTNESS: Contains the current state of the display brightness.
//
// Next ID = 2
message DisplayBrightnessValue {
  // The brightness level value for all displays, as a percentage (0 to 100).
  uint32 value = 1;
}

// AUDIO_VOLUME: Contains the current state of the system audio volume.
//
// Next ID = 2
message AudioVolumeValue {
  // The system audio playback volume level, as a percentage (0 to 100).
  uint32 value = 1;
}

// AUDIO_MUTE: Contains the current state of the system audio mute.
//
// This is included separately from `AudioVolumeValue` so that a system mute
// event does not erase the current volume information for the unmute event.
//
// **N.B.**: This implies a difference between system mute and system
// volume at 0, even if those states are interpreted to be the same thing.
//
// Next ID = 2
message AudioMuteValue {
  // The system audio mute value.
  bool value = 1;
}

// DEVICE_WEAR_STATE: Contains the current worn/not worn state of the device.
//
// Next ID = 2
message DeviceWearStateValue {
  // True if the device is currently being worn.
  bool is_worn = 1;
}

// SOFTWARE_INFO: Contains information about the versions of software running on
// the device. This info may change after a reboot.
//
// Next ID = 2
message SoftwareInfoValue {
  // Operating system version string. For example, an android build string.
  //
  // This should be used only for identification, and not for determining
  // device capabilities. Example value: UQ1A.240205.002
  string os_build_id = 1;
}

// AUTO_BRIGHTNESS_MODE: Current state of auto brightness on the device.
//
// Next ID = 2
message AutoBrightnessModeValue {
  // True if auto brightness is enabled
  bool is_enabled = 1;
}

// Device-specific value.
// Compatible with google.protobuf.Any.
// https://github.com/protocolbuffers/protobuf/blob/main/src/google/protobuf/any.proto
// Next ID = 3;
message DeviceValue {
  string type_url = 1;
  bytes value = 2;
}

