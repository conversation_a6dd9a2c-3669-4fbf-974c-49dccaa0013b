syntax = "proto3";

package frames.display;

import "blob.proto";

// *** Request Messages (Host to Receiver) ***

// Request the display capabilities of the Receiver.
//
// Next ID = 1
message GetCapabilitiesRequest {}

// Payload to request a display brightness value change.
//
// Next ID = 2
message SetBrightnessRequest {
  // The brightness level value for all displays, as a percentage (0 to 100).
  uint32 brightness = 1;
}

// Payload to request a display auto brightness mode change.
//
// Next ID = 2
message SetAutoBrightnessModeRequest {
  // Enable auto brightness.
  bool is_enabled = 1;
}

// *** Response Messages (Receiver to Host) ***

// Return the capabilities of the Receiver's display.
//
// Next ID = 5
message GetCapabilitiesResponse {
  // List of display profiles supported by the Receiver.
  repeated DisplayProfile display_profiles = 1;

  // Indicates if the display brightness is adjustable.
  //
  // If false, Display::SetBrightness must always return an error and have no
  // user-observable side effects.
  bool adjustable_brightness = 3;

  // If false, Display::SetAutoBrightnessMode must always return an error
  // and have no user-observable side effects.
  bool auto_brightness = 4;
}


// Result of a display brightness set operation.
//
// Next ID = 1
message SetBrightnessResponse {}

// Result of an auto brightness mode set operation.
//
// Next ID = 1
message SetAutoBrightnessModeResponse {}

// *** Other Messages ***

// Display profiles supported by the Receiver device.
//
// Next ID = 20
message DisplayProfile {
  // Rank used to indicate profile preference, with '0' being most desirable.
  int32 rank = 1;

  // Display width in pixels.
  //
  // For binocular displays, this represents the combined width of each eye.
  // That is, double the width of a single eye.
  int32 width_px = 2;

  // Display height in pixels.
  int32 height_px = 3;

  // Pixel density in pixels per inch.
  //
  // This is defined relative to the display's pixels per degree (PPD). We
  // define a device with PPD = 30 to have DPI = 160. That is, DPI can be
  // computed from FOV as follows, where PPD is computed from the dimension with
  // the smallest pixel density:
  // ```text
  //    DPI = (PPD/30) * 160
  //    PPD = min(width_px / horizontal_fov_degrees,
  //              height_px / vertical_fov_degress)
  // ```
  // For binocular displays, we use the width of a single eye:
  // ```text
  //    PPD = min((width_px/2) / horizontal_fov_degrees,
  //              height_px / vertical_fov_degress)
  // ```
  // If the DPI and FOV fields are both set, they must agree according to the
  // above formula.
  int32 dpi = 4;

  // Horizontal field of view in degrees.
  //
  // This is the angle from the eye between the left-most active pixel and
  // right-most active pixel on the projection plane. For binocular displays,
  // this is the FOV of a single eye only, where the view frustums are angled
  // inwards to intersect the projection plane.
  float horizontal_fov_degrees = 5;

  // Vertical field of view in degrees.
  //
  // This is the angle from the eye between the top-most active pixel and
  // bottom-most active pixel on the projection plane.
  float vertical_fov_degrees = 6;

  // The interpupillary distance (IPD) in millimeters.
  //
  // This is the IPD of the wearer, if known.
  //
  // Otherwise, we estimate IPD from the location of the displays. For binocular
  // devices, this is the horizontal distance between the centers of the
  // displays, measured as if the displays had no angular offset. For monocular
  // devices, this is measured as if there were two displays (that is, twice the
  // distance of the device center to the display center).
  int32 ipd_mm = 8;

  // The focal length of the display in millimeters.
  //
  // The distance to the virtual image produced by the virtual image optics.
  // This value is not influenced by the image contents (that is, no parallax in
  // the case of a stereo pair).
  int32 focal_length_mm = 9;

  // Angular offset of the left eye.
  //
  // Not present if the left eye has no display.
  DisplayAngularOffset left_eye_offset = 10;

  // Angular offset of the right eye.
  //
  // Not present if the right eye has no display.
  DisplayAngularOffset right_eye_offset = 11;

  // Distortion mesh for the left eye.
  //
  // Not present if the left eye has no display.
  DistortionMesh left_eye_distortion_mesh = 12;

  // Distortion mesh for the right eye.
  //
  // Not present if the right eye has no display.
  DistortionMesh right_eye_distortion_mesh = 13;

  // The refresh rates supported by this profile (HZ).
  repeated int32 refresh_rates = 14;

  // Frame illumination start offsets (same order as refresh rates)
  repeated int32 illumination_start_offset_ns = 18;

  // Frame illumination end offsets (same order as refresh rates)
  repeated int32 illumination_end_offset_ns = 19;

  // The output colorspace expected by the display
  enum ColorSpace {
    COLOR_SPACE_UNSPECIFIED = 0;
    COLOR_SPACE_RGB_888 = 1;
    COLOR_SPACE_YUV_422 = 2;
    COLOR_SPACE_YUV_420 = 3;
  }
  ColorSpace color_space = 15;

  // The transport used for display. Currently only DP ALT MODE is supported
  enum DisplayTransport {
    DISPLAY_TRANSPORT_UNSPECIFIED = 0;
    DISPLAY_PORT_ALT_MODE = 1;
    USB_VIDEO_ENCODED = 2;
  }
  DisplayTransport transport = 16;
  DisplayPortAltModeTransport dp_alt_mode  = 17;
}

// Next ID = 3
message DisplayPortAltModeTransport {
  // Display Port Version (1.2 , 1.4 , 2.0 etc.)
  string version = 1;
  // Display Stream Compression support
  bool display_stream_compression_support = 2;
}


// Angular offset of a display.
//
// These values define a vector which points from the eye to the center of the
// display. This is a two-dimensional pair (horizontal offset, vertical offset),
// in degrees. Positive values are offset to the top/right. Negative values are
// offset to the bottom/left.
//
// A display with angular offset (0,0), i.e. no offset, is centered on a vector
// pointing straight out of the eye. A display with offset (+5,-5) is centered
// in the bottom-right quadrant of the wearer's vision.
//
// Next ID = 3
message DisplayAngularOffset {
  // Horizontal offset in degrees.
  float horizontal_offset_degrees = 1;

  // Vertical offset in degrees.
  float vertical_offset_degrees = 2;
}


// Distortion mesh used for geometry correction.
//
// Next ID = 3
message DistortionMesh {
  /// Specifies the file structure of the distortion mesh.
  enum Format {
    FORMAT_UNSPECIFIED = 0;
    // V1 mesh format that supports chromatic aberration correction.
    //
    // Mesh with triangle strip topology is stored as a sequence of:
    // [vertex_count][vertices][index_count][indices]
    // where:
    // - vertex_count: number of vertices in the mesh, as an int32
    // - vertices: array of vertex data. Each vertex contains 4 float32 pairs
    // - index_count: number of indices in the mesh, as an int32
    // - indices: array of indices, each index is an int16
    //
    // Each vertex contains 4 pairs of float32 values:
    // - position:  float32 (x,y) pair for destination location
    // - texCoordR: float32 (x,y) pair for red channel source location
    // - texCoordG: float32 (x,y) pair for green channel source location
    // - texCoordB: float32 (x,y) pair for blue channel source location
    FORMAT_V1_CBC = 1;
  }
  Format format = 1;

  // Blob info for the distortion mesh binary file.
  frames.blob.BlobInfo blob_info = 2;
}

