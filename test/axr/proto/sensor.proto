syntax = "proto3";

package frames.sensor;

///////////////////////////////////////////////////////////////
// NOTE: Messages available in SC protocol using the same name
///////////////////////////////////////////////////////////////

// *** Requests (host to device) ***

// Next ID = 1
message GetCapabilitiesRequest {}

// Request a stream of samples from a set of sensors.
//
// Only one stream may be active at a time. If one stream is active when this
// is requested again, the server will close the existing stream.
//
// The server should, whenever possible, do this atomically to avoid dropping
// any samples for a given sensor when both the old and new streams contain
// that sensor.
//
// All sensor data goes over a single stream to facilitate batching samples
// across sensors into the same message. When sampling sensors at a high rate,
// this can provide significant bandwidth savings.
//
// Next ID = 2
message ReceiveSensorStreamRequest {
  // Requested properties for a single sensor's stream.
  //
  // Next ID = 4
  message SensorStreamRequestParameters {
    // Requested sample rate in microseconds.
    //
    // This rate is a hint only. The actual sample rate may be different,
    // depending on the capabilities of the sensor. A value less than or
    // equal to the `min_sampling_period_us` from the sensor's
    // `SensorCapability` means that samples should be sent as fast as possible.
    int32 sampling_period_us = 1;

    // Requested batch size in microseconds.
    //
    // To fetch samples as quickly as possible, set this to zero. To fetch
    // samples in batches, set this to a larger value. Larger batch sizes
    // decrease overheads and can increase throughput at the cost of latency.
    //
    // This size is a hint only. The server should try to respect this hint but
    // may use a different batch size depending on the capabilities of the
    // sensor and on server policies to limit frequent wakeups.
    int32 batch_size_us = 2;

    // Requested types of additional information.
    //
    // If the sensor supports additional information, this specifies the
    // types of additional information that the client is interested in.
    repeated SensorAdditionalInfoType additional_info_types = 3;
  }

  // A mapping of sensor id to the requested parameters for the sensor.
  //
  // This determines which sensors are active. The id must come from a specific
  // sensor's SensorCapability.
  map<int32, SensorStreamRequestParameters> sensors = 1;
}

// *** Responses (device to host) ***

// Contains the list of sensors supported by the device.
//
// Next ID = 2
message GetCapabilitiesResponse {
  repeated SensorCapability sensors = 1;
}

// Contains a batch of sensor samples. May contain multiple samples from
// the same sensor, multiple samples from different sensors, or both.
//
// Next ID = 2
message ReceiveSensorStreamResponse {
  repeated SensorSample samples = 1;
}

// A single sample from a specific sensor.
//
// Next ID = 6
message SensorSample {
  // The ID of the sensor this sample came from.
  int32 sensor_id = 1;

  // Timestamp of the sample.
  int64 timestamp_ns = 2;

  // The accuracy of the sample.
  // If SENSOR_ACCURACY_UNSPECIFIED, the accuracy is the same as the previous
  // sample for this sensor.
  SensorAccuracy accuracy = 3;

  // Raw sample data from the sensor.
  //
  // See the documentation in SensorCapability.SensorType for
  //
  // See the Android SensorEvent documentation for a description of what each
  // index represents:
  // https://developer.android.com/reference/android/hardware/SensorEvent.html
  repeated float values = 4;

  // If this sample represents additional information from the sensor,
  // the type of the additional information.
  //
  // See the Android SensorAdditionalInfo documentation for a description of
  // what each type represents:
  // https://developer.android.com/reference/android/hardware/SensorAdditionalInfo
  SensorAdditionalInfoType additional_info_type = 5;
}

// Contains the properties of a sensor on the remote device.
//
// Next ID: 11
message SensorCapability {
  // The type of data the sensor provides.
  enum SensorType {
    NONE = 0;

    // Maps to Sensor#TYPE_ACCELEROMETER.
    ACCELEROMETER = 1;

    // Maps to Sensor#TYPE_MAGNETIC_FIELD.
    MAGNETOMETER = 2;

    // Maps to Sensor#TYPE_GYROSCOPE.
    GYROSCOPE = 4;

    // Maps to Sensor#TYPE_LIGHT.
    LIGHT = 5;

    // Maps to Sensor#TYPE_PRESSURE.
    PRESSURE = 6;

    // Maps to Sensor#TYPE_TEMPERATURE.
    TEMPERATURE = 7;

    // Maps to Sensor#TYPE_PROXIMITY.
    PROXIMITY = 8;

    // Maps to Sensor#TYPE_GRAVITY.
    GRAVITY = 9;

    // Maps to Sensor#ROTATION_VECTOR.
    ROTATION_VECTOR = 11;

    // Maps to Sensor#TYPE_AMBIENT_TEMPERATURE.
    AMBIENT_TEMPERATURE = 13;

    // Maps to Sensor#TYPE_MAGNETIC_FIELD_UNCALIBRATED.
    MAGNETOMETER_UNCALIBRATED = 14;

    // Maps to Sensor#TYPE_GAME_ROTATION_VECTOR.
    GAME_ROTATION_VECTOR = 15;

    // Maps to Sensor#TYPE_GYROSCOPE_UNCALIBRATED.
    GYROSCOPE_UNCALIBRATED = 16;

    // Maps to Sensor#STEP_DETECTOR.
    STEP_DETECTOR = 18;

    // Maps to Sensor#GEOMAGNETIC_ROTATION_VECTOR.
    GEOMAGNETIC_ROTATION_VECTOR = 20;

    // Maps to Sensor#ADDITIONAL_INFO.
    ADDITIONAL_INFO = 33;

    // Maps to Sensor#ACCELEROMETER_UNCALIBRATED.
    ACCELEROMETER_UNCALIBRATED = 35;

    // Maps to Sensor#HINGE_ANGLE.
    HINGE_ANGLE = 36;

    // Used in SensorEvent.h but does not map to any type in Android HAL.
    // We use same enum value as in SensorEvent.h.
    INTER_PUPILLARY_DISTANCE = 33171007;
  }

  // A unique identifier for the sensor.
  int32 sensor_id = 1;

  // The type of sensor.
  SensorType sensor_type = 2;

  // Human readable display name for the sensor.
  string name = 3;

  // Vendor string of this sensor.
  string vendor = 4;

  // The range of the sensor in the sensor's unit. This is the maximum magnitude
  // for any component of the sample values.
  float range = 5;

  // Resolution of the sensor in the sensor's unit.
  float resolution = 6;

  // The minimum sampling period allowed between two events in microseconds,
  // or zero if this sensor only returns a value when the data it's measuring
  // changes.
  // Per Android Sensor HAL docs:
  // continuous sensors: the min sample period allowed (us).
  // on-change sensors: 0
  // one-shot sensors: -1
  // special sensors: 0
  int32 min_sampling_period_us = 7;

  // The maximum sampling period for this sensor in microseconds.
  // Per Android Sensor HAL docs:
  // continuous and on-change sensors: max sampling period allowed (us).
  // one-shot and special sensors: 0
  int32 max_sampling_period_us = 8;

  // Whether the sensor supports providing "additional information".
  bool supports_additional_info = 10;
}

// The accuracy of a sensor reading.
enum SensorAccuracy {
  SENSOR_ACCURACY_UNSPECIFIED = 0;

  // The sensor is reporting data with a low level of accuracy.
  // Calibration with the environment is needed.
  SENSOR_ACCURACY_LOW = 1;

  // The sensor is reporting data with an average level of accuracy.
  // Calibration with the environment may improve the readings.
  SENSOR_ACCURACY_MEDIUM = 2;

  // The sensor is reporting data with maximum accuracy.
  SENSOR_ACCURACY_HIGH = 3;
}

// Types of additional information that can be provided by a sensor.
enum SensorAdditionalInfoType {
  SENSOR_ADDITIONAL_INFO_TYPE_UNSPECIFIED = 0;

  // Maps to SensorAdditionalInfo#TYPE_INTERNAL_TEMPERATURE.
  SENSOR_ADDITIONAL_INFO_TYPE_INTERNAL_TEMPERATURE = 65537;

  // Used in SensorEvent.h but does not map to any type in Android HAL.
  // We use same enum value as in SensorEvent.h.
  SENSOR_ADDITIONAL_INFO_TYPE_LOCAL_VELOCITY = 65538;

  // Used in SensorEvent.h but does not map to any type in Android HAL.
  // We use same enum value as in SensorEvent.h.
  SENSOR_ADDITIONAL_INFO_TYPE_UWB_RANGE = 65568;
}
