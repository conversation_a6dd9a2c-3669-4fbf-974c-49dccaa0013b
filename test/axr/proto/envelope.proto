syntax = "proto3";

package frames;

import "audio.proto";
import "blob.proto";
import "camera.proto";
import "composition.proto";
import "device_state.proto";
import "display.proto";
import "logging.proto";
import "monitor.proto";
import "sensor.proto";
import "settings.proto";
import "time.proto";

// ---------------------------------------------------------------------------
// Request-Response Protocol

// This is a request-response protocol, where the host issues requests, and the
// frames device replies with responses.  When the host needs to receive a
// stream of messages from the frames, the host requests activation of that
// stream (e.g., the host requests IMU sample delivery first, and then the
// frames can start delivering the IMU samples).  Upon disconnection, the frames
// and host cancel all pending messages and data streams; and then when they
// reconnect, they must re-negotiate for them to be delivered again.  In most
// cases, the host will ignore unsolicited messages (there will be some explicit
// cases that allow unsolicited messages to the host).

// The request messages include a request ID to uniquely identify the request
// within the current connection.  A response to the request must include
// this same request ID, including data streams (each message in a data stream
// will use the request_id from the last request that started or reconfigured
// the data stream).  This allows the host to know when samples arrive using
// the updated stream configuration.

// If the frames device can't service a request, it can respond with an error
// status and an error message to help with debugging.

// ---------------------------------------------------------------------------
// Wire Framing

// To help determine the start and stop of the encoded bytes on the connection,
// we frame the messages.  Each message is preceded by a header, with the first
// 32-bits (little endian) being the length of the data following the header,
// and the second 32-bits (little endian) identifying the frame type.  The
// frame types, as currently defined, are: None=0, RequestToFrames=1, and
// ResponseFromFrames=2.  In Rust:
//
//     #[repr(C, packed)]
//     pub struct FrameHeader {
//         /// The length of the payload after the header (little endian).
//         pub length: u32,
//         /// The frame type, using the values from the FrameType enum (little endian).
//         pub frame_type: u32,
//     }
//
//     #[repr(u32)]
//     pub enum FrameType {
//         None = 0,
//         ProtobufRequestToFrames = 1,
//         ProtobufResponseFromFrames = 2,
//     }
//

// ---------------------------------------------------------------------------
// More information

// Design document: http://goto.google.com/frames-protocol

// ---------------------------------------------------------------------------

// The current version of this protocol.  It should be incremented when it
// introduces a backwards incompatibility, such as a new message request that
// a peer running an older version would ignore.
enum Versions {
  UNDEFINED_VERSION = 0;
  PROTOCOL_VERSION = 1;
}

// Next ID: 19
message RequestToFrames {
  // Monotonically increasing ID to be returned with the response.
  // Starts at 1 upon connection establishment.
  // A value of 0 is undefined.
  int64 request_id = 1;

  // The possible requests:
  oneof request {
    ProtocolVersionRequest protocol_version_request = 2;
    monitor.PingRequest ping_request = 3;
    settings.SetSystemTimeRequest set_system_time_request = 4;
    device_state.GetCapabilitiesRequest device_state_get_capabilities_request = 5;
    device_state.ObserveStateRequest device_state_observe_state_request = 6;
    display.GetCapabilitiesRequest display_get_capabilities_request = 7;
    display.SetBrightnessRequest display_set_brightness_request = 8;
    display.SetAutoBrightnessModeRequest display_set_auto_brightness_mode_request = 9;
    time.PrecisionTimeProtocolMessage ptp_request = 10;
    logging.GetCapabilitiesRequest logging_get_capabilities_request = 11;
    logging.WatchLogsRequest logging_watch_logs_request = 12;
    composition.GetCapabilitiesRequest composition_get_capabilities_request = 13;
    sensor.GetCapabilitiesRequest sensor_get_capabilities_request = 14;
    sensor.ReceiveSensorStreamRequest sensor_receive_sensor_stream_request = 15;
    blob.ReceiveBlobRequest blob_receive_blob_request = 16;
    audio.GetCapabilitiesRequest audio_get_capabilities_request = 17;
    camera.GetCapabilitiesRequest camera_get_capabilities_request = 18;
  }
}

// Next ID: 21
message ResponseFromFrames {
  // The request_id from the original RequestToFrames message.
  // If there was no request, this is 0.  Data streams report the
  // original request ID that activated the stream (thus if a stream
  // gets reconfigured, it will start sending samples using the new
  // request ID that caused a reconfiguration).
  int64 request_id = 1;

  // The result status for the original RequestToFrames message.
  // This is helpful for propagating error information that prevented proper
  // handling of the request.
  enum Status {
    UNKNOWN = 0;
    SUCCESS = 1;

    // Errors:
    CANCELLED = 2;
    INTERNAL_ERROR = 3;
    INVALID_INPUT = 4; // A parameter was incorrect.
    PERMISSION_DENIED = 5;
    RESOURCE_EXHAUSTED = 6;
    TIMED_OUT = 7;
    UNAVAILABLE = 8; // Frames shutting down.
    UNIMPLEMENTED = 9;
  }
  Status status = 2;
  string error_message = 3;

  // Possible responses:
  oneof response {
    ProtocolVersionResponse protocol_version_response = 4;
    monitor.PingResponse ping_response = 5;
    settings.SetSystemTimeResponse set_system_time_response = 6;
    device_state.GetCapabilitiesResponse device_state_get_capabilities_response = 7;
    device_state.ObserveStateResponse device_state_observe_state_response = 8;
    display.GetCapabilitiesResponse display_get_capabilities_response = 9;
    display.SetBrightnessResponse display_set_brightness_response = 10;
    display.SetAutoBrightnessModeResponse display_set_auto_brightness_mode_response = 11;
    time.PrecisionTimeProtocolMessage ptp_response = 12;
    logging.GetCapabilitiesResponse logging_get_capabilities_response = 13;
    logging.WatchLogsResponse logging_watch_logs_response = 14;
    composition.GetCapabilitiesResponse composition_get_capabilities_response = 15;
    sensor.GetCapabilitiesResponse sensor_get_capabilities_response = 16;
    sensor.ReceiveSensorStreamResponse sensor_receive_sensor_stream_response = 17;
    blob.ReceiveBlobResponse blob_receive_blob_response = 18;
    audio.GetCapabilitiesResponse audio_get_capabilities_response = 19;
    camera.GetCapabilitiesResponse camera_get_capabilities_response = 20;
  }
}


// Manage the active protocol version.
// Next ID: 4
message ProtocolVersionRequest {
  enum Command {
    QUERY = 0; // Query for the current version.
    ACTIVATE = 1; // Change the protocol version.
  }

  Command command = 1;
  int32 desired_version = 2;

  // The unique identification of the build running on the host.
  // Since an Android host can have multiple partitions from independent
  // installations, this is an array of fingerprints from each of those partitions.
  // See https://developer.android.com/reference/android/os/Build#getFingerprintedPartitions()
  repeated string host_software_fingerprints = 3;
}

// Protocol-version management response.
// Next ID: 3
message ProtocolVersionResponse {
  // Each version is an integer, with newer versions > older versions.
  repeated int32 supported_versions = 1;
  int32 active_version = 2;
}

