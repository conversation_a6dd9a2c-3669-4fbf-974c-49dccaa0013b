#include <grpcpp/ext/proto_server_reflection_plugin.h>
#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>

#include <iostream>
#include <memory>
#include <string>

#include "absl/flags/flag.h"
#include "absl/flags/parse.h"
#include "absl/strings/str_format.h"

#include "service.grpc.pb.h"  

ABSL_FLAG(uint16_t, port, 50051, "Server port for the service");

using grpc::CallbackServerContext;
using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerBidiReactor;
using grpc::Status;

using frames::service::Frames;
using frames::RequestToFrames;
using frames::ResponseFromFrames;

class StreamingReactor final : public ServerBidiReactor<RequestToFrames, ResponseFromFrames> {
 public:
  StreamingReactor() {
    std::cout << "StreamingReactor. StartRead begin" << std::endl;
    StartRead(&request_);
    std::cout << "StreamingReactor. StartRead end" << std::endl;
  }

  void OnReadDone(bool ok) override {
    if (!ok) {
      Finish(Status::OK);
      return;
    }

    std::cout << "Received RequestToFrames." << std::endl;

    ResponseFromFrames response;
   // response.set_timestamp(GetMonotonicTimeNs()); 
    StartWrite(&response);
  }

  void OnWriteDone(bool ok) override {
    if (!ok) {
      Finish(Status::OK);
      return;
    }

    StartRead(&request_);
  }

  void OnDone() override {
    delete this;
  }

 private:
  RequestToFrames request_;
};


class FramesServiceImpl final : public Frames::CallbackService {
 public:
  ServerBidiReactor<RequestToFrames, ResponseFromFrames>*
  StartStreaming(CallbackServerContext* context) override {
    return new StreamingReactor();
  }
};

void RunServer(uint16_t port) {
  std::string server_address = absl::StrFormat("0.0.0.0:%d", port);
  FramesServiceImpl service;

  grpc::EnableDefaultHealthCheckService(true);
  grpc::reflection::InitProtoReflectionServerBuilderPlugin();

  ServerBuilder builder;
  builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
  builder.RegisterService(&service);

  std::unique_ptr<Server> server(builder.BuildAndStart());
  std::cout << "Server listening on " << server_address << std::endl;
  server->Wait();
}

int main(int argc, char** argv) {
  absl::ParseCommandLine(argc, argv);
  RunServer(absl::GetFlag(FLAGS_port));
  return 0;
}
