#include <grpcpp/grpcpp.h>

#include <chrono>
#include <iostream>
#include <memory>
#include <random>
#include <string>
#include <thread>

#include "service.grpc.pb.h" 

#include "usb_client.h"
#include "common.h"

using grpc::Channel;
using grpc::ClientContext;
using grpc::Status;
using grpc::ClientReaderWriter;

using frames::service::Frames;
using frames::RequestToFrames;
using frames::ResponseFromFrames;
using frames::sensor::GetCapabilitiesRequest;
using frames::sensor::ReceiveSensorStreamRequest;

class StreamingClient {
public:
  explicit StreamingClient(std::shared_ptr<Channel> channel)
      : stub_(Frames::NewStub(channel)) {}

  void RunStream() {
    int64_t request_id =1;
    ClientContext context;
    std::shared_ptr<ClientReaderWriter<RequestToFrames, ResponseFromFrames>> stream(
        stub_->StartStreaming(&context));

    RequestToFrames req1;
    req1.set_request_id(request_id);
    request_id++;
    auto* caps = req1.mutable_sensor_get_capabilities_request(); 
    //caps->set_device_id("camera_01");
    stream->Write(req1);
    LOG_INFO("[Sent] SensorGetCapabilitiesRequest");

    std::this_thread::sleep_for(std::chrono::seconds(1));

    ResponseFromFrames response;
    if(stream->Read(&response)) {
      LOG_INFO("[Received] {}", response.DebugString().c_str());
      if(response.response_case() == ResponseFromFrames::kSensorGetCapabilitiesResponse) {
        LOG_INFO("Received GetCapabilitiesResponse");
        const frames::sensor::GetCapabilitiesResponse& caps_respnse = response.sensor_get_capabilities_response();

        RequestToFrames req2;
        req2.set_request_id(request_id);
        request_id++;
        auto* stream_req = req2.mutable_sensor_receive_sensor_stream_request();
        for (const auto& sensor : caps_respnse.sensors()) {
          frames::sensor::ReceiveSensorStreamRequest::SensorStreamRequestParameters param;
          param.set_sampling_period_us(sensor.min_sampling_period_us());
          // param.set_batch_size_us(30000);    
          // param.add_additional_info_types(frames::sensor::SENSOR_ADDITIONAL_INFO_TYPE_UNSPECIFIED);
          (*stream_req->mutable_sensors())[sensor.sensor_id()] = std::move(param);
          // std::cout << "Sensor ID: " << sensor.sensor_id() << std::endl;
          // std::cout << "Sensor Name: " << sensor.name() << std::endl;
          // std::cout << "Sensor Vendor: " << sensor.vendor() << std::endl;
          // std::cout << "Sensor Type: " << sensor.sensor_type() << std::endl;
          // std::cout << "Range: " << sensor.range() << std::endl;
          // std::cout << "Resolution: " << sensor.resolution() << std::endl;
          // std::cout << "Min Sampling Period (us): " << sensor.min_sampling_period_us() << std::endl;
          // std::cout << "Max Sampling Period (us): " << sensor.max_sampling_period_us() << std::endl;
          // std::cout << "Supports Additional Info: " << (sensor.supports_additional_info() ? "true" : "false") << std::endl;
        }
        
        stream->Write(req2);
        LOG_INFO("[Sent] SensorReceiveStreamRequest");

        if(stream->Read(&response)) {
          LOG_INFO("[Received] {}", response.DebugString().c_str());
          if(response.response_case() == ResponseFromFrames::kSensorReceiveSensorStreamResponse) {
            LOG_INFO("Received ReceiveSensorStreamResponse");
          }
          else{
            LOG_ERROR("[Received] after send SensorReceiveStreamRequest, Unknown or unhandled response type: {}", response.DebugString());
          }
        }
      }
      else{
        LOG_ERROR("[Received] after send SensorGetCapabilitiesRequest, Unknown or unhandled response type: {}", response.DebugString());
      }
    }

    std::this_thread::sleep_for(std::chrono::seconds(4));

    //停止imu
    RequestToFrames stop_imu_req;
    stop_imu_req.set_request_id(request_id);
    request_id++;
    auto* stream_req = stop_imu_req.mutable_sensor_receive_sensor_stream_request();
    stream->Write(stop_imu_req);
    LOG_INFO("[Sent] Stop SensorReceiveStreamRequest begin");
    if(stream->Read(&response)) {
      LOG_INFO("Stop [Received] {}", response.DebugString().c_str());
      if(response.response_case() == ResponseFromFrames::kSensorReceiveSensorStreamResponse) {
        LOG_INFO("Stop Received ReceiveSensorStreamResponse");
      }
      else{
        LOG_ERROR("Stop [Received] after send SensorReceiveStreamRequest, Unknown or unhandled response type: {}", response.DebugString());
      }
    }

    LOG_INFO("[Sent] Stop SensorReceiveStreamRequest end");

    stream->WritesDone();
    Status status = stream->Finish();
    if (status.ok()) {
      LOG_INFO("Stream finished successfully.");
    } else {
      LOG_ERROR("Stream failed: {}", status.error_message());
    }
  }

private:
  std::unique_ptr<Frames::Stub> stub_;
};

int main(int argc, char** argv) {

  StartUsbClient(0,true);

  std::string target_str = "169.254.2.1:50051";
  if (argc >= 2) {
    target_str = argv[1];
  }

  LOG_INFO("Connecting to: {}", target_str);
  auto channel = grpc::CreateChannel(target_str, grpc::InsecureChannelCredentials());
  StreamingClient client(channel);

  client.RunStream();

  return 0;
}
