message(STATUS "CMAKE_SOURCE_DIR = ${CMAKE_SOURCE_DIR}")
message(STATUS "CMAKE_BINARY_DIR = ${CMAKE_BINARY_DIR}")

find_path(LIBUSB_INCLUDE_DIR
    NAMES libusb.h
    PATHS 
        "${CMAKE_SOURCE_DIR}/external/libusb-cmake/libusb/libusb"
    REQUIRED
)

set(HIDAPI_DIR ${CMAKE_SOURCE_DIR}/external/hidapi)

if(ANDROID)
    set(HIDAPI_LIB hidapi_libusb)   
elseif(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    set(HIDAPI_LIB hidapi_hidraw)  
elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
    set(HIDAPI_LIB hidapi_darwin)     
elseif(CMAKE_SYSTEM_NAME STREQUAL "Windows")
    set(HIDAPI_LIB hidapi_winapi) 
else()
    message(FATAL_ERROR "Unsupported platform: ${CMAKE_SYSTEM_NAME}")
endif()

include_directories(
    "${CMAKE_BINARY_DIR}/test/env"
    ${LIBUSB_INCLUDE_DIR}
    ${HIDAPI_DIR}/hidapi
    include
)

# 源文件列表（除 main.cpp）
set(LIB_SRC_FILES
    src/async_usb.cpp
    src/tcp_heartbeat.cpp
    src/hid_device.cpp
    src/imu_data_converter.cpp
    src/imu_device.cpp
    src/heartbeat_device.cpp
    src/usb_client.cpp)

# 只用于构建最终 bin 的 main.cpp
set(MAIN_SRC_FILE
    src/main.cpp)

# 链接项
set(LINK_ITEMS        
    usb-1.0
    ${HIDAPI_LIB})

if(USE_FRAMEWORK)
    message(STATUS "USE_FRAMEWORK")
    list(APPEND LINK_ITEMS
        framework::framework)
else()
    message(STATUS "not USE_FRAMEWORK")
    include_directories(
        ${CMAKE_SOURCE_DIR}/external/fmt/include
        ${CMAKE_SOURCE_DIR}/external/jsoncpp/include
    )
    list(APPEND LINK_ITEMS
        fmt::fmt
        jsoncpp_static
        perfetto::perfetto)
endif()

if (WIN32)
    list(APPEND LINK_ITEMS
        advapi32
        setupapi
    )
    if(MINGW)
        list(APPEND LINK_ITEMS
            ws2_32
        )
    endif()
endif()

# ✅ 添加静态库 target
add_library(RawUsbClientLib STATIC
    ${LIB_SRC_FILES}
)

target_link_libraries(RawUsbClientLib PUBLIC
    ${LINK_ITEMS}
)

target_include_directories(RawUsbClientLib PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    "${CMAKE_BINARY_DIR}/test/env"
)

# ✅ 第一个可执行文件，使用静态库 + main.cpp
add_executable(RawUsbClient
    ${MAIN_SRC_FILE}
)

target_link_libraries(RawUsbClient
    RawUsbClientLib
)

# ✅ 第二个可执行文件，同样使用静态库 + main.cpp
add_executable(RawUsbClient_all_symbols
    ${MAIN_SRC_FILE}
)

target_link_libraries(RawUsbClient_all_symbols
    RawUsbClientLib
)

PROJECT_STRIP_LINK_OPTIONS(RawUsbClient RawUsbClient_all_symbols "symbol_type_null" FALSE)
