#include "system_config.h"

#if defined(TEST_SYSTEM_WINDOWS) 
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <windows.h>
    #include <io.h>        // 替代 unistd.h 中的 close()
#else
    #include <arpa/inet.h>
    #include <netinet/in.h>
    #include <netinet/tcp.h>
    #include <sys/socket.h>
    #include <unistd.h>
    #include <signal.h>
#endif

#include <chrono>
#include <cstring>
#include <iostream>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <signal.h>
#include <time.h>
#include "data_types.h"
#include "common.h"

#include "tcp_heartbeat.h"



static constexpr uint64_t DELTA_NS = OS_NS_PER_SEC / 10;

std::queue<uint64_t> t1_queue;
std::mutex queue_mutex;
std::condition_variable queue_cv;
bool stop = false;

int ConnectSocket(const char* ip, uint16_t port) {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == -1) {
        perror("socket");
        return -1;
    }

    sockaddr_in addr {};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    inet_pton(AF_INET, ip, &addr.sin_addr);

    if (connect(sock, (sockaddr*)&addr, sizeof(addr)) == -1) {
        perror("connect");
        close(sock);
        return -1;
    }

    int flag = 1;
    setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, (const char*)&flag, sizeof(flag));

    int bufsize = 128;
    setsockopt(sock, SOL_SOCKET, SO_SNDBUF, (const char*)&bufsize, sizeof(bufsize));
    setsockopt(sock, SOL_SOCKET, SO_RCVBUF, (const char*)&bufsize, sizeof(bufsize));


    return sock;
}

inline bool SendAndRecvHeartBeat(int sock,uint64_t t1) {
    FrameHeader frame_header;
    frame_header.len = sizeof(t1);
    frame_header.type = FrameType::Heartbeat;
    std::vector<uint8_t> send_buf;
    send_buf.resize(sizeof(frame_header) + sizeof(t1));
    memcpy(send_buf.data(), &frame_header, sizeof(frame_header));
    memcpy(send_buf.data() + sizeof(frame_header), &t1, sizeof(t1));

#ifdef TEST_SYSTEM_WINDOWS
    int sent = send(sock, reinterpret_cast<const char*>(send_buf.data()), static_cast<int>(send_buf.size()), 0);
    if (sent != (int)send_buf.size()) {
        LOG_ERROR("send failed: {}", WSAGetLastError());
        return false;
    }
#else
    if (write(sock, send_buf.data(), send_buf.size()) != (ssize_t)send_buf.size()) {
        LOG_ERROR("write failed");
        return false;
    }
#endif

    FrameHeader rev_frame_header;
    TimeResonse reply;
#ifdef TEST_SYSTEM_WINDOWS
    int n = recv(sock, reinterpret_cast<char*>(&rev_frame_header), sizeof(rev_frame_header), MSG_WAITALL);
    if (n != sizeof(rev_frame_header)) {
        LOG_ERROR("recv header failed: {}", WSAGetLastError());
        return false;
    }

    n = recv(sock, reinterpret_cast<char*>(&reply), sizeof(reply), MSG_WAITALL);
    if (n != sizeof(reply)) {
        LOG_ERROR("recv body failed: {}", WSAGetLastError());
        return false;
    }
#else
    if (read(sock, &rev_frame_header, sizeof(rev_frame_header)) != (ssize_t)sizeof(rev_frame_header)) {
        LOG_ERROR("read header failed");
        return false;
    }
    if (read(sock, &reply, sizeof(reply)) != (ssize_t)sizeof(reply)) {
        LOG_ERROR("read body failed");
        return false;
    }
#endif


    uint64_t t1_rev = reply.t1;
    if(t1 != t1_rev) {
        LOG_ERROR("t1:{} and t1_rev:{} not equal", t1, t1_rev);
    }
    uint64_t t2 = reply.t2;
    uint64_t t3 = reply.t3;
    uint64_t t4 = FMonotonicGetNs();

    LOG_INFO("tcp_heart_beat:{},{},{},{}", t1_rev, t2, t3, t4);

    return true;
}

void SenderThread(int sock) {
    while (!stop) {

       // LOG_INFO("SenderThread 1");
        uint64_t t1 = 0;

        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            queue_cv.wait(lock, [] { return !t1_queue.empty() || stop; });

           // LOG_INFO("SenderThread 2");


            if (stop) break;

            t1 = t1_queue.front();
            if(t1_queue.size() > 1) {
                std::cout << "t1:" << t1 << ",t1_queue.size:" << t1_queue.size() << std::endl;
            }
            t1_queue.pop();
        }

        SendAndRecvHeartBeat(sock,t1);
    }
}

void RunTcpHeartbeat() {

#ifdef TEST_SYSTEM_WINDOWS
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "WSAStartup failed." << std::endl;
        return;
    }
#endif
    //signal(SIGPIPE, SIG_IGN);
    LOG_INFO("RunTcpHeartbeat");
    const char* ip = "***********";

    int sock = ConnectSocket(ip, 50080);
    if (sock < 0){
        LOG_ERROR("connect socket failed");
        return;
    }
    LOG_INFO("RunTcpHeartbeat sock:{}", sock);

    uint64_t app_ns = FMonotonicGetNs();

#if USE_SYNC_TCP_HEART_BEAT
    LOG_INFO("USE_SYNC_TCP_HEART_BEAT");
#else
    LOG_INFO("Not USE_SYNC_TCP_HEART_BEAT");
    std::thread sender(SenderThread, sock);
#endif
    
    LOG_INFO("start loop");

    while (true) {
        uint64_t real_ns = FMonotonicGetNs();
        app_ns += DELTA_NS;
        uint64_t t1 = real_ns;

#if USE_SYNC_TCP_HEART_BEAT
        SendAndRecvHeartBeat(sock,t1);
#else
        {
            std::lock_guard<std::mutex> lock(queue_mutex);
            t1_queue.push(t1);
            queue_cv.notify_one();
        }
#endif

        FNanoSleep(app_ns - real_ns);
    }

#if !USE_SYNC_TCP_HEART_BEAT
    stop = true;
    queue_cv.notify_one();
    sender.join();
#endif


#ifdef TEST_SYSTEM_WINDOWS
	closesocket(sock);
    WSACleanup();
#else
    close(sock);
#endif
}
