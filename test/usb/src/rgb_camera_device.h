#pragma once

#include "async_usb.h"
#include "file_writer.h"

#define RGB_FRAME_BUF_SIZE	( 4 * 1024 * 1024) 

class RgbCameraDevice: public AsyncUSBDevice {
public:

    RgbCameraDevice(const std::string& interface_description);

    ~RgbCameraDevice(){
        std::cout << "~RgbCameraDevice" << std::endl;
        Close();
    }

    void OnOpen(TransferContext* transfer_context) override;

    void OnProcessPayload(TransferContext* transfer_context) override;

    bool NeedCopyData() override{
        return false;
    }

    void Close() override;

    void SwapBuffers(TransferContext* transfer_context);

    void SaveDataFrame(const DataFrame& data_frame);

    void PublishFrame(TransferContext* transfer_context);

private:
    std::shared_ptr<std::thread> rgb_work_thread_;
    std::mutex rgb_mutex_;
    std::condition_variable rgb_cond_;
    DataFrame rgb_frame_;
    FileWriter rgb_file_writer_;
};