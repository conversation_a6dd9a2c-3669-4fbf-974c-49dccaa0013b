#pragma once
#include <libusb.h>
#include <functional>
#include <vector>
#include <memory>
#include <atomic>
#include <common.h>
#include <data_types.h>


#define USB_MAX_NUM_TRANSFER_BUFS 30

#define TRANSFER_BUF_MAX_SIZE	( 16 * 1024 ) //16KB


class AsyncUSBDevice;

//1个in端点对应1个TransferContext
struct TransferContext {
    uint32_t ep_index;
    EndpointInfo in_end_point_info;
    EndpointInfo out_end_point_info;
    struct libusb_transfer *transfers[USB_MAX_NUM_TRANSFER_BUFS];
    uint8_t *transfer_bufs[USB_MAX_NUM_TRANSFER_BUFS];
    AsyncUSBDevice* async_usb_device;

    uint32_t transfer_actual_count{USB_MAX_NUM_TRANSFER_BUFS};
    uint32_t transfer_buf_actual_len{TRANSFER_BUF_MAX_SIZE};

    std::vector<uint8_t> outbuf;
    uint32_t outbuf_actual_size{0};

    std::vector<uint8_t> holdbuf; //bulk使用
    uint32_t holdbuf_actual_size{0};

    std::vector<uint8_t> leftover_buf;
    uint32_t leftover_buf_actual_size{0};

    bool has_parsed_header{false};
    FrameHeader frame_header{0,0};

    uint8_t one_transfer_buf[TRANSFER_BUF_MAX_SIZE];
    uint32_t one_transfer_buf_actual_size{0};


    FrequencyContext frequency_context;
    FrequencyContext frequency_context_ext;


    void Reset(){
        outbuf_actual_size = 0;
        has_parsed_header = false;
        frame_header.type = 0;
        frame_header.len = 0;
    }
};



class AsyncUSBDevice {
public:
    
    AsyncUSBDevice(const std::string& interface_description);
    virtual ~AsyncUSBDevice();

    bool Initialize();
    
    bool open();

    virtual void OnEpInOpen(TransferContext* transfer_context,EndpointInfo* ep) = 0;

    virtual void OnProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context) = 0;

    virtual bool NeedCopyData() = 0;

    virtual bool NeedGetCurrentHostTime() = 0;

    virtual void OnPostOpen(TransferContext* transfer_context) {}// called after OnEpInOpen

    virtual void Close();

    void HandleEvents();

    libusb_device_handle *  GetInterfaceString(libusb_device *dev, int interface_index,std::string& interface_string);

    bool ClaimDevice();

    void ProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context, uint8_t* data, int data_length);

    void TraceFrameRate(uint64_t current_time_ns, const std::string& tag, FrequencyContext* frequency_context);

    int WriteUsbData(const unsigned char *data, size_t length, TransferContext* transfer_context);

    void Stop();

    void TryFreeTransferAndNotify(TransferContext* context,libusb_transfer* transfer);

    void GetInterfaceDescription(std::string& interface_des) const {
        interface_des = interface_description_;
    }

protected:
    bool FindUsbDevice();

private:

    static void LIBUSB_CALL TransferCallback(libusb_transfer* transfer);
    
    libusb_device_handle* device_handle_{nullptr};
    std::vector<InterfaceInfo> interface_infos_;

    uint32_t in_ep_count_{0};
    uint32_t out_ep_count_{0};

    libusb_context *usb_ctx_{nullptr};

    std::shared_ptr<std::thread> handle_event_thread_;
    std::string interface_description_;

    static bool has_send_sig_int_;

protected:
    std::vector<TransferContext>  transfer_contexts_;
    std::atomic<bool> stream_running_{false};

    std::mutex cb_mutex_;
    std::condition_variable cb_cond_;
    int kill_handler_thread_{0};

    bool has_closed_{false};
};