#pragma once
#include <libusb.h>
#include <functional>
#include <vector>
#include <memory>
#include <atomic>
#include <common.h>
#include <data_types.h>



#include <perfetto.h>
#include <mutex>

/**
 * 在模块里的某个 cc/cpp 文件里放一行：
 * 注意
 * 1. 整个 so 或者 bin 只有一次 F_TRACE_INIT 调用。
 * 2. F_TRACE_INIT 不要放在任何命名空间内。
 * 3. 需要放在 F_TRACE_DEFINE_MODULE 之后。
 * 例子：
 *   F_TRACE_INIT(dove);
 */
#define F_TRACE_INIT(__NAMESPACE__) \
    PERFETTO_TRACK_EVENT_STATIC_STORAGE();                                  \
    /* 自动注册 */                                                          \
    namespace __NAMESPACE__ {                                               \
    struct _AutoReg {                                                       \
        _AutoReg() {                                                        \
            std::call_once(framework::util::trace::g_init_once, [] {        \
                ::perfetto::TracingInitArgs args;                             \
                args.backends = ::perfetto::BackendType::kSystemBackend;      \
                ::perfetto::Tracing::Initialize(args);                        \
            });                                                             \
            ::perfetto::TrackEvent::Register();                                \
        }                                                                   \
    };                                                                      \
    static _AutoReg _auto_reg;                                              \
    }

/**
 * 需要埋点的 cc 文件里放这一行。
 * 如果多个 cc 文件需要埋点， 可以把这个宏放在一个头文件里。 然后每个cc 文件include 这个头文件。
 * 例子：
 *   F_TRACE_DEFINE_MODULE("pilot", "pilot module");
 */
 /* 分类要唯一 */
#define F_TRACE_DEFINE_MODULE(__CATEGORY__, __DESCRIPTION__) \
    PERFETTO_DEFINE_CATEGORIES(                                             \
        ::perfetto::Category(__CATEGORY__).SetDescription(__DESCRIPTION__)); 

#define F_TRACE_DEFINE_MODULE2(__CATEGORY1__, __DESCRIPTION1__, __CATEGORY2__, __DESCRIPTION2__) \
    PERFETTO_DEFINE_CATEGORIES(                                             \
        ::perfetto::Category(__CATEGORY1__).SetDescription(__DESCRIPTION1__), \
        ::perfetto::Category(__CATEGORY2__).SetDescription(__DESCRIPTION2__)); 
/**
 * This type of trace event is scoped, under the hood it uses C++ RAII. 
 * The event will cover the time from 
 * when the F_TRACE_EVENT annotation is encountered to the end of the block.
 */
#define F_TRACE_EVENT(__CATEGORY__, __NAME__, ...) \
  TRACE_EVENT(__CATEGORY__, __NAME__, ##__VA_ARGS__)

namespace framework::util::trace {
 extern std::once_flag g_init_once;
} // namespace framework::util::trace



#define USB_MAX_NUM_TRANSFER_BUFS 30

#define TRANSFER_BUF_MAX_SIZE	( 16 * 1024 ) //16KB

#define INTERRUPT_FRAME_BUF_SIZE	( 16 * 1024 )


class AsyncUSBDevice;

//1个in端点对应1个TransferContext
struct TransferContext {
    uint32_t ep_index;
    EndpointInfo in_end_point_info;
    EndpointInfo out_end_point_info;
    struct libusb_transfer *transfers[USB_MAX_NUM_TRANSFER_BUFS];
    uint8_t *transfer_bufs[USB_MAX_NUM_TRANSFER_BUFS];
    AsyncUSBDevice* async_usb_device;

    uint32_t transfer_actual_count{USB_MAX_NUM_TRANSFER_BUFS};
    uint32_t transfer_buf_actual_len{TRANSFER_BUF_MAX_SIZE};

    std::vector<uint8_t> outbuf;
    uint32_t outbuf_actual_size{0};

    std::vector<uint8_t> holdbuf;
    uint32_t holdbuf_actual_size{0};

    std::vector<uint8_t> leftover_buf;
    uint32_t leftover_buf_actual_size{0};

    bool has_parsed_header{false};
    FrameHeader frame_header{0,0};

    uint8_t one_transfer_buf[TRANSFER_BUF_MAX_SIZE];
    uint32_t one_transfer_buf_actual_size{0};


    FrequencyContext frequency_context;
    FrequencyContext frequency_context_ext;


    void Reset(){
        outbuf_actual_size = 0;
        has_parsed_header = false;
        frame_header.type = 0;
        frame_header.len = 0;
    }
};



class AsyncUSBDevice {
public:
    
    AsyncUSBDevice(const std::string& interface_description);
    virtual ~AsyncUSBDevice();

    bool Initialize();
    
    bool open();

    virtual void OnOpen(TransferContext* transfer_context) = 0;

    virtual void OnProcessPayload(TransferContext* transfer_context) = 0;

    virtual bool NeedCopyData() = 0;

    virtual void OnPostOpen(TransferContext* transfer_context) {}// called after OnOpen

    virtual void Close();

    void HandleEvents();

    libusb_device_handle *  GetInterfaceString(libusb_device *dev, int interface_index,std::string& interface_string);

    bool ClaimDevice();

    void ProcessPayload(TransferContext* transfer_context, uint8_t* data, int data_length);

    void TraceFrameRate(uint64_t current_time_ns, const std::string& tag, FrequencyContext* frequency_context);

    int WriteUsbData(const unsigned char *data, size_t length, TransferContext* transfer_context);

    void Stop();

    void TryFreeTransferAndNotify(TransferContext* context,libusb_transfer* transfer);

protected:
    bool FindUsbDevice();

private:

    static void LIBUSB_CALL TransferCallback(libusb_transfer* transfer);
    
    libusb_device_handle* device_handle_{nullptr};
    std::vector<InterfaceInfo> interface_infos_;

    uint32_t in_ep_count_{0};
    uint32_t out_ep_count_{0};

    libusb_context *usb_ctx_{nullptr};

    std::shared_ptr<std::thread> handle_event_thread_;
    std::string interface_description_;

protected:
    std::vector<TransferContext>  transfer_contexts_;
    std::atomic<bool> stream_running_{false};

    std::mutex cb_mutex_;
    std::condition_variable cb_cond_;
    int kill_handler_thread_{0};

    bool has_closed_{false};
};