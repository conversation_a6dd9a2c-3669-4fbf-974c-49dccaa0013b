#include "hid_device.h"

#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
#include <unistd.h>
#include <sys/syscall.h>
#include <sched.h>
#include <pthread.h>
#endif


bool HidDevice::open() {
    if (hid_init()) {
        std::cerr << "hid_init failed\n";
        return false;
    }

    struct hid_device_info* devs = hid_enumerate(vendor_id_, product_id_);
    struct hid_device_info* cur = devs;
    while (cur) {
        if (cur->interface_number == interface_number_) {
            device_ = hid_open_path(cur->path);
            LOG_INFO("HidDevice::open cur->path:{},interface_number_:{} success",cur->path,interface_number_);
            break;
        }
        cur = cur->next;
    }
    hid_free_enumeration(devs);

    if (!device_) {
        LOG_ERROR("Failed to open HID device");
        return false;
    }

    running_ = true;
    read_thread_ = std::thread(&HidDevice::readLoop, this);
    return true;
}

void HidDevice::close() {
    running_ = false;
    if (read_thread_.joinable())
        read_thread_.join();

    if (device_) {
        hid_close(device_);
        device_ = nullptr;
    }

    hid_exit();
}

bool HidDevice::write(const std::vector<unsigned char>& data) {
    std::lock_guard<std::mutex> lock(write_mutex_);
    if (!device_) return false;
    int res = hid_write(device_, data.data(), data.size());
    return res >= 0;
}

void HidDevice::readLoop() {

#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
    static bool has_set_thread_priority_and_name = false;
    if( !has_set_thread_priority_and_name){
        has_set_thread_priority_and_name = true;
        pid_t tid = syscall(SYS_gettid); 
        struct sched_param param;
        param.sched_priority = 99;
        if (sched_setscheduler(0, SCHED_RR, &param) == -1) {
            LOG_INFO("readLoop sched_setscheduler error:tid:{}",tid);
        }
        else{
            LOG_INFO("readLoop sched_setscheduler success:tid:{}",tid);
        }
        pthread_setname_np(pthread_self(), "imu_cb");
    }
#endif

    constexpr size_t READ_BUF_SIZE = 1024;
    std::vector<unsigned char> buf(READ_BUF_SIZE);
    NRImuData nr_imu_data{};
    while (running_) {
        //LOG_DEBUG("readLoop hid_read begin");
        int read_length = hid_read(device_, buf.data(), buf.size());
       // LOG_DEBUG("readLoop read {} bytes",read_length);
        if (read_length > 0) {

            uint64_t current_host_time_ns = FMonotonicGetNs();
            if(imu_source_type_ == IMU_SOURCE_TYPE_FROM_SDK_HID) {
                if((uint32_t)read_length <= sizeof(FrameHeader)) {
                    LOG_ERROR("HidDevice::readLoop read {} bytes less than FrameHeader size", read_length);
                    continue;
                }
                FrameHeader* frame_header = (FrameHeader*)buf.data();
                if(frame_header->type == FrameType::MultiImuFromFrames){
                    uint32_t* imu_packet_count = (uint32_t*)(buf.data()+sizeof(FrameHeader));

                    if(*imu_packet_count == 0){
                        LOG_ERROR("HidDevice::readLoop imu_packet_count is 0,data_length:{}",read_length);
                        continue;
                    }
                    uint32_t imu_data_len = read_length - sizeof(FrameHeader) - sizeof(uint32_t);
                    if(imu_data_len % sizeof(NRImuData) != 0){
                        LOG_ERROR("HidDevice::readLoop imu_data_len is not align,data_length:{}",read_length);
                        continue;
                    }
                    uint32_t real_imu_data_count = imu_data_len / sizeof(NRImuData);
                    if(real_imu_data_count != *imu_packet_count){
                        LOG_ERROR("HidDevice::readLoop real_imu_data_count:{} is not equal to imu_packet_count:{} data_length:{}",real_imu_data_count, *imu_packet_count, read_length);
                        continue;
                    }

                    // DoTraceFrameRate(current_host_time_ns, "SDK-HID-MULIT-IMU", &multi_imu_freq_context_);
                    // LOG_DEBUG("HidDevice::readLoop SDK-HID-MULIT-IMU real_imu_data_count:{}",real_imu_data_count);

                    for(uint32_t i = 0; i < real_imu_data_count; ++i){
                        NRImuData* imu_data = (NRImuData*)(buf.data()+sizeof(FrameHeader)+ sizeof(uint32_t) + i*sizeof(NRImuData));
                        // DoTraceFrameRate(current_host_time_ns,"SDK-HID-IMU", &imu_freq_context_);
                        // LOG_DEBUG("Imu Raw Data: {}",NRImuDataWithTime{*imu_data, current_host_time_ns});
                        record_helper_.push(NRImuDataWithTime{nr_imu_data, current_host_time_ns});
                    }
                }
                else if (frame_header->type == FrameType::ImuFromFrames){
                    NRImuData* imu_data = (NRImuData*)(buf.data()+sizeof(FrameHeader));
                    // DoTraceFrameRate(current_host_time_ns,"SDK-HID-IMU", &imu_freq_context_);
                    // LOG_DEBUG("Imu Raw Data: {}",NRImuDataWithTime{*imu_data, current_host_time_ns});
                    record_helper_.push(NRImuDataWithTime{nr_imu_data, current_host_time_ns});
                }
                else{
                    LOG_ERROR("HidDevice::readLoop Unknown frame type: {}",frame_header->type);
                }
            }
            else if(imu_source_type_ == IMU_SOURCE_TYPE_FROM_BSP_HID) {
                NRImuDataBsp* imu_data_bsp = (NRImuDataBsp*)buf.data();
                converter_.Convert(nr_imu_data, *imu_data_bsp);
                // DoTraceFrameRate(current_host_time_ns,"BSP-HID-IMU", &imu_freq_context_);
                // LOG_DEBUG("Imu Raw Data: {}",NRImuDataWithTime{nr_imu_data, current_host_time_ns});
                record_helper_.push(NRImuDataWithTime{nr_imu_data, current_host_time_ns});
            }
            else{
                LOG_ERROR("HidDevice::readLoop Unknown IMU source type: {}",(int)imu_source_type_);
                break;
            }
        } else if (read_length < 0) {
            LOG_ERROR("hid_read error:{}",read_length);
            break;
        }
        else{
            continue;
        }
    }
}



bool HidDevice::SendImuCommand(int8_t imu_ids,bool enable) {
    if(!device_){
        return false;
    }
    uint8_t buffer[11] = {0};
    uint32_t pos = 8;
    buffer[pos++] = 0x01;
    buffer[pos++] = enable ? 0x01 : 0x02;
    buffer[pos++] = imu_ids;

    LOG_INFO("Send IMU command: imu_ids={}, enable={}",imu_ids,enable)

    int res = hid_write(device_, buffer, sizeof(buffer));
    if (res < 0) {
        //LOG_ERROR("Failed to write IMU command: {}",hid_error(device_));
        return false;
    } else {
        LOG_INFO("{},res={}",(enable ? "IMU1 Opened" : "IMU1 Closed"),res);
        return true;
    }
}