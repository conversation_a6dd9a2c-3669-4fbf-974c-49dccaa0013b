#include "imu_device.h"
#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
#include <unistd.h>
#include <sys/syscall.h>
#include <sched.h>
#include <pthread.h>
#include <sys/sysinfo.h>
#endif

#if NEED_OPEN_TRACE
F_TRACE_DEFINE_MODULE("process", "usb process")
#endif

void setaffinity_CPU(){
#if defined(__ANDROID__)
    int cpu_count = get_nprocs();
    LOG_INFO("CPU count: {}, CPU_SETSIZE: {}", cpu_count, CPU_SETSIZE);

    //puck 2,3,4,5是大核，0,1是小核
    if(cpu_count >= 6) {
        cpu_set_t mask;
        CPU_ZERO(&mask);

        // 避免越界
        int cpus_to_bind[] = {2, 3, 4, 5};
        for (int cpu : cpus_to_bind) {
            if (cpu < CPU_SETSIZE && cpu < cpu_count) {
                CPU_SET(cpu, &mask);
            } else {
                LOG_INFO("Skip binding to CPU {}, out of range", cpu);
            }
        }

        pid_t pid = gettid();
        int ret = sched_setaffinity(pid, sizeof(mask), &mask);
        if (ret < 0) {
            LOG_INFO("Bind process {} setaffinity failed, ret={}, errno = {}, message = {}",
                     pid, ret, errno, strerror(errno));
        } else {    
            LOG_INFO("Bind process {} setaffinity success!", pid);
        }
    }
#endif
}

#define IMU_RAW_DATA_TAG "Imu Raw Data"

ImuDevice::ImuDevice(const std::string& interface_description): AsyncUSBDevice(interface_description),
record_helper_(IMU_RAW_DATA_TAG),
record_helper_google_(IMU_RAW_DATA_TAG),
record_helper_imu_stats_(IMU_RAW_DATA_TAG){
    record_helper_.start();
    record_helper_google_.start();
    record_helper_imu_stats_.start();
}


void ImuDevice::OnEpInOpen(TransferContext* transfer_context,EndpointInfo* ep){
    // transfer_context->transfer_actual_count = 5;
    // transfer_context->transfer_buf_actual_len = ep->maxPacketSize;

    LOG_INFO("ImuDevice::OnEpInOpen transfer_actual_count:{} transfer_buf_actual_len:{}",
        transfer_context->transfer_actual_count,transfer_context->transfer_buf_actual_len);

    transfer_context->outbuf.resize(transfer_context->transfer_buf_actual_len);
}

template<typename T,typename T_WithTime>
bool ImuDevice::ParsePacket(TransferContext* transfer_context, RecordHelper<T_WithTime>& record_helper, uint64_t current_host_time_ns, const std::string& log_tag) {
    uint32_t imu_data_len = transfer_context->outbuf_actual_size - sizeof(FrameHeader);
    if(imu_data_len % sizeof(T) != 0){
        LOG_ERROR("ImuDevice::ParsePacket: {} MultiImuFromFrames imu_data_len is not align: imu_data_len:{}, imu one size:{},len_in_header:{}",
            log_tag,imu_data_len,sizeof(T),transfer_context->frame_header.len);
        transfer_context->Reset();
        return false;
    }

    uint32_t imu_data_count = imu_data_len / sizeof(T);

    // TraceFrameRate(current_host_time_ns, "Google-Interrupt-Multi IMU", &transfer_context->frequency_context_ext);
    //LOG_DEBUG("Google-Interrupt-Multi IMU imu_data_count:{}",imu_data_count);

    for(uint32_t i = 0; i < imu_data_count; ++i){
        T* imu_data = (T*)(transfer_context->outbuf.data()+sizeof(FrameHeader) + i*sizeof(T));

#if NEED_OPEN_TRACE
        if constexpr (std::is_same_v<T, NRImuDataGoogle>) {
            LOGI("google-imu data: %d accel_id: %d, gyro_id: %d, curr:%ld, time_device:%ld",
                        imu_data->frame_id, imu_data->accel_id,imu_data->gyro_id,
                        current_host_time_ns, imu_data->hmd_time_nanos_device);
        }

        if constexpr (std::is_same_v<T, NRImuDataGoogleStats>) {
            LOGI("google_stats-imu data: %d accel_id: %d, gyro_id: %d, curr:%ld, time_device:%ld",
                        imu_data->imu_data.frame_id, imu_data->imu_data.accel_id,imu_data->imu_data.gyro_id,
                        current_host_time_ns, imu_data->imu_data.hmd_time_nanos_device);
        }
#endif

        // TraceFrameRate(current_host_time_ns,"Interrupt-IMU", &transfer_context->frequency_context);
        // LOG_DEBUG("Imu Raw Data: {}",NRImuDataGoogleWithTime{*imu_data, current_host_time_ns});
        record_helper.push(T_WithTime{*imu_data, current_host_time_ns});
    }
    return true;
}

//模板实例化
template bool ImuDevice::ParsePacket<NRImuDataGoogleStats,NRImuDataGoogleStatsWithTime>(TransferContext*, RecordHelper<NRImuDataGoogleStatsWithTime>&, uint64_t, const std::string&);
template bool ImuDevice::ParsePacket<NRImuDataGoogle,NRImuDataGoogleWithTime>(TransferContext*, RecordHelper<NRImuDataGoogleWithTime>&, uint64_t, const std::string&);  

// 特化模板
template<>
bool ImuDevice::ParsePacket<NRImuData, NRImuDataWithTime>(
    TransferContext* transfer_context,
    RecordHelper<NRImuDataWithTime>& record_helper,
    uint64_t current_host_time_ns,
    const std::string& log_tag)
{
    uint32_t* imu_packet_count = (uint32_t*)(transfer_context->outbuf.data()+sizeof(FrameHeader));

    if(*imu_packet_count == 0){
        LOG_ERROR("ImuDevice::ParsePacket: xreal MultiImuFromFrames imu_packet_count is 0");
        transfer_context->Reset();
        return false;
    }
    uint32_t imu_data_len = transfer_context->outbuf_actual_size - sizeof(FrameHeader) - sizeof(uint32_t);
    if(imu_data_len % sizeof(NRImuData) != 0){
        LOG_ERROR("ImuDevice::ParsePacket: xreal MultiImuFromFrames imu_data_len is not align: imu_data_len:{}, imu one size:{},len_in_header:{}",
            imu_data_len,sizeof(NRImuData),transfer_context->frame_header.len);
        transfer_context->Reset();
        return false;
    }
    uint32_t imu_data_count = imu_data_len / sizeof(NRImuData);
    if(imu_data_count != *imu_packet_count){
        LOG_ERROR("ImuDevice::ParsePacket: xreal MultiImuFromFrames imu_data_count is not equal to imu_packet_count");
        transfer_context->Reset();
        return false;
    }

    //TraceFrameRate(current_host_time_ns, "Interrupt-Multi IMU", &transfer_context->frequency_context_ext);
    //LOG_DEBUG("Interrupt-Multi IMU imu_data_count:{}",imu_data_count);

    for(uint32_t i = 0; i < imu_data_count; ++i){
        NRImuData* imu_data = (NRImuData*)(transfer_context->outbuf.data()+sizeof(FrameHeader)+ sizeof(uint32_t) + i*sizeof(NRImuData));
        // TraceFrameRate(current_host_time_ns,"Interrupt-IMU", &transfer_context->frequency_context);
        // LOG_DEBUG("Imu Raw Data: {}",NRImuDataWithTime{*imu_data, current_host_time_ns});
        record_helper_.push(NRImuDataWithTime{*imu_data, current_host_time_ns});
    }

    return true;
}


void ImuDevice::OnProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context){
#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
    static thread_local bool thread_named = false;
    if (!thread_named) {
        pthread_setname_np(pthread_self(), "imu_cb");
        thread_named = true;
    }

    static thread_local bool has_set_priority = false;
    if(!has_set_priority){
        has_set_priority = true;
        pid_t tid = syscall(SYS_gettid); 
        struct sched_param param;
        param.sched_priority = 99;
        if (sched_setscheduler(0, SCHED_RR, &param) == -1) {
            LOG_INFO("sched_setscheduler error:tid:{}",tid);
        }
        else{
            LOG_INFO("sched_setscheduler success:tid:{}",tid);
        }
        setaffinity_CPU();
    }
#endif
    //uint64_t current_host_time_ns = FMonotonicGetNs();
    if(transfer_context->frame_header.type == FrameType::MultiImuStats){
        if(!ParsePacket<NRImuDataGoogleStats,NRImuDataGoogleStatsWithTime>(transfer_context, record_helper_imu_stats_,current_host_time_ns,"ImuGoogleStats")){
            return;
        }
    }
#if USE_GOOGLE_IMU
    else if(transfer_context->frame_header.type == FrameType::MultiImuFromFrames){
        if(!ParsePacket<NRImuDataGoogle,NRImuDataGoogleWithTime>(transfer_context, record_helper_google_,current_host_time_ns,"ImuGoogle")){
            return;
        }
    }
#else
    else if(transfer_context->frame_header.type == FrameType::MultiImuFromFrames){
        if(!ParsePacket<NRImuData,NRImuDataWithTime>(transfer_context, record_helper_,current_host_time_ns,"ImuXreal")){
            return;
        }
    }
#endif
    else if(transfer_context->frame_header.type == FrameType::ImuFromFrames){
        NRImuData* imu_data = (NRImuData*)(transfer_context->outbuf.data()+sizeof(FrameHeader));
        // TraceFrameRate(current_host_time_ns,"Interrupt-IMU", &transfer_context->frequency_context);
        // LOG_DEBUG("Imu Raw Data: {}",NRImuDataWithTime{*imu_data, current_host_time_ns});
        record_helper_.push(NRImuDataWithTime{*imu_data, current_host_time_ns});
    }
    else {
        LOG_ERROR("ImuDevice::OnProcessPayload Invalid frame type: {}", transfer_context->frame_header.type);
    }
}

