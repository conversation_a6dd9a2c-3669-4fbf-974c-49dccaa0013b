#pragma once


#include <string>
#include "data_types.h"
#include "common.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

/// gravity
constexpr double kGravity = 9.8;
/// accelerometer measurement range
constexpr int32_t kAccNumerator = 16 * 2;
/// accelerometer measurement bit range, 16 bits
constexpr int32_t kAccDenominator = 65536;
/// gyroscope measurement range
constexpr int32_t kGyroNumerator = 2000 * 2;
/// gyroscope measurement bit range, 16 bits
constexpr int32_t kGyroDenominator = 65536;
/// Degree 2 Rad convert coefficient
constexpr double kDegree2Rad = 2.0 * M_PI / 360;
constexpr double kMagScale = 100.f;


constexpr int32_t kImuLatency = 950*1000;


static const char * default_imu_json = R"(
{
    "protocol_version":"0x20220101",
    "support_identifiers":[
        "539099393ULL"],
    "imu":{
        "gyro_numerator":
        {
            "benchmark":8000
        },
        "gyro_denominator":
        {
            "benchmark":16777216
        },
        "acc_numerator":
        {
            "benchmark":64
        },
        "acc_denominator":
        {
            "benchmark":16777216
        },
        "mag_offset":
        {
        },
        "mag_denominator":
        {
        },
        "temperature":
        {
            "scale":0.0078125,
            "difference":25.0
        },
        "gyro_swap":
        {
            "swap_pos1":1,
            "swap_pos2":2
        },
        "acc_swap":
        {
            "swap_pos1":1,
            "swap_pos2":2
        },
        "mag_swap":
        {
        },
    }
}
)";

template <typename TYPE, typename TYPE2>
struct ConvertUnit {
    bool convert(TYPE& value, TYPE2 src_value) {
        if (std::is_same<TYPE, std::string>::value) {
            LOG_TRACE("Item '{}' TYPE is string", item_name_);
            return false;
        }
        if (std::is_same<TYPE2, std::string>::value) {
            LOG_TRACE("Item '{}' TYPE2 is string", item_name_);
            return false;
        }
        if (default_exist_) {
            value = default_value_ * scale_ + difference_;
            return true;
        }

        if (benchmark_exist_ && benchmark_value_ != src_value) {
            LOG_TRACE("Item '{}' : recv {} != expected {}", item_name_, src_value, benchmark_value_);
            return false;
        }

        value = src_value * scale_ + difference_;
        return true;
    }
    bool swap(TYPE2 value) {
        if (!std::is_pointer<TYPE2>::value) {
            LOG_TRACE("Item '{}' not pointer", item_name_);
            return false;
        }
        if (swap_pos1_ < 0 || swap_pos2_ < 0) {
            return true;
        }
        std::swap(value[swap_pos1_], value[swap_pos2_]);
        return true;
    }
    void clear() {
        item_name_ = "";
        is_valid_ = false;
        default_exist_ = false;
        benchmark_exist_ = false;
        default_value_ = 0;
        benchmark_value_ = 0;
        difference_ = 0;
        scale_ = 1.0;
        swap_pos1_ = -1;
        swap_pos2_ = -1;
    }
    void dump() const {
        LOG_TRACE("{},{},{},{},{},{},{},{},{},{}",
                          item_name_,
                          is_valid_,
                          default_exist_,
                          benchmark_exist_,
                          default_value_ ,
                          benchmark_value_,
                          difference_,
                          scale_,
                          swap_pos1_,
                          swap_pos2_);
    }

    std::string item_name_;
    bool is_valid_{false};
    bool default_exist_{false};
    bool benchmark_exist_{false};
    TYPE default_value_{0};
    TYPE benchmark_value_{0};
    TYPE difference_{0};
    double scale_{1.0};
    int32_t swap_pos1_{-1};
    int32_t swap_pos2_{-1};
};

struct StructParser {
    bool initialize(const std::string& json_str) {
        Json::CharReaderBuilder json_builder;
        json_builder["collectComments"] = false;
        JSONCPP_STRING json_errs;
        std::istringstream json_stream(json_str);
        if (!parseFromStream(json_builder, json_stream, &json_proto_, &json_errs)) {
            LOG_ERROR("StructParser initialize Parsing error, json_errs = {}", json_errs.c_str());
            return false;
        }
        return true;
    }

    uint64_t version() {
        uint64_t version = 0;
        if (json_proto_.isMember("protocol_version")) {
            LOG_TRACE("protocol_version = {}", json_proto_["protocol_version"].asString());
            version = strtoull(json_proto_["protocol_version"].asString().c_str(), nullptr, 16);;
        }
        LOG_TRACE("StructParser version = {:x}", version);
        return version;
    }

    template<typename fieldType>
    bool getField(const Json::Value &field, const std::string& field_item, fieldType& value) {
        if (!field.isMember(field_item)) return false;

        if (std::is_same<fieldType, uint64_t>::value) {
            value = field[field_item].asUInt64();
        } else if (std::is_same<fieldType, int64_t>::value) {
            value = field[field_item].asInt64();
        } else if (std::is_same<fieldType, uint32_t>::value || std::is_same<fieldType, uint16_t>::value || std::is_same<fieldType, uint8_t>::value) {
            value = field[field_item].asUInt();
        } else if (std::is_same<fieldType, int32_t>::value || std::is_same<fieldType, int16_t>::value || std::is_same<fieldType, int8_t>::value) {
            value = field[field_item].asInt();
        } else if (std::is_same<fieldType, float>::value || std::is_same<fieldType, double>::value)  {
            value = field[field_item].asDouble();
        } else {
            LOG_ERROR("{}, type not supported", field_item);
            return false;
        }
        return true;
    }

    template <typename fieldType, typename fieldType2>
    bool initConvertUnit(const std::string& proto_name, const std::string& field_name, ConvertUnit<fieldType, fieldType2>& value) {
        if (std::is_same<fieldType, std::string>::value) {
            LOG_ERROR("The type of {} is string, cannot using convert unit", field_name);
            return false;
        }
        value.item_name_ = field_name;
        if (!json_proto_.isMember(proto_name)) {
            value.is_valid_ = false;
            return false;
        }

        const Json::Value &items = json_proto_[proto_name];
        if (!items.isMember(field_name)) {
            value.is_valid_ = false;
            return false;
        }
        value.is_valid_ = true;

        const Json::Value &field = items[field_name];
        value.benchmark_exist_ = getField(field, "benchmark", value.benchmark_value_);
        value.default_exist_ = getField(field, "default", value.default_value_);
        getField(field, "scale", value.scale_);
        getField(field, "difference", value.difference_);
        getField(field, "swap_pos1", value.swap_pos1_);
        getField(field, "swap_pos2", value.swap_pos2_);
        return true;
    }

protected:
    Json::Value json_proto_;
};


class ImuConverter {
public:
    explicit ImuConverter();

    void Init(const std::string & json_str);
    void Clear();

    inline void Convert(NRImuData & data, const NRImuDataBsp & src_data)  {
        //static const int32_t imu_latency = kImuLatency;//flora imu has 950us latency,imu timestamp need minus this latency
        data.imu_id = src_data.imu_id;
        data.frame_id = src_data.frame_id;
        data.data_mask = src_data.data_mask;
        data.hmd_time_nanos_device = src_data.hmd_time_nanos_device;
        //data.hmd_time_nanos_device = src_data.hmd_time_nanos_device - imu_latency;
        data.hmd_time_nanos_sensor = src_data.hmd_time_nanos_sensor;

//    unit_imu_ts_.convert(data.hmd_time_nanos_device, src_data.hmd_time_nanos_device);
//    unit_sensor_ts_.convert(data.hmd_time_nanos_sensor, src_data.hmd_time_nanos_sensor);
        unit_temperature_.convert(data.temperature, src_data.temperature);

        unit_gyro_numerator_.convert(gyro_numerator_, src_data.gyroscope_numerator);
        unit_gyro_denominator_.convert(gyro_denominator_, src_data.gyroscope_denominator);
        unit_acc_numerator_.convert(acc_numerator_, src_data.accelerometer_numerator);
        unit_acc_denominator_.convert(acc_denominator_, src_data.accelerometer_denominator);
        unit_mag_offset_.convert(mag_offset_, src_data.magnetometer_offset);
        unit_mag_denominator_.convert(mag_denominator_, src_data.magnetometer_denominator);

        data.gyroscope.x = float(-convertUint24ToInt32(src_data.gyroscope.x) * kDegree2Rad * gyro_numerator_ / gyro_denominator_);
        data.gyroscope.y = float(-convertUint24ToInt32(src_data.gyroscope.y) * kDegree2Rad * gyro_numerator_ / gyro_denominator_);
        data.gyroscope.z = float(-convertUint24ToInt32(src_data.gyroscope.z) * kDegree2Rad * gyro_numerator_ / gyro_denominator_);
        data.accelerometer.x = float(-convertUint24ToInt32(src_data.accelerometer.x) * gravity_ * acc_numerator_ / acc_denominator_);
        data.accelerometer.y = float(-convertUint24ToInt32(src_data.accelerometer.y) * gravity_ * acc_numerator_ / acc_denominator_);
        data.accelerometer.z = float(-convertUint24ToInt32(src_data.accelerometer.z) * gravity_ * acc_numerator_ / acc_denominator_);
        data.magnetometer.x = float((src_data.magnetometer.x - mag_offset_) * mag_scale_ / mag_denominator_);
        data.magnetometer.y = float((src_data.magnetometer.y - mag_offset_) * mag_scale_ / mag_denominator_);
        data.magnetometer.z = float((src_data.magnetometer.z - mag_offset_) * mag_scale_ / mag_denominator_);

        unit_gyro_swap_.swap(&(data.gyroscope.x));
        unit_acc_swap_.swap(&(data.accelerometer.x));
        unit_mag_swap_.swap(&(data.magnetometer.x));

        data.gyroscope_numerator = src_data.gyroscope_numerator;
        data.accelerometer_numerator = src_data.accelerometer_numerator;
        data.magnetometer_numerator = 0;
        data.out_numerator_mask = 0;
    }


private:
    inline static int32_t convertUint24ToInt32(uint32_t data) {
        auto value = (int32_t) data;
        if ((data & 0x800000)) {
            value = (int32_t) (data | 0xFF000000);
        }
        return value;
    }

    inline static int16_t convertUint32ToInt16(uint32_t data) {
        return (int16_t)(data);
    }

protected:
    std::string proto_name_{"imu"};
    StructParser struct_parser_;

//    ConvertUnit<uint64_t, uint64_t> unit_imu_ts_;
//    ConvertUnit<uint64_t, uint64_t> unit_sensor_ts_;
    ConvertUnit<float, int32_t> unit_temperature_;

    ConvertUnit<int32_t, int32_t>  unit_gyro_numerator_;
    ConvertUnit<int32_t, int32_t>  unit_gyro_denominator_;
    ConvertUnit<int32_t, int32_t>  unit_acc_numerator_;
    ConvertUnit<int32_t, int32_t>  unit_acc_denominator_;
    ConvertUnit<int32_t, int32_t>  unit_mag_offset_;
    ConvertUnit<int32_t, int32_t>  unit_mag_denominator_;

    ConvertUnit<int32_t, float*>  unit_gyro_swap_;
    ConvertUnit<int32_t, float*>  unit_acc_swap_;
    ConvertUnit<int32_t, float*>  unit_mag_swap_;

    int32_t gyro_numerator_{kGyroNumerator};
    int32_t gyro_denominator_{kGyroDenominator};
    int32_t acc_numerator_{kAccNumerator};
    int32_t acc_denominator_{kAccDenominator};
    int32_t mag_offset_{0};
    int32_t mag_denominator_{1};
    double gravity_{kGravity};
    double mag_scale_{kMagScale};
};
