#pragma once

#include "async_usb.h"
#include "file_writer.h"

#define GRAY_FRAME_BUF_SIZE	( 4 * 1024 * 1024 ) 

class GrayCameraDevice: public AsyncUSBDevice {
public:

    GrayCameraDevice(const std::string& interface_description);

    ~GrayCameraDevice(){
        std::cout << "~GrayCameraDevice" << std::endl;
        Close();
    }

    void OnEpInOpen(TransferContext* transfer_context,EndpointInfo* ep) override;

    void OnProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context) override;

    bool NeedCopyData() override{
        return false;
    }

    bool NeedGetCurrentHostTime() override {
        return false;
    }

    void Close() override;

    void SwapBuffers(TransferContext* transfer_context);

    void SaveDataFrame(const DataFrame& data_frame);

    void PublishFrame(TransferContext* transfer_context);

private:
    std::shared_ptr<std::thread> work_thread_;
    std::mutex gray_mutex_;
    std::condition_variable gray_cond_;
    DataFrame gray_frame_;
    FileWriter gray_file_writer_;
};