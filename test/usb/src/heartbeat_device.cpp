#include "heartbeat_device.h"

#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
#include <pthread.h>
#endif

#define HEARTBEAT_FPS 10

HeartbeatDevice::HeartbeatDevice(const std::string& interface_description):AsyncUSBDevice(interface_description),record_helper_("usb_heart_beat",1,HEARTBEAT_FPS*10){
    record_helper_.start();
}

void HeartbeatDevice::OnEpInOpen(TransferContext* transfer_context,EndpointInfo* ep){

    // transfer_context->transfer_actual_count = 5;
    // transfer_context->transfer_buf_actual_len = ep->maxPacketSize;
    LOG_INFO("HeartbeatDevice::OnEpInOpen transfer_actual_count:{} transfer_buf_actual_len:{}",
        transfer_context->transfer_actual_count,transfer_context->transfer_buf_actual_len);
        
    transfer_context->outbuf.resize(transfer_context->transfer_buf_actual_len);
}

void HeartbeatDevice::StartLaunchHeartbeat(){
    heartbeat_thread_.reset(new std::thread(std::bind(&HeartbeatDevice::SendHeartbeatThead, this, cur_transfer_context_)));
}

void HeartbeatDevice::OnPostOpen(TransferContext* transfer_context){
    cur_transfer_context_ = transfer_context;
}

void HeartbeatDevice::Close(){
    if(has_closed_) return;
    std::cout << "HeartbeatDevice::Close" << std::endl;
    record_helper_.stop();
    stream_running_ = false;
    if (heartbeat_thread_ && heartbeat_thread_->joinable()) {
        heartbeat_thread_->join();
    }
    AsyncUSBDevice::Close();
}


void HeartbeatDevice::OnProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context){
#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
    static thread_local bool thread_named = false;
    if (!thread_named) {
        pthread_setname_np(pthread_self(), "hb_cb");
        thread_named = true;
    }
#endif
    //uint64_t current_host_time_ns = FMonotonicGetNs();
    if(transfer_context->frame_header.type == FrameType::Heartbeat){
        NRHeartBeat* heartbeat_data = (NRHeartBeat*)(transfer_context->outbuf.data()+sizeof(FrameHeader));
        heartbeat_data->t4 = current_host_time_ns;
        //TraceFrameRate(current_host_time_ns,"Interrupt-Heartbeat", &transfer_context->frequency_context);
        //LOG_INFO("usb_heart_beat:{},{},{},{}", heartbeat_data->t1, heartbeat_data->t2, heartbeat_data->t3, heartbeat_data->t4);
        record_helper_.push(*heartbeat_data);
    }
    else {
        LOG_ERROR("HeartbeatDevice::OnProcessPayload Invalid frame type: {}", transfer_context->frame_header.type);
    }
}

static constexpr uint64_t DELTA_NS = OS_NS_PER_SEC / HEARTBEAT_FPS;

void HeartbeatDevice::SendHeartbeatThead(TransferContext* transfer_context){
    if (!stream_running_) return;
    LOG_INFO("start SendHeartbeatThead");

#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
    static thread_local bool thread_named = false;
    if (!thread_named) {
        pthread_setname_np(pthread_self(), "send_hb");
        thread_named = true;
    }
#endif

    uint64_t app_ns = FMonotonicGetNs();
    NRHeartBeat  heart_beat;
    heart_beat.is_request = 1;
    FrameHeader frame_header;
    frame_header.type = FrameType::Heartbeat;
    frame_header.len = sizeof(heart_beat);

    while (true) {
        uint64_t real_ns = FMonotonicGetNs();
        app_ns += DELTA_NS;
        {
            heart_beat.t1 = real_ns;

            uint32_t packet_len = sizeof(frame_header) + sizeof(heart_beat);
            std::vector<uint8_t> packet(packet_len);
            memcpy(packet.data(), &frame_header, sizeof(frame_header));
            memcpy(packet.data() + sizeof(frame_header), &heart_beat, sizeof(heart_beat));

            WriteUsbData(packet.data(), packet_len, transfer_context);
        }
        FNanoSleep(app_ns - real_ns);

        if(!stream_running_) break;
    }
}


bool HeartbeatDevice::ControlImu(bool enable){
    FrameHeader frame_header;
    frame_header.type = enable? FrameType::StartImu : FrameType::StopImu;
    frame_header.len = 0;
    if(WriteUsbData((const unsigned char *)&frame_header, sizeof(frame_header), &transfer_contexts_[0])>0){
        LOG_INFO("ControlImu: {} success~",enable);
        return true;
    }
    else{
        LOG_INFO("ControlImu: {} failed!",enable);
        return false;
    }
}