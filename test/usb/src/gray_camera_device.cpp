#include "gray_camera_device.h"
#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
#include <unistd.h>
#include <sys/syscall.h>
#include <sched.h>
#include <pthread.h>
#endif

GrayCameraDevice::GrayCameraDevice(const std::string& interface_description):AsyncUSBDevice(interface_description),gray_file_writer_("graycamera_data.bin"){

}


void GrayCameraDevice::OnOpen(TransferContext* transfer_context){
    transfer_context->outbuf.resize(GRAY_FRAME_BUF_SIZE);
    transfer_context->holdbuf.resize(GRAY_FRAME_BUF_SIZE);

#if NEED_PUBLISH_FRAME
    gray_frame_.type = FrameType::GraycameraFromFrames;
    gray_frame_.data.resize(GRAY_FRAME_BUF_SIZE);
    gray_frame_.data_size = 0;
    work_thread_.reset(new std::thread(std::bind(&GrayCameraDevice::PublishFrame, this, transfer_context)));
#endif
}

void GrayCameraDevice::OnProcessPayload(TransferContext* transfer_context){
    uint64_t current_host_time_ns = FMonotonicGetNs();
    if(transfer_context->frame_header.type == FrameType::GraycameraFromFrames){
        NRGrayscaleCameraFrameData* gray_frame = (NRGrayscaleCameraFrameData*)(transfer_context->outbuf.data()+sizeof(FrameHeader));
        TraceFrameRate(current_host_time_ns,"Bulk-Graycamera", &transfer_context->frequency_context);
        LOG_TRACE("Graycamera Raw Data: {}",NRGrayscaleCameraFrameDataWrapper{*gray_frame, current_host_time_ns});

#ifdef NEED_PUBLISH_FRAME
        if(gray_frame->frame_id % 60 == 0) {
            SwapBuffers(transfer_context);
        }
#endif
    }
    else {
        LOG_ERROR("GrayCameraDevice::OnProcessPayload Invalid frame type: {}", transfer_context->frame_header.type);
    }
}

void GrayCameraDevice::SaveDataFrame(const DataFrame& data_frame){
    FrameHeader* frame_header = (FrameHeader*)data_frame.data.data();
    if(data_frame.type == FrameType::GraycameraFromFrames) {
        NRGrayscaleCameraFrameData* gray_frame_data = (NRGrayscaleCameraFrameData*)(data_frame.data.data() + sizeof(FrameHeader));
        uint32_t data_offset = sizeof(FrameHeader) + sizeof(NRGrayscaleCameraFrameData);
        int32_t write_size =  data_frame.data_size - data_offset;
        if(write_size > 0) {
            gray_file_writer_.Write(data_frame.data.data() + data_offset,write_size);
            LOG_TRACE("GrayCameraDevice::SaveDataFrame: gray frame data size:{}",write_size);
        }
        else{
            LOG_WARN("GrayCameraDevice::SaveDataFrame: gray frame data size is too small,data_size:{},data_offset:{},frame_header:type:{},len:{}",
                data_frame.data_size,data_offset,frame_header->type,frame_header->len);
        }
    }
    else{
        LOG_WARN("GrayCameraDevice::SaveDataFrame: Invalid frame type: {}", frame_header->type);
    }
}

void GrayCameraDevice::PublishFrame(TransferContext* transfer_context){
    while(stream_running_) {
        {
            std::unique_lock<std::mutex> lock(gray_mutex_);
            gray_cond_.wait(lock);
            if (!stream_running_) break;
            gray_frame_.data_size = transfer_context->holdbuf_actual_size;
            memcpy(gray_frame_.data.data(), transfer_context->holdbuf.data(), transfer_context->holdbuf_actual_size);
        }
        SaveDataFrame(gray_frame_);
    }
    LOG_INFO("PublishFrame thread exit");
}


void GrayCameraDevice::SwapBuffers(TransferContext* transfer_context){
    std::unique_lock lock(gray_mutex_);
    transfer_context->outbuf.swap(transfer_context->holdbuf);
    transfer_context->holdbuf_actual_size = transfer_context->outbuf_actual_size;
    LOG_TRACE("SwapBuffers: gray frame");
    gray_cond_.notify_one();
}

void GrayCameraDevice::Close(){
    if(has_closed_) return;
    
    stream_running_ = false;
    gray_cond_.notify_one();

#ifdef NEED_PUBLISH_FRAME
    if (work_thread_ && work_thread_->joinable()) {
        work_thread_->join();
    }
#endif
    AsyncUSBDevice::Close();
}