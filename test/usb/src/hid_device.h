#pragma once
#include <iostream>
#include <thread>
#include <mutex>
#include <atomic>
#include <vector>
#include <hidapi.h>
#include "data_types.h"
#include "common.h"
#include "imu_data_converter.h"
#include "record_helper.h"

class HidDevice {
public:
    HidDevice(unsigned short vendor_id, unsigned short product_id, int interface_number,ImuSourceType source_type)
        : vendor_id_(vendor_id), product_id_(product_id),
          interface_number_(interface_number), device_(nullptr), running_(false),imu_source_type_(source_type),record_helper_("Imu Raw Data") {
            record_helper_.start();
          }

    ~HidDevice() {
        close();
    }

    bool open();

    void close();

    bool write(const std::vector<unsigned char>& data);

    bool SendImuCommand(int8_t imu_ids,bool enable);

private:

    void readLoop();

    unsigned short vendor_id_;
    unsigned short product_id_;
    int interface_number_;
    hid_device* device_;
    std::thread read_thread_;
    std::mutex write_mutex_;
    std::atomic<bool> running_;

    FrequencyContext imu_freq_context_;
    FrequencyContext multi_imu_freq_context_;

    ImuConverter converter_;

    ImuSourceType imu_source_type_{IMU_SOURCE_TYPE_FROM_SDK_INTERRUPT};

    RecordHelper<NRImuDataWithTime> record_helper_;

};

