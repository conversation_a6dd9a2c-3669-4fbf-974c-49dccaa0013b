#include "usb_client.h"
#include <common.h>
#include <csignal>

void signal_handler(int signal) {
    StopUsbClient(signal);
}

int main(int argc, char** argv) {
   std::signal(SIGINT, signal_handler);

#ifdef USE_FRAMEWORK
 #if !defined(__ANDROID__)
	Logger::defaultLogger()->set_level(LogLevel::trace);
 #endif
#endif
    if(argc < 2){
        LOG_ERROR("argc < 2");
        return -1;
    }
    int32_t param1 = std::atoi(argv[1]);
    if(param1 < 0 || param1 > 2){
        LOG_ERROR("param1:%d is invalid",param1);
        return -1;
    }
    
    return StartUsbClient(param1,false);
}