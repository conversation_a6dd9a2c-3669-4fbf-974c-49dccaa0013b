#pragma once

#include "async_usb.h"
#include "record_helper.h"

class ImuDevice: public AsyncUSBDevice {
public:

    ImuDevice(const std::string& interface_description);

    ~ImuDevice(){
        std::cout << "~ImuDevice" << std::endl;
        Close();
    }

    void OnEpInOpen(TransferContext* transfer_context,EndpointInfo* ep) override;

    void OnProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context) override;

    bool NeedCopyData() override{
        return true;
    }

    bool NeedGetCurrentHostTime() override {
        return true;
    }


    void Close() override{
        if(has_closed_) return;
        std::cout << "ImuDevice::Close" << std::endl;
        stream_running_ = false;
        record_helper_.stop();
        record_helper_google_.stop();
        record_helper_imu_stats_.stop();
        AsyncUSBDevice::Close();
    }
    
    template<typename T,typename T_WithTime>
    bool ParsePacket(TransferContext* transfer_context,RecordHelper<T_WithTime>& record_helper,uint64_t current_host_time_ns,const std::string& log_tag);

private:
    RecordHelper<NRImuDataWithTime> record_helper_;
    RecordHelper<NRImuDataGoogleWithTime> record_helper_google_;
    RecordHelper<NRImuDataGoogleStatsWithTime> record_helper_imu_stats_;
};