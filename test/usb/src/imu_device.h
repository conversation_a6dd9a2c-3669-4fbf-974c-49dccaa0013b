#pragma once

#include "async_usb.h"
#include "record_helper.h"

class ImuDevice: public AsyncUSBDevice {
public:

    ImuDevice(const std::string& interface_description);

    ~ImuDevice(){
        std::cout << "~ImuDevice" << std::endl;
        Close();
    }

    void OnOpen(TransferContext* transfer_context) override;

    void OnProcessPayload(TransferContext* transfer_context) override;

    bool NeedCopyData() override{
        return true;
    }

    void Close() override{
        if(has_closed_) return;
        std::cout << "ImuDevice::Close" << std::endl;
        stream_running_ = false;
        record_helper_.stop();
        record_helper_ext_.stop();
        AsyncUSBDevice::Close();
    }

private:
    RecordHelper<NRImuDataWithTime> record_helper_;
    RecordHelper<NRImuDataExWithTime> record_helper_ext_;
};