#include "usb_client.h"

#include "async_usb.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <common.h>
#include <data_types.h>
#include "tcp_heartbeat.h"
#include "hid_device.h"
#include "imu_device.h"
#include "heartbeat_device.h"
#include "gray_camera_device.h"
#include "rgb_camera_device.h"

#include <csignal>



std::shared_ptr<HidDevice> g_hid_device;
std::shared_ptr<HeartbeatDevice> heartbeat_device;
std::shared_ptr<ImuDevice> imu_device;

int8_t g_imu_ids = 0;
ImuSourceType g_imu_source_type = ImuSourceType::IMU_SOURCE_TYPE_FROM_SDK_INTERRUPT;
bool g_usb_client_from_axr = false;
void StopUsbClient(int signal){
    if (signal == SIGINT) {
        if( g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_BSP_HID) {
            if(!g_hid_device->SendImuCommand(g_imu_ids,false)){
                LOG_ERROR("SendImuCommand false:failed!");
            }
            else{
                LOG_INFO("SendImuCommand false:success~");
            }
        }
        else if(g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_SDK_INTERRUPT || g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_SDK_HID){
            LOG_INFO("heartbeat_device checking");
            if(!g_usb_client_from_axr) {
                if(heartbeat_device && !heartbeat_device->ControlImu(false)){
                    LOG_ERROR("Can not close imu by heartbeat_device");
                }
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        std::cout << "StopUsbClient SIGINT received\n";
        if(heartbeat_device) {
            heartbeat_device->Close();
        }
        if(imu_device) {
            imu_device->Close();
        }
        TERMINATE_PROCESS();
    }
}


int StartUsbClient(int imu_source_type,bool from_axr){
    g_usb_client_from_axr = from_axr;
#ifdef USE_FRAMEWORK
 #if !defined(__ANDROID__)
	Logger::defaultLogger()->set_level(LogLevel::trace);
 #endif
#endif

    g_imu_source_type = (ImuSourceType)imu_source_type;
    LOG_INFO("g_imu_source_type:{}",(int)g_imu_source_type);

    std::string interface_string = FRAMES_INTERFACE_STRING;
    std::string imu_interface_string = FRAMES_INTERFACE_STRING_IMU;

 #if NEED_USB_HEARTBEAT
    heartbeat_device = std::make_shared<HeartbeatDevice>(interface_string);
    if(!heartbeat_device->Initialize()){
        LOG_ERROR("Can not find frames heartbeat_device");
        return -1;  
    }
    if (!heartbeat_device->open()) {
        LOG_ERROR("Failed to open heartbeat_device");
        return -1;
    }
    if(g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_SDK_INTERRUPT || g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_SDK_HID){
        if(!g_usb_client_from_axr){
            if(!heartbeat_device->ControlImu(true)){
                LOG_ERROR("Can not open imu by heartbeat_device");
                return -1;  
            }
            else{
                LOG_INFO("Open imu by heartbeat_device success");
            }
        }
    }
    heartbeat_device->StartLaunchHeartbeat();
#endif

    if(g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_SDK_INTERRUPT){
        if(!imu_interface_string.empty()) {
            imu_device = std::make_shared<ImuDevice>(imu_interface_string);
            if(!imu_device->Initialize()){
                LOG_ERROR("Can not find frames device");
                return -1;  
            }
            if (!imu_device->open()) {
                std::cerr << "Failed to open device" << std::endl;
                return -1;
            }
        }
    }
    else if(g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_SDK_HID || g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_BSP_HID){
        g_hid_device = std::make_shared<HidDevice>(0x3318, 0x043a, 1,g_imu_source_type);
        if (!g_hid_device->open()) {
            LOG_ERROR("cannot open imu hid interface");
            return -1;
        }
        
        if(g_imu_source_type == ImuSourceType::IMU_SOURCE_TYPE_FROM_BSP_HID){
            // g_imu_ids = NR_IMU_ID_0|NR_IMU_ID_1|NR_IMU_ID_2;
            g_imu_ids = NR_IMU_ID_1;
            if(!g_hid_device->SendImuCommand(g_imu_ids,true)){
                LOG_ERROR("SendImuCommand true failed!");
                return -1;
            }
        }
    }
    else{
        LOG_ERROR("unknown imu source type{}!",(int)g_imu_source_type);
    }

#if USE_TCP_HEART_BEAT
    std::thread tcp_heartbeat_thread(RunTcpHeartbeat);
    tcp_heartbeat_thread.detach();
#endif

    LOG_INFO("Device opened successfully");

    if(!g_usb_client_from_axr) {
        std::this_thread::sleep_for(std::chrono::seconds(100000000));
        //std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    return 0;
}