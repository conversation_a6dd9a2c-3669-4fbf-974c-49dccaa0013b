#include "async_usb.h"
#include <thread>
#include <iostream>
#include <fstream>


#if NEED_OPEN_TRACE
F_TRACE_DEFINE_MODULE("raw_usb", "usb module")
F_TRACE_INIT(usb) 
#endif

bool AsyncUSBDevice::has_send_sig_int_ = false;

AsyncUSBDevice::AsyncUSBDevice(const std::string& interface_description):interface_description_(interface_description){
}

bool AsyncUSBDevice::Initialize(){
    if(libusb_init(&usb_ctx_) != 0){
        LOG_ERROR("libusb_init failed");
        return false;
    }
    else{
       // libusb_set_option(usb_ctx_, LIBUSB_OPTION_LOG_LEVEL, LIBUSB_LOG_LEVEL_DEBUG);
        libusb_set_option(usb_ctx_, LIBUSB_OPTION_LOG_LEVEL, LIBUSB_LOG_LEVEL_INFO);
    }
    if(!FindUsbDevice()){
        LOG_ERROR("FindUsbDevice:{} failed", interface_description_);
        return false;
    }
    return true;
}

AsyncUSBDevice::~AsyncUSBDevice() {
    std::cout << "AsyncUSBDevice::~AsyncUSBDevice" << std::endl;
}

libusb_device_handle * AsyncUSBDevice::GetInterfaceString(libusb_device *dev, int iInterface,std::string& interface_string) {
    libusb_device_handle *handle = nullptr;
    char buffer[256] = {0};
    if (libusb_open(dev, &handle) != 0) {
        LOG_ERROR("GetInterfaceString Failed to open device");
        return nullptr;
    }
    int r = libusb_get_string_descriptor_ascii(handle, iInterface, (unsigned char*)buffer, sizeof(buffer));
    if (r > 0) {
        interface_string = std::string(buffer);
        LOG_INFO("GetInterfaceString interface string: {}",buffer);
    } else {
        LOG_WARN("GetInterfaceString Failed to get iInterface string");
    }
    //libusb_close(handle);
    return handle;
}


bool AsyncUSBDevice::FindUsbDevice() {
    libusb_device** devs = nullptr;
    ssize_t cnt = libusb_get_device_list(usb_ctx_, &devs);
    interface_infos_.clear();

    bool found = false;

    for (ssize_t i = 0; i < cnt; ++i) {
        libusb_device* dev = devs[i];
        libusb_device_descriptor desc;

        if (libusb_get_device_descriptor(dev, &desc) != 0) continue;

        libusb_config_descriptor* config = nullptr;
        if (libusb_get_config_descriptor(dev, 0, &config) != 0) continue;

        for (int i = 0; i < config->bNumInterfaces; ++i) {
            const libusb_interface& iface = config->interface[i];

            for (int j = 0; j < iface.num_altsetting; ++j) {
                const libusb_interface_descriptor& iface_desc = iface.altsetting[j];

                InterfaceInfo interfaceInfo;
                interfaceInfo.interfaceNumber = iface_desc.bInterfaceNumber;
                std::string interface_string;
                libusb_device_handle* handle = GetInterfaceString(dev,iface_desc.iInterface,interface_string);
                if(handle != nullptr && interface_string == interface_description_){
                    device_handle_ = handle;
                    found = true;
                    LOG_INFO("Find frames interface:{}",interface_description_);
                    for (int k = 0; k < iface_desc.bNumEndpoints; ++k) {
                        const libusb_endpoint_descriptor& ep = iface_desc.endpoint[k];
                        interfaceInfo.endpoints.push_back({
                            ep.bEndpointAddress,
                            ep.bmAttributes,
                            ep.wMaxPacketSize,
                            ep.bInterval
                        });
                        if(is_in_endpoint(ep.bEndpointAddress)){
                            in_ep_count_++;
                        }
                        else if(is_out_endpoint(ep.bEndpointAddress)){
                            out_ep_count_++;
                        }
                    }
                    interface_infos_.push_back(interfaceInfo);
                    break;
                }
                else if(handle != nullptr){
                    libusb_close(handle);
                }
            }
            if(found){
                break;
            }
        }
        libusb_free_config_descriptor(config);
        if(found){
            break;
        }
    }

    libusb_free_device_list(devs, 1);

    if (found) {
        for (const auto& iface : interface_infos_) {
            LOG_INFO("Interface: {}", iface.interfaceNumber);

            for (const auto& ep : iface.endpoints) {
                std::string direction;
                if (is_in_endpoint(ep.address)) {
                    direction = "in";
                } else if (is_out_endpoint(ep.address)) {
                    direction = "out";
                } else {
                    direction = "unknown";
                }

                std::string transfer_type_str;
                switch (get_transfer_type(ep)) {
                    case CONTROL: transfer_type_str = "control"; break;
                    case ISOCHRONOUS: transfer_type_str = "isochronous"; break;
                    case BULK: transfer_type_str = "bulk"; break;
                    case INTERRUPT: transfer_type_str = "interrupt"; break;
                    default: transfer_type_str = "unknown"; break;
                }

                LOG_INFO("  Endpoint: 0x{:02x}, Attr: 0x{:02x}, MaxPktSize: {}, Interval: {}, Direction: {}, Type: {}",
                            static_cast<int>(ep.address),
                            static_cast<int>(ep.attributes),
                            ep.maxPacketSize,
                            static_cast<int>(ep.interval),
                            direction,
                            transfer_type_str);
            }
        }
    } else {
        LOG_ERROR("Cannot find interface: {}", interface_description_);
    }

    return found;
}


bool AsyncUSBDevice::ClaimDevice() {
    int ret = libusb_detach_kernel_driver(device_handle_, interface_infos_[0].interfaceNumber); //在mac上，需要sudo执行，才能detach成功，否则会返回没有权限的错误（LIBUSB_ERROR_ACCESS）。
    LOG_DEBUG("libusb_detach_kernel_driver ret {}", ret);

    if (ret == LIBUSB_SUCCESS || ret == LIBUSB_ERROR_NOT_FOUND || ret == LIBUSB_ERROR_NOT_SUPPORTED) {
        ret = libusb_claim_interface(device_handle_, interface_infos_[0].interfaceNumber);
        if ( ret != LIBUSB_SUCCESS) {
            LOG_DEBUG("libusb_claim_interface ret {}", ret);
            libusb_close(device_handle_);
            device_handle_ = nullptr;
        }
        else{
            LOG_INFO("ClaimDevice: claim interface {} success", interface_infos_[0].interfaceNumber);
            return true;
        }
    } else {
        LOG_DEBUG("not claiming interface {}: unable to detach kernel driver ({})", interface_infos_[0].interfaceNumber, ret);
    }
    return false;
}

int AsyncUSBDevice::WriteUsbData(const unsigned char *data, size_t length, TransferContext* transfer_context){
	int actual_length=0;
	int res = 0;
    int retry = 3;
    while (retry-- > 0) {
        res = libusb_interrupt_transfer(device_handle_,
            transfer_context->out_end_point_info.address,
            (unsigned char*)data,
            length,
            &actual_length, 1000);
        if (res == 0) break; // 成功

        if (res == LIBUSB_ERROR_INTERRUPTED || res == LIBUSB_ERROR_TIMEOUT || res == LIBUSB_ERROR_OVERFLOW) {
            LOG_WARN("WriteUsbData: transfer error:{}, ep_index:{},need retry, retry left:{}",res,transfer_context->ep_index,retry);
            continue; 
        }

        if (res == LIBUSB_ERROR_PIPE) {
            LOG_WARN("WriteUsbData: LIBUSB_ERROR_PIPE, clear halt and retry, ep_index:{}",transfer_context->ep_index);
            libusb_clear_halt(device_handle_, transfer_context->out_end_point_info.address);
            continue; 
        }

        if( res == LIBUSB_ERROR_NO_DEVICE) {
            stream_running_ = false;
            LOG_ERROR("WriteUsbData: LIBUSB_ERROR_NO_DEVICE, ep_index:{}",transfer_context->ep_index);
            if(!has_send_sig_int_) {
                LOG_INFO("WriteUsbData: send sigint");
                has_send_sig_int_ = true;
                send_sigint();
            }
            break;
        }

        LOG_ERROR("WriteUsbData: transfer error:{}, ep_index:{}",res,transfer_context->ep_index);
        break;
    }

    return res == 0? actual_length :-1;
}


void AsyncUSBDevice::TraceFrameRate(uint64_t current_time_ns, const std::string& tag, FrequencyContext* frequency_context){
    DoTraceFrameRate(current_time_ns,tag,frequency_context);
}


bool AsyncUSBDevice::open() {
    if (!ClaimDevice()) return false;
    
    stream_running_ = true;
 
    handle_event_thread_.reset(new std::thread(std::bind(&AsyncUSBDevice::HandleEvents, this)));

    transfer_contexts_.resize(in_ep_count_);

    uint32_t ep_index = 0;
    uint32_t in_ep_index = 0;
    uint32_t out_ep_index = 0;
    //interface_infos_[0].endpoints的顺序有要求，必须是in,out,in,out,in,out ...成对出现
    for(auto& ep : interface_infos_[0].endpoints){
        if(is_in_endpoint(ep.address)){
            TransferContext* cur_context = &(transfer_contexts_[in_ep_index]);
            cur_context->ep_index = ep_index;
            cur_context->in_end_point_info = ep;  
            cur_context->async_usb_device = this;
            OnEpInOpen(cur_context,&ep);
            cur_context->leftover_buf.resize(TRANSFER_BUF_MAX_SIZE);
            for (uint32_t transfer_id = 0; transfer_id < cur_context->transfer_actual_count; ++transfer_id) {
                libusb_transfer *transfer = libusb_alloc_transfer(0);
                cur_context->transfers[transfer_id] = transfer;
                cur_context->transfer_bufs[transfer_id] = (uint8_t *)malloc (cur_context->transfer_buf_actual_len);
                LOG_DEBUG("alloc transfer: 0x{:p}", (void *)transfer);
                if(get_transfer_type(ep) == BULK){
                    libusb_fill_bulk_transfer ( transfer, device_handle_,
                        ep.address,
                        cur_context->transfer_bufs[transfer_id],
                        cur_context->transfer_buf_actual_len, TransferCallback,
                        ( void* ) cur_context, 5000 ); // 5000ms timeout
                }
                else if(get_transfer_type(ep) == INTERRUPT){
                    libusb_fill_interrupt_transfer ( transfer, device_handle_,
                        ep.address,
                        cur_context->transfer_bufs[transfer_id],
                        cur_context->transfer_buf_actual_len, TransferCallback,
                        ( void* ) cur_context, 5000 ); 
                }
                
            }
            in_ep_index++; 
        }
        else if(is_out_endpoint(ep.address)){
            TransferContext* cur_context = &(transfer_contexts_[out_ep_index]);
            cur_context->out_end_point_info = ep;
            out_ep_index++;
        }
        ep_index++;
    }

    int ret = 0;
    for(auto& context : transfer_contexts_){
        uint32_t i = 0;
        for(i = 0; i < context.transfer_actual_count; ++i){
            if(context.transfers[i] != nullptr){
                //LOG_DEBUG("submit transfer: 0x{:p}", (void *)context.transfers[i]);
                ret = libusb_submit_transfer(context.transfers[i]);
                if(ret != LIBUSB_SUCCESS){
                    LOG_ERROR("libusb_submit_transfer error {}", ret);
                    break;
                }
            }
        }
        if(ret != LIBUSB_SUCCESS && i > 0){
            for(;i< context.transfer_actual_count; i++) {
                //LOG_INFO("submit transfer error, libusb_free_transfer: 0x{:p}", (void *)context.transfers[i]);
                free(context.transfer_bufs[i]);
                libusb_free_transfer(context.transfers[i]);
                context.transfer_bufs[i] = nullptr;
                context.transfers[i] = nullptr;
            }
        }
    }

    OnPostOpen(&transfer_contexts_[0]);

    return true;
}

void AsyncUSBDevice::Stop(){
    
    std::unique_lock<std::mutex> lock(cb_mutex_);
    for(auto& context : transfer_contexts_){
        for(uint32_t i = 0; i < context.transfer_actual_count; ++i){
            if(context.transfers[i] != nullptr){
                std::cout << "AsyncUSBDevice::close cancel transfer,i:" <<  i <<  std::endl; 
                 int r = libusb_cancel_transfer(context.transfers[i]);
                if (r != 0) {
                    std::cerr << "Cancel failed: " << libusb_error_name(r) << std::endl;
                }
            }
        }
    }

    for(auto& context : transfer_contexts_){
        do {
            uint32_t i = 0;
            for(; i < context.transfer_actual_count; ++i){
                if(context.transfers[i] != NULL)
                    break;
            }
            if(i == context.transfer_actual_count) break;
            std::cout << "AsyncUSBDevice::close wait transfer begin:i:" <<  i <<  std::endl;
            cb_cond_.wait(lock);
            std::cout << "AsyncUSBDevice::close wait transfer end:i:" <<  i <<  std::endl;
        } while(1);
    }
    std::cout << "AsyncUSBDevice::Stop end" << std::endl;
}


void AsyncUSBDevice::Close() {
    if(has_closed_) return;
    //do not use spdlog in this function
    std::cout << "AsyncUSBDevice::close" << std::endl;

    if (!device_handle_) return;
    Stop();
    kill_handler_thread_ = 1;
    std::cout << "handle_event_thread_ join begin:interface_description_:" << interface_description_ << std::endl;
    if (handle_event_thread_ && handle_event_thread_->joinable()) {
        handle_event_thread_->join();
    }
    std::cout << "handle_event_thread_ join end:interface_description_:" << interface_description_ << std::endl;

    libusb_release_interface(device_handle_, interface_infos_[0].interfaceNumber);
    /* Reattach any kernel drivers that were disabled when we claimed this interface */
    libusb_attach_kernel_driver(device_handle_, interface_infos_[0].interfaceNumber);
    libusb_close(device_handle_);
    device_handle_ = nullptr;
    libusb_exit(NULL);
    has_closed_ = true;
}

#define DEBUG_PROCESS_PAYLOAD 0
void AsyncUSBDevice::ProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context, uint8_t* data, int data_length){
#if NEED_OPEN_TRACE
    F_TRACE_EVENT("raw_usb", "ProcessPayload");
#endif
    if(transfer_context->leftover_buf_actual_size > 0){
        memcpy(transfer_context->outbuf.data()+transfer_context->outbuf_actual_size, transfer_context->leftover_buf.data(), transfer_context->leftover_buf_actual_size);
        transfer_context->outbuf_actual_size += transfer_context->leftover_buf_actual_size;
        transfer_context->leftover_buf_actual_size = 0;
    }

    if(transfer_context->outbuf_actual_size >= transfer_context->outbuf.size()) {
        LOG_ERROR("ProcessPayload: outbuf is full");
        transfer_context->Reset();
        return;
    }

#if DEBUG_PROCESS_PAYLOAD
    LOG_TRACE("transfer_context->outbuf_actual_size={},data_length={},description={}",transfer_context->outbuf_actual_size,data_length,interface_description_);
#endif

    memcpy(transfer_context->outbuf.data()+transfer_context->outbuf_actual_size, data, data_length);
    transfer_context->outbuf_actual_size += data_length;

    if(!transfer_context->has_parsed_header) {
        if(transfer_context->outbuf_actual_size >= sizeof(FrameHeader)){
            transfer_context->frame_header = *((FrameHeader*)transfer_context->outbuf.data());
            if(!IsValidFrameType((FrameType)transfer_context->frame_header.type)){
                LOG_ERROR("ProcessPayload: Invalid frame type: {},data_length:{},description={}", transfer_context->frame_header.type,data_length,interface_description_);
                transfer_context->Reset();
                return;
            }

#if DEBUG_PROCESS_PAYLOAD
            LOG_TRACE("ProcessPayload: frame_header: len:{},type:{},description={}", 
                transfer_context->frame_header.len, transfer_context->frame_header.type,interface_description_);
#endif
            transfer_context->has_parsed_header = true;
        }
    }

    if(transfer_context->has_parsed_header) {
        uint32_t frame_total_len = transfer_context->frame_header.len + sizeof(FrameHeader);
        if(transfer_context->outbuf_actual_size >= frame_total_len){
            uint32_t leftover_size  = transfer_context->outbuf_actual_size - frame_total_len;
            transfer_context->outbuf_actual_size -=leftover_size;
            OnProcessPayload(current_host_time_ns,transfer_context);
            if(leftover_size > 0){
                memcpy(transfer_context->leftover_buf.data(), transfer_context->outbuf.data()+frame_total_len, leftover_size);
                transfer_context->leftover_buf_actual_size = leftover_size;

#if DEBUG_PROCESS_PAYLOAD
                if(transfer_context->leftover_buf_actual_size >= sizeof(FrameHeader)){
                    FrameHeader temp_frame_header = *((FrameHeader*)transfer_context->leftover_buf.data());
                    LOG_TRACE("ProcessPayload: temp_frame_header: len:{},type:{},description={}", 
                        temp_frame_header.len, temp_frame_header.type,interface_description_);
                    if(!IsValidFrameType((FrameType)temp_frame_header.type)){
                        LOG_ERROR("ProcessPayload: temp_frame_header Invalid frame type: {},description={}", 
                            temp_frame_header.type,interface_description_);
                    }
                }
#endif
            }
            else{
                transfer_context->leftover_buf_actual_size = 0;
            }
            //reset
            transfer_context->Reset();
        }
    }
}

void AsyncUSBDevice::TryFreeTransferAndNotify(TransferContext* context,libusb_transfer* transfer){
    std::unique_lock lock(context->async_usb_device->cb_mutex_);
    std::cout << "TransferCallback status " << (int)transfer->status << std::endl;
    //LOG_ERROR("TransferCallback status {} ", (int)transfer->status);
    uint32_t i;
    for(i=0; i < context->transfer_actual_count; i++) {
        if(context->transfers[i] == transfer) {
            std::cout << "TransferCallback libusb_free_transfer " << i << " (0x" << std::hex << (void *)transfer << ")" << std::dec << std::endl;
            //LOG_DEBUG("TransferCallback libusb_free_transfer {} (0x{:p})", i, (void *)transfer);
            libusb_free_transfer(transfer);
            free(transfer->buffer);
            context->transfers[i] = NULL;
            context->transfer_bufs[i] = NULL;
            break;
        }
    }
    if(i == context->transfer_actual_count ) {
        std::cout << "TransferCallback transfer 0x" << std::hex << (void *)transfer << " not found; not freeing!" << std::endl;
        //LOG_DEBUG("transfer 0x{:p} not found; not freeing!", (void*)transfer);
    }

    context->async_usb_device->cb_cond_.notify_all(); // signal that the transfer is done
}


void LIBUSB_CALL AsyncUSBDevice::TransferCallback(libusb_transfer* transfer) {
#if NEED_OPEN_TRACE
        F_TRACE_EVENT("raw_usb", "TransferCallback");
#endif
    TransferContext* context = static_cast<TransferContext*>(transfer->user_data);
    int resubmit = 1;
    bool need_copy_data = context->async_usb_device->NeedCopyData();

    uint64_t current_host_time_ns = 0;
    switch (transfer->status) {
    case LIBUSB_TRANSFER_COMPLETED: {
            if(!need_copy_data){
                if (transfer->num_iso_packets == 0) {

                    current_host_time_ns = FMonotonicGetNs();

                    // if(context->async_usb_device->NeedGetCurrentHostTime()){ //if device is gray or rgb, do not GetCurrentHostTime here!
                    //     current_host_time_ns = FMonotonicGetNs();

                    //     // std::string interface_des;
                    //     // context->async_usb_device->GetInterfaceDescription(interface_des);
                    //     // LOG_TRACE("TransferCallback no need copy and need get current host time, current_host_time_ns {},interface_des {}", current_host_time_ns,interface_des);
                    // }
                    context->async_usb_device->ProcessPayload(current_host_time_ns,context, transfer->buffer, transfer->actual_length);
                } else {
                    LOG_ERROR("TransferCallback num_iso_packets {} ", transfer->num_iso_packets);
                }
            }
        }
        break;
    case LIBUSB_TRANSFER_CANCELLED:
    case LIBUSB_TRANSFER_ERROR:
    case LIBUSB_TRANSFER_NO_DEVICE: {
        context->async_usb_device->TryFreeTransferAndNotify(context,transfer);
        resubmit = 0;
        break;
    }
    case LIBUSB_TRANSFER_TIMED_OUT:
    case LIBUSB_TRANSFER_STALL:
    case LIBUSB_TRANSFER_OVERFLOW:
        LOG_ERROR("retrying transfer, status = {}", (int)transfer->status);
        break;
    }

    if ( resubmit ) {
        if (context->async_usb_device->stream_running_) {

            if(need_copy_data){
                //copy data
                current_host_time_ns = FMonotonicGetNs();
                if(transfer->actual_length > 0 && (uint32_t)transfer->actual_length <= sizeof(context->one_transfer_buf)){
                    memcpy(context->one_transfer_buf, transfer->buffer, transfer->actual_length);
                    context->one_transfer_buf_actual_size = transfer->actual_length;
                }
                else if(transfer->actual_length > 0){
                    LOG_ERROR("AsyncUSBDevice::TransferCallback actual_length {} > sizeof(context->one_transfer_buf) {}", transfer->actual_length, sizeof(context->one_transfer_buf));
                    context->one_transfer_buf_actual_size = 0;
                }
                else{
                    LOG_ERROR("AsyncUSBDevice::TransferCallback actual_length {} <= 0", transfer->actual_length);
                    context->one_transfer_buf_actual_size = 0;
                }
            }

            int libusbRet = libusb_submit_transfer(transfer);
            if(libusbRet == LIBUSB_SUCCESS){
                if(need_copy_data && context->one_transfer_buf_actual_size > 0){
                    context->async_usb_device->ProcessPayload(current_host_time_ns, context, context->one_transfer_buf,  context->one_transfer_buf_actual_size);
                }
            }
            else{
                LOG_ERROR("libusb_submit_transfer failed, status = {}", (int)transfer->status);
                //TODO 释放transfer
            }
        } else {
            std::cout << "event_thread has exit"<< std::endl;
            context->async_usb_device->TryFreeTransferAndNotify(context,transfer);
        }
    }
}

void AsyncUSBDevice::HandleEvents() {
    timeval tv{0, 500000}; // 500ms
    // struct timeval tv;
	// tv.tv_sec = 1;
	// tv.tv_usec = 0;

    while (!kill_handler_thread_) {
#if NEED_OPEN_TRACE
        F_TRACE_EVENT("raw_usb", "HandleEvents");
#endif
        //LOG_DEBUG("HandleEvents begin");
        int res = libusb_handle_events_timeout_completed(usb_ctx_, &tv, &kill_handler_thread_);
        //LOG_DEBUG("HandleEvents end ret={}", res);

        if (res < 0 ) {
			//LOG_ERROR("HandleEvents11 encounter an error:{}",libusb_error_name(res));

			if (res != LIBUSB_ERROR_BUSY &&
			    res != LIBUSB_ERROR_TIMEOUT &&
			    res != LIBUSB_ERROR_OVERFLOW &&
			    res != LIBUSB_ERROR_INTERRUPTED) {
                kill_handler_thread_ = true;
				LOG_ERROR("HandleEvents22 encounter an error:{}",libusb_error_name(res));
				break;
			}
        }
    }
}