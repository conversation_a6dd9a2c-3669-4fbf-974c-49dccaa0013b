#include "rgb_camera_device.h"
#if defined(__ANDROID__) || defined(linux) || defined(__linux__)
#include <unistd.h>
#include <sys/syscall.h>
#include <sched.h>
#include <pthread.h>
#endif

RgbCameraDevice::RgbCameraDevice(const std::string& interface_description):AsyncUSBDevice(interface_description),rgb_file_writer_("rgb_data.bin"){

}


void RgbCameraDevice::OnOpen(TransferContext* transfer_context){
    transfer_context->outbuf.resize(RGB_FRAME_BUF_SIZE);
    transfer_context->holdbuf.resize(RGB_FRAME_BUF_SIZE);

#if NEED_PUBLISH_FRAME
    rgb_frame_.type = FrameType::RgbFromFrames;
    rgb_frame_.data.resize(RGB_FRAME_BUF_SIZE);
    rgb_frame_.data_size = 0;
    rgb_work_thread_.reset(new std::thread(std::bind(&RgbCameraDevice::PublishFrame, this, transfer_context)));
#endif
}

void RgbCameraDevice::OnProcessPayload(TransferContext* transfer_context){
    uint64_t current_host_time_ns = FMonotonicGetNs();
    if(transfer_context->frame_header.type == FrameType::RgbFromFrames){
        NRRgbCameraFrameData* rgb_frame = (NRRgbCameraFrameData*)(transfer_context->outbuf.data()+sizeof(FrameHeader));
        TraceFrameRate(current_host_time_ns,"Bulk-RgbCamera", &transfer_context->frequency_context);
        LOG_TRACE("Rgbcamera Raw Data: {}",NRRgbCameraFrameDataWrapper{*rgb_frame, current_host_time_ns});
#ifdef NEED_PUBLISH_FRAME
        SwapBuffers(transfer_context);
#endif
    }
    else {
        LOG_ERROR("RgbCameraDevice::OnProcessPayload Invalid frame type: {}", transfer_context->frame_header.type);
    }
}

void RgbCameraDevice::SaveDataFrame(const DataFrame& data_frame){
    FrameHeader* frame_header = (FrameHeader*)data_frame.data.data();
    if(data_frame.type == FrameType::RgbFromFrames) {
        uint32_t data_offset = sizeof(FrameHeader) + sizeof(NRRgbCameraFrameData);
        int32_t write_size =  data_frame.data_size - data_offset;
        if(write_size > 0) {
            rgb_file_writer_.Write(data_frame.data.data() + data_offset,write_size);
            LOG_TRACE("SaveDataFrame: rgb frame data size:{}",write_size);
        }
        else{
            LOG_WARN("SaveDataFrame: rgb frame data size is too small");
        }
    }
    else{
        LOG_WARN("RgbCameraDevice::SaveDataFrame: Invalid frame type: {}", frame_header->type);
    }
}

void RgbCameraDevice::PublishFrame(TransferContext* transfer_context){
    while(stream_running_) {
        {
            std::unique_lock<std::mutex> lock(rgb_mutex_);
            rgb_cond_.wait(lock);
            if (!stream_running_) break;
            rgb_frame_.data_size = transfer_context->holdbuf_actual_size;
            memcpy(rgb_frame_.data.data(), transfer_context->holdbuf.data(), transfer_context->holdbuf_actual_size);
        }
        SaveDataFrame(rgb_frame_);
    }
    LOG_INFO("PublishRgbFrame thread exit");
}


void RgbCameraDevice::SwapBuffers(TransferContext* transfer_context){
    std::unique_lock lock(rgb_mutex_);
    transfer_context->outbuf.swap(transfer_context->holdbuf);
    transfer_context->holdbuf_actual_size = transfer_context->outbuf_actual_size;
    rgb_cond_.notify_one();
}

void RgbCameraDevice::Close(){
    if(has_closed_) return;

    stream_running_ = false;
    rgb_cond_.notify_one();

#ifdef NEED_PUBLISH_FRAME
    if (rgb_work_thread_ && rgb_work_thread_->joinable()) {
        rgb_work_thread_->join();
    }
#endif
    AsyncUSBDevice::Close();
}