#pragma once

#include "async_usb.h"
#include "record_helper.h"

class HeartbeatDevice: public AsyncUSBDevice {
public:
    HeartbeatDevice(const std::string& interface_description);


    ~HeartbeatDevice(){
        std::cout << "~HeartbeatDevice" << std::endl;
        Close();
    }

    void StartLaunchHeartbeat();

    void OnEpInOpen(TransferContext* transfer_context,EndpointInfo* ep) override;

    void OnProcessPayload(uint64_t current_host_time_ns,TransferContext* transfer_context) override;

    bool NeedCopyData() override{
        return true;
    }

    bool NeedGetCurrentHostTime() override {
        return true;
    }

    void OnPostOpen(TransferContext* transfer_context) override;

    void Close() override;

    bool ControlImu(bool enable);

    void SendHeartbeatThead(TransferContext* transfer_context);
private:
    std::shared_ptr<std::thread> heartbeat_thread_;
    RecordHelper<NRHeartBeat> record_helper_;
    TransferContext* cur_transfer_context_{nullptr};
};