#pragma once

#include "async_usb.h"
#include "record_helper.h"

class HeartbeatDevice: public AsyncUSBDevice {
public:
    HeartbeatDevice(const std::string& interface_description);


    ~HeartbeatDevice(){
        std::cout << "~HeartbeatDevice" << std::endl;
        Close();
    }

    void OnOpen(TransferContext* transfer_context) override;

    void OnProcessPayload(TransferContext* transfer_context) override;

    bool NeedCopyData() override{
        return true;
    }

    void OnPostOpen(TransferContext* transfer_context) override;

    void Close() override;

    bool ControlImu(bool enable);

    void SendHeartbeatThead(TransferContext* transfer_context);
private:
    std::shared_ptr<std::thread> heartbeat_thread_;
    RecordHelper<NRHeartBeat> record_helper_;
};