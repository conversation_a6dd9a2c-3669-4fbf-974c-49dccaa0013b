#pragma once
#include <condition_variable>
#include <thread>
#include <common.h>

template<typename T>
class RecordHelper {
public:
    explicit RecordHelper(const std::string& log_tag, int format_type = 0, int buffer_size = 2400*60) //1min的imu
        : log_tag_(log_tag),
          format_type_(format_type),
          buffer_(buffer_size),
          stop_flag_(false) {}

    ~RecordHelper() {
        //stop();
    }

    void start() {
        stop_flag_ = false;
        thread_ = std::thread([this]() {
            while (!stop_flag_) {
                T* item = buffer_.GetReadableBuffer();
                if (item) {
                    printItem(*item);
                    buffer_.DoneReadBuffer();
                } else {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1));  // 减少CPU占用
                }
            }

            // 清空剩余数据
            T* item = nullptr;
            while ((item = buffer_.GetReadableBuffer()) != nullptr) {
                printItem(*item);
                buffer_.DoneReadBuffer();
            }
        });
    }

    void push(const T& item) {
        T* slot = buffer_.GetWritableBuffer();
        if (slot) {
            *slot = item;
            buffer_.DoneWriteBuffer();
        } else {
            // 缓冲区满了，丢弃或记录警告
            LOG_WARN("RingBuffer full, dropped item for tag: {}", log_tag_);
        }
    }

    void stop() {
        stop_flag_ = true;
        if (thread_.joinable()) {
            thread_.join();
        }
    }

private:
    void printItem(const T& item) {
        if (format_type_ == 0) {
            LOG_DEBUG("{}: {}", log_tag_, item);
        } else {
            LOG_DEBUG("{}:{}", log_tag_, item);
        }
    }

private:
    std::string log_tag_;
    int format_type_{0};
    RingBufferRW<T> buffer_;
    std::thread thread_;
    std::atomic<bool> stop_flag_;
};