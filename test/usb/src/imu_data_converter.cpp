#include "imu_data_converter.h"


ImuConverter::ImuConverter() {
    Init(default_imu_json);
}

void ImuConverter::Init(const std::string &json_str) {
    try {
        struct_parser_.initialize(json_str);

//        struct_parser_.initConvertUnit(proto_name_, "timestamp", unit_imu_ts_);
//        struct_parser_.initConvertUnit(proto_name_, "sensor_timestamp", unit_sensor_ts_);
        struct_parser_.initConvertUnit(proto_name_, "temperature", unit_temperature_);

        struct_parser_.initConvertUnit(proto_name_, "gyro_numerator", unit_gyro_numerator_);
        struct_parser_.initConvertUnit(proto_name_, "gyro_denominator", unit_gyro_denominator_);
        struct_parser_.initConvertUnit(proto_name_, "acc_numerator", unit_acc_numerator_);
        struct_parser_.initConvertUnit(proto_name_, "acc_denominator", unit_acc_denominator_);
        struct_parser_.initConvertUnit(proto_name_, "mag_offset", unit_mag_offset_);
        struct_parser_.initConvertUnit(proto_name_, "mag_denominator", unit_mag_denominator_);

        struct_parser_.initConvertUnit(proto_name_, "gyro_swap", unit_gyro_swap_);
        struct_parser_.initConvertUnit(proto_name_, "acc_swap", unit_acc_swap_);
        struct_parser_.initConvertUnit(proto_name_, "mag_swap", unit_mag_swap_);
    }
    catch (std::exception& e) {
        LOG_ERROR("Imu Converter Init, error={}", e.what());
    }
    catch (...) {
        LOG_ERROR("Imu Converter Init error");
    }
}

void ImuConverter::Clear() {
//    unit_imu_ts_.clear();
//    unit_sensor_ts_.clear();
    unit_temperature_.clear();

    unit_gyro_numerator_.clear();
    unit_gyro_denominator_.clear();
    unit_acc_numerator_.clear();
    unit_acc_denominator_.clear();
    unit_mag_offset_.clear();
    unit_mag_denominator_.clear();

    unit_gyro_swap_.clear();
    unit_acc_swap_.clear();
    unit_mag_swap_.clear();
}
