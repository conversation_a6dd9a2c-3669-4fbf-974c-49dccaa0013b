#pragma once

#include <mutex>
#include <thread>
#include <condition_variable>

#include <time.h>
#include <cstdint>

#include <fmt/core.h>
#include <fmt/format.h>
#include <string>

#include <json/json.h>

#ifdef _WIN32
#include <windows.h>
#include <stdint.h>
#endif

#include <stdint.h>
#include <atomic>
#include <vector>

#define OS_NS_PER_SEC (1000000000)

#ifdef _WIN32
static inline uint64_t FMonotonicGetNs()
{
    static LARGE_INTEGER frequency = {};
    if (frequency.QuadPart == 0) {
        QueryPerformanceFrequency(&frequency);
    }

    LARGE_INTEGER counter;
    QueryPerformanceCounter(&counter);

    return (uint64_t)(OS_NS_PER_SEC * counter.QuadPart / frequency.QuadPart);
}

static inline void FNanoSleep(int64_t nsec)
{
    if (nsec <= 0) return;

    HANDLE timer = CreateWaitableTimer(NULL, TRUE, NULL);
    if (!timer) return;

    LARGE_INTEGER li;
    li.QuadPart = -nsec / 100; // Convert to 100ns units

    SetWaitableTimer(timer, &li, 0, NULL, NULL, FALSE);
    WaitForSingleObject(timer, INFINITE);
    CloseHandle(timer);
}

#else
static inline uint64_t FMonotonicGetNs()
{
    struct timespec ts;
    int ret = clock_gettime(CLOCK_MONOTONIC, &ts);
    if (ret != 0) {
        return 0;
    }
    uint64_t ns = 0;
    ns += (uint64_t)ts.tv_sec * OS_NS_PER_SEC;
    ns += (uint64_t)ts.tv_nsec;
    return ns;
}

static inline void FNanoSleep(int64_t nsec)
{
    struct timespec spec;
    spec.tv_sec = (nsec / OS_NS_PER_SEC);
    spec.tv_nsec = (nsec % OS_NS_PER_SEC);
    nanosleep(&spec, NULL);
}

#endif


template <typename Value>
class RingBufferRW {
   public:
    typedef typename std::vector<Value> ValueVec;

   public:
    RingBufferRW(int32_t buffer_size)
        : buffer_(buffer_size),
          buffer_size_(buffer_size),
          cur_read_index_(0),
          cur_write_index_(0) {}

	RingBufferRW(RingBufferRW&& other) {
		buffer_ = std::move(other.buffer_);
		buffer_size_ = other.buffer_size_;
		cur_read_index_ = other.cur_read_index_.load();
		cur_write_index_ = other.cur_write_index_.load();
	}

    /***********************************
     *
     *		pay attention:
     *		1. getWritable and doneWrite is in one thread
     *		2. getReadable and doneRead is in another thread
     *
     **********************************/
    Value* GetWritableBuffer() {
        if ((int32_t)(cur_write_index_ - cur_read_index_) >= buffer_size_)
            return nullptr;

        int32_t index = cur_write_index_ % buffer_size_;
        return &buffer_[index];
    }
    void DoneWriteBuffer() { ++cur_write_index_; }
    Value* GetReadableBuffer() {
        if (cur_read_index_ >= cur_write_index_) return nullptr;

        int32_t index = cur_read_index_ % buffer_size_;
        return &buffer_[index];
    }
    void DoneReadBuffer() { ++cur_read_index_; }
    int32_t GetSize() const { return buffer_size_; }

   protected:
    ValueVec buffer_;
    int32_t buffer_size_;
    std::atomic<int64_t> cur_read_index_;
    std::atomic<int64_t> cur_write_index_;
};

#define LOG_TRACE(format, ...) fmt::print("[TRACE] " format "\n", ##__VA_ARGS__);

#define LOG_DEBUG(format, ...) fmt::print("[DEBUG] " format "\n", ##__VA_ARGS__);

#define LOG_INFO(format, ...)  fmt::print("[INFO ] " format "\n", ##__VA_ARGS__);

#define LOG_WARN(format, ...)  fmt::print("[WARN ] " format "\n", ##__VA_ARGS__);

#define LOG_ERROR(format, ...) fmt::print("[ERROR] " format "\n", ##__VA_ARGS__);

#define LOG_FATAL(format, ...) fmt::print("[FATAL] " format "\n", ##__VA_ARGS__);
