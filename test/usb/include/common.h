#pragma once
#include <vector>
#include <libusb.h>
#include <string>
#include <iostream>

#include "system_config.h"


#ifdef USE_FRAMEWORK
 #if !defined(__ANDROID__)
    #include <framework/util/log.h>
    using namespace framework::util::log;
    #define LOGI(...)
 #else
    #include <log.h>
 #endif

    #include <framework/util/os_time.h>
    #include <framework/util/json.h>
    #include <framework/util/trace_manager.h>
    #include <framework/util/ringbuffer.h>

    using namespace framework::util;
#else
    #include "util.h"
#endif


//是否需要打开USB heartbeat功能
#define NEED_USB_HEARTBEAT 1

//是否需要打开TCP heartbeat功能
#define USE_TCP_HEART_BEAT 0

//tcp heartbeat是否同步发送（在一个线程发和收）
#define USE_SYNC_TCP_HEART_BEAT  1

//是否打开trace功能
#define NEED_OPEN_TRACE 0

#define FRAMES_INTERFACE_STRING "com.google.frames-protocol"

#define FRAMES_INTERFACE_STRING_CAMERA "com.google.frames-protocol-camera"

#define FRAMES_INTERFACE_STRING_IMU "com.google.frames-protocol-imu"

//是否使用Google格式的imu数据
#define USE_GOOGLE_IMU 1

struct EndpointInfo {
    uint8_t address;
    uint8_t attributes;
    uint16_t maxPacketSize;
    uint8_t interval;
};

struct InterfaceInfo {
    int interfaceNumber;
    std::vector<EndpointInfo> endpoints;
};
struct FrequencyContext{
    uint64_t last_print_fps_time{0};
    uint64_t packet_count{0};
};

enum EndpointTransferType {
    CONTROL = 0x00,
    ISOCHRONOUS = 0x01,
    BULK = 0x02,
    INTERRUPT = 0x03
};


#if defined(_WIN32) || defined(_WIN64)
    #include <windows.h>
    #define TERMINATE_PROCESS() ExitProcess(1)
    #include <process.h>
    inline void send_sigint(void) {
        // 尝试通过 CRT raise() 触发 SIGINT
        if (raise(SIGINT) != 0) {
            fprintf(stderr, "raise(SIGINT) failed.\n");
        }

        // 额外发送 Ctrl+C 事件（仅控制台程序有效）
        if (!GenerateConsoleCtrlEvent(CTRL_C_EVENT, 0)) {
            fprintf(stderr, "GenerateConsoleCtrlEvent failed. Error: %lu\n", GetLastError());
        }
    }
#else
    #include <unistd.h>
    #include <sys/types.h>  
    #include <signal.h>     
    #define TERMINATE_PROCESS() _exit(1)
    inline void send_sigint(void) {
        pid_t pid = getpid();
        if (kill(pid, SIGINT) != 0) {
            perror("kill(SIGINT) failed");
        }
    }
#endif

inline bool is_in_endpoint(uint8_t address) {
    return (address & LIBUSB_ENDPOINT_IN) != 0; // LIBUSB_ENDPOINT_IN == 0x80
}

inline bool is_out_endpoint(uint8_t address) {
    return (address & LIBUSB_ENDPOINT_DIR_MASK) == LIBUSB_ENDPOINT_OUT; // LIBUSB_ENDPOINT_OUT == 0x00
}

inline EndpointTransferType get_transfer_type(const EndpointInfo& ep) {
    return static_cast<EndpointTransferType>(ep.attributes & 0x03);
}


inline void DoTraceFrameRate(uint64_t current_time_ns, const std::string& tag, FrequencyContext* frequency_context){
    frequency_context->packet_count++;
    if(frequency_context->last_print_fps_time == 0) {
        frequency_context->last_print_fps_time = current_time_ns;
        return;
    }

    uint64_t ns = current_time_ns - frequency_context->last_print_fps_time;
    if (ns >= OS_NS_PER_SEC)
    {
        uint64_t fps = frequency_context->packet_count * OS_NS_PER_SEC / ns;
        LOG_INFO("{} frequency: {} Hz (packets: {})", tag, fps,frequency_context->packet_count);
        frequency_context->packet_count = 0;
        frequency_context->last_print_fps_time = current_time_ns;
    }
}



