#pragma once
#include <fmt/core.h>
#include <fmt/format.h>

#if defined(__ANDROID__)
#include <android/log.h>
#define LOG_TAG "MyUsbLog"
#define LOGI(...)  __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#else
#define LOGI(...)
#endif


#define LOG_TRACE(format, ...) fmt::print("[TRACE] " format "\n", ##__VA_ARGS__);

#define LOG_DEBUG(format, ...) fmt::print("[DEBUG] " format "\n", ##__VA_ARGS__);

#define LOG_INFO(format, ...)  fmt::print("[INFO ] " format "\n", ##__VA_ARGS__);

#define LOG_WARN(format, ...)  fmt::print("[WARN ] " format "\n", ##__VA_ARGS__);

#define LOG_ERROR(format, ...) fmt::print("[ERROR] " format "\n", ##__VA_ARGS__);

#define LOG_FATAL(format, ...) fmt::print("[FATAL] " format "\n", ##__VA_ARGS__);