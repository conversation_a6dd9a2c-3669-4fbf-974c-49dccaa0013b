#pragma once

#ifdef USE_FRAMEWORK
    #include <framework/util/log.h>
#else
    #include "util.h"
#endif

#define GRAY_FRAME_SIZE 768*512

enum FrameType {
	None = 0,
	RequestToFrames = 1,
	ResponseFromFrames = 2,
    ImuFromFrames = 10294,   
    GraycameraFromFrames = 10056,
    RgbFromFrames = 10117,
    Heartbeat = 10000,
    MultiImuFromFrames = 11002,
    StartImu = 11003,
    StopImu = 11004
};

inline bool IsValidFrameType(FrameType type){
    return type == ImuFromFrames || type == GraycameraFromFrames || type == RgbFromFrames || type == Heartbeat || type == MultiImuFromFrames;
}

struct DataFrame{
    FrameType type{None};
    std::vector<uint8_t> data;
    uint32_t data_size{0};
};

enum NRImuID : int8_t {
    NR_IMU_ID_0 = 0x0001,
    NR_IMU_ID_1 = 0x0002,
    NR_IMU_ID_2 = 0x0004,
    NR_IMU_ID_3 = 0x0008,
};

enum ImuSourceType {
	IMU_SOURCE_TYPE_FROM_SDK_INTERRUPT,
	IMU_SOURCE_TYPE_FROM_SDK_HID,
	IMU_SOURCE_TYPE_FROM_BSP_HID
};

#pragma pack(1)


typedef struct {
    uint8_t is_request;
    uint64_t t1;
    uint64_t t2;
    uint64_t t3;
    uint64_t t4;
} NRHeartBeat;

struct FrameHeader {
    uint32_t len;
    uint32_t type;
};

struct TimeResonse{
    uint64_t t1;
    uint64_t t2;
    uint64_t t3;
};

struct TimeRequest{
    uint64_t t1;
};

typedef struct NRVector3i {
    int32_t x;
    int32_t y;
    int32_t z;

} NRVector3i;

typedef struct NRImuDataBsp {
  union {
    struct {
      int8_t imu_id;
      uint32_t frame_id;
      uint64_t hmd_time_nanos_device;
      uint64_t hmd_time_nanos_sensor;
      int32_t data_mask;
      NRVector3i gyroscope;
      NRVector3i accelerometer;
      NRVector3i magnetometer;
      int32_t temperature;
      int32_t gyroscope_numerator;
      int32_t gyroscope_denominator;
      int32_t accelerometer_numerator;
      int32_t accelerometer_denominator;
      int32_t magnetometer_offset;
      int32_t magnetometer_denominator;
    };
    uint8_t padding[128];
  };
 
} NRImuDataBsp;

struct NRImuDataBspWithTime {
    const NRImuDataBsp& data;
    uint64_t now;
};

typedef struct NRVector3f {
    float x, y, z;
} NRVector3f;


typedef struct NRImuData {
    union {
        struct {
            uint64_t hmd_time_nanos_system;
            uint64_t hmd_time_nanos_device;
            uint64_t hmd_time_nanos_sensor;
            int32_t data_mask;
            NRVector3f gyroscope;
            NRVector3f accelerometer;
            NRVector3f magnetometer;
            float temperature;
            int8_t imu_id;
            uint32_t frame_id;
            int32_t gyroscope_numerator;
            int32_t accelerometer_numerator;
            int32_t magnetometer_numerator;
            uint32_t out_numerator_mask;
        };
        uint8_t padding[128];
    };

} NRImuData;


typedef struct NRImuDataEx {
    union {
        struct {
            uint64_t hmd_time_nanos_system;
            uint64_t hmd_time_nanos_device;
            uint64_t hmd_time_nanos_sensor;
            int32_t data_mask;
            NRVector3f gyroscope;
            NRVector3f accelerometer;
            NRVector3f magnetometer;
            float temperature;
            int8_t gyro_id;
            int8_t accel_id;
            int8_t mag_id;
            int8_t temperature_id;
            uint32_t frame_id;
            int32_t gyroscope_numerator;
            int32_t accelerometer_numerator;
            int32_t magnetometer_numerator;
            uint32_t out_numerator_mask;
        };
        uint8_t padding[128];
    };

} NRImuDataEx;


struct NRImuDataExWithTime {
    NRImuDataEx data;
    uint64_t now;
    NRImuDataExWithTime(const NRImuDataEx& d, uint64_t t) : data(d), now(t) {}
    NRImuDataExWithTime(){}
};

struct NRImuDataWithTime {
    NRImuData data;
    uint64_t now;
    NRImuDataWithTime(const NRImuData& d, uint64_t t) : data(d), now(t) {}
    NRImuDataWithTime(){}
};

enum NRGrayscaleCameraPixelFormat : uint8_t{
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_UNKNOWN = 0,
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_YUV_420_888,
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_RGB_BAYER_8BPP,
};


enum NRGrayscaleCameraID : uint8_t{
    NR_GRAYSCALE_CAMERA_ID_0 = 0x0001,
    NR_GRAYSCALE_CAMERA_ID_1 = 0x0002,
    NR_GRAYSCALE_CAMERA_ID_2 = 0x0004,
    NR_GRAYSCALE_CAMERA_ID_3 = 0x0008,
};

typedef struct NRGrayscaleCameraUnitData {
    union {
        struct {
            uint32_t offset;
            NRGrayscaleCameraID camera_id;
            uint32_t width;
            uint32_t height;
            uint32_t stride;
            uint64_t exposure_start_time_device;
            uint32_t exposure_duration;
            uint32_t rolling_shutter_time;
            uint32_t gain;
            uint64_t exposure_start_time_system;
        };
        uint8_t padding[64];
    };

} NRGrayscaleCameraUnitData;

typedef struct NRGrayscaleCameraFrameData {
    union {
        struct {
            NRGrayscaleCameraUnitData cameras[4];
            uint64_t data_ptr;
            uint32_t data_bytes;
            uint8_t camera_count;
            uint32_t frame_id;
            NRGrayscaleCameraPixelFormat pixel_format;
        };
        uint8_t padding[320];
    };

} NRGrayscaleCameraFrameData;

struct NRGrayscaleCameraFrameDataWrapper {
    const NRGrayscaleCameraFrameData& frame_data;
    uint64_t now;  
};


enum NRRgbCameraPixelFormat : uint8_t{
    NR_RGB_CAMERA_PIXEL_FORMAT_UNKNOWN = 0,
    NR_RGB_CAMERA_PIXEL_FORMAT_YUV_420_888,
    NR_RGB_CAMERA_PIXEL_FORMAT_RGB_888,
};

typedef struct NRRgbCameraFrameData {
    union {
        struct {
            uint64_t data_ptr;
            uint32_t data_bytes;
            uint32_t width;
            uint32_t height;
            uint32_t stride;
            uint64_t exposure_start_time_device;
            uint32_t exposure_duration;
            uint32_t rolling_shutter_time;
            uint32_t gain;
            uint32_t frame_id;
            NRRgbCameraPixelFormat pixel_format;
            uint64_t exposure_start_time_system;
        };
        uint8_t padding[96];
    };
	uint8_t transmit_state;

} NRRgbCameraFrameData;

struct NRRgbCameraFrameDataWrapper {
    const NRRgbCameraFrameData& frame_data;
    uint64_t now;  
};

#pragma pack()



// 实现格式化输出

template <>
struct fmt::formatter<FrameType> : fmt::formatter<int> {
    template <typename FormatContext>
    auto format(FrameType type, FormatContext& ctx) const -> decltype(ctx.out()) {
        return fmt::formatter<int>::format(static_cast<int>(type), ctx);
    }
};
template <>
struct fmt::formatter<NRImuDataWithTime> : fmt::formatter<std::string> {
    auto format(const NRImuDataWithTime& v, fmt::format_context& ctx) const -> decltype(ctx.out()) {
        return fmt::format_to(
            ctx.out(),
            "NRImuData {{ monotonic_ns_now: {}, "
            "timestamp_system: {}, timestamp_device: {}, timestamp_sensor: {}, data_mask: {}, "
            "gyro: [{:.3f}, {:.3f}, {:.3f}], accel: [{:.3f}, {:.3f}, {:.3f}], mag: [{:.3f}, {:.3f}, {:.3f}], "
            "temperature: {:.2f}, imu_id: {}, frame_id: {}, "
            "gyro_numerator: {}, accel_numerator: {}, mag_numerator: {}, "
            "out_numerator_mask: {} }}",
            v.now,
            v.data.hmd_time_nanos_system,
            v.data.hmd_time_nanos_device,
            v.data.hmd_time_nanos_sensor,
            v.data.data_mask,
            v.data.gyroscope.x, v.data.gyroscope.y, v.data.gyroscope.z,
            v.data.accelerometer.x, v.data.accelerometer.y, v.data.accelerometer.z,
            v.data.magnetometer.x, v.data.magnetometer.y, v.data.magnetometer.z,
            v.data.temperature,
            static_cast<int>(v.data.imu_id),
            v.data.frame_id,
            v.data.gyroscope_numerator,
            v.data.accelerometer_numerator,
            v.data.magnetometer_numerator,
            v.data.out_numerator_mask
        );
    }
};


template <>
struct fmt::formatter<NRGrayscaleCameraUnitData> : fmt::formatter<std::string> {
    auto format(const NRGrayscaleCameraUnitData& v, fmt::format_context& ctx) const
        -> decltype(ctx.out())
    {
        return fmt::format_to(
            ctx.out(),
            "NRGrayscaleCameraUnitData {{ offset: {}, camera_id: {}, width: {}, height: {}, stride: {}, "
            "exposure_start_time_device: {}, exposure_duration: {}, rolling_shutter_time: {}, "
            "gain: {}, exposure_start_time_system: {} }}",
            v.offset,
            static_cast<int>(v.camera_id),
            v.width,
            v.height,
            v.stride,
            v.exposure_start_time_device,
            v.exposure_duration,
            v.rolling_shutter_time,
            v.gain,
            v.exposure_start_time_system
        );
    }
};

template <>
struct fmt::formatter<NRGrayscaleCameraFrameDataWrapper> : fmt::formatter<std::string> {
    auto format(const NRGrayscaleCameraFrameDataWrapper& wrapper, fmt::format_context& ctx) const
        -> decltype(ctx.out())
    {
        NRGrayscaleCameraUnitData first_camera;
        if (wrapper.frame_data.camera_count > 0) {
            std::memcpy(&first_camera, &wrapper.frame_data.cameras[0], sizeof(NRGrayscaleCameraUnitData));
        }

        return fmt::format_to(
            ctx.out(),
            "NRGrayscaleCameraFrameData {{ monotonic_ns_now: {}, data_ptr: {}, data_size: {}, "
            "camera_count: {}, frame_id: {}, pixel_format: {}, first_camera: {} }}",
            wrapper.now,
            wrapper.frame_data.data_ptr,
            wrapper.frame_data.data_bytes,
            static_cast<int>(wrapper.frame_data.camera_count),
            wrapper.frame_data.frame_id,
            static_cast<int>(wrapper.frame_data.pixel_format),
            fmt::format("{}", first_camera)  // 调用上面定义的 formatter
        );
    }
};


template <>
struct fmt::formatter<NRRgbCameraFrameDataWrapper> : fmt::formatter<std::string> {
    auto format(const NRRgbCameraFrameDataWrapper& wrapper, format_context& ctx) const -> decltype(ctx.out()) {

        return fmt::format_to(
            ctx.out(),
            "NRRgbCameraFrameData {{monotonic_ns_now: {}, data_ptr: {}, data_size: {}, width: {}, height: {}, stride: {}, exposure_start_time_device: {}, exposure_duration: {}, rolling_shutter_time: {}, gain: {}, frame_id: {}, pixel_format: {}, exposure_start_time_system: {}, transmit_state: {}}}",
            wrapper.now,
            wrapper.frame_data.data_ptr,
            wrapper.frame_data.data_bytes,
            wrapper.frame_data.width,
            wrapper.frame_data.height,
            wrapper.frame_data.stride,
            wrapper.frame_data.exposure_start_time_device,
            wrapper.frame_data.exposure_duration,
            wrapper.frame_data.rolling_shutter_time,
            wrapper.frame_data.gain,
            wrapper.frame_data.frame_id,
            static_cast<int>(wrapper.frame_data.pixel_format),
            wrapper.frame_data.exposure_start_time_system,
            wrapper.frame_data.transmit_state
        );
    }
};


template <>
struct fmt::formatter<NRImuDataBspWithTime> : fmt::formatter<char> {
    auto format(const NRImuDataBspWithTime& v, fmt::format_context& ctx) const -> decltype(ctx.out()) {
        const auto& data = v.data;
        return fmt::format_to(
            ctx.out(),
            "NRImuData {{ monotonic_ns_now: {}, imu_id: {}, frame_id: {}, "
            "hmd_hw_time_nanos: {}, hmd_sensor_time_nanos: {}, data_mask: {}, "
            "gyro: [{}, {}, {}], accel: [{}, {}, {}], mag: [{}, {}, {}], "
            "temperature: {}, "
            "gyro_numerator: {}, gyro_denominator: {}, "
            "accel_numerator: {}, accel_denominator: {}, "
            "mag_offset: {}, mag_denominator: {} }}",
            v.now,
            static_cast<int>(data.imu_id),
            data.frame_id,
            data.hmd_time_nanos_device,
            data.hmd_time_nanos_sensor,
            data.data_mask,
            data.gyroscope.x, data.gyroscope.y, data.gyroscope.z,
            data.accelerometer.x, data.accelerometer.y, data.accelerometer.z,
            data.magnetometer.x, data.magnetometer.y, data.magnetometer.z,
            data.temperature,
            data.gyroscope_numerator,
            data.gyroscope_denominator,
            data.accelerometer_numerator,
            data.accelerometer_denominator,
            data.magnetometer_offset,
            data.magnetometer_denominator
        );
    }
};

template <>
struct fmt::formatter<NRImuDataExWithTime> : fmt::formatter<std::string> {
    auto format(const NRImuDataExWithTime& v, fmt::format_context& ctx) const -> decltype(ctx.out()) {
        return fmt::format_to(
            ctx.out(),
            "NRImuDataEx {{ monotonic_ns_now: {}, "
            "timestamp_system: {}, timestamp_device: {}, timestamp_sensor: {}, data_mask: {}, "
            "gyro: [{:.3f}, {:.3f}, {:.3f}], accel: [{:.3f}, {:.3f}, {:.3f}], mag: [{:.3f}, {:.3f}, {:.3f}], "
            "temperature: {:.2f}, gyro_id: {}, accel_id: {}, mag_id: {} ,temperature_id: {}, frame_id: {}, "
            "gyro_numerator: {}, accel_numerator: {}, mag_numerator: {}, "
            "out_numerator_mask: {} }}",
            v.now,
            v.data.hmd_time_nanos_system,
            v.data.hmd_time_nanos_device,
            v.data.hmd_time_nanos_sensor,
            v.data.data_mask,
            v.data.gyroscope.x, v.data.gyroscope.y, v.data.gyroscope.z,
            v.data.accelerometer.x, v.data.accelerometer.y, v.data.accelerometer.z,
            v.data.magnetometer.x, v.data.magnetometer.y, v.data.magnetometer.z,
            v.data.temperature,
            static_cast<int>(v.data.gyro_id),
            static_cast<int>(v.data.accel_id),
            static_cast<int>(v.data.mag_id),
            static_cast<int>(v.data.temperature_id),
            v.data.frame_id,
            v.data.gyroscope_numerator,
            v.data.accelerometer_numerator,
            v.data.magnetometer_numerator,
            v.data.out_numerator_mask
        );
    }
};

template <>
struct fmt::formatter<NRHeartBeat> : fmt::formatter<std::string> {
    auto format(const NRHeartBeat& v, fmt::format_context& ctx) const -> decltype(ctx.out()) {
        return fmt::format_to(ctx.out(),"{},{},{},{}",v.t1,v.t2,v.t3,v.t4);
    }
};

