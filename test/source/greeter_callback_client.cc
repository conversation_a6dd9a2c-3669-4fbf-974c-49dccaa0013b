/*
 *
 * Copyright 2021 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#include <grpcpp/grpcpp.h>

#include <condition_variable>
#include <iostream>
#include <memory>
#include <mutex>
#include <string>
#include <time.h>

#ifdef BAZEL_BUILD
#include "examples/protos/helloworld.grpc.pb.h"
#else
#include "helloworld.grpc.pb.h"
#endif

using grpc::Channel;
using grpc::ClientContext;
using grpc::Status;
using helloworld::Greeter;
using helloworld::HelloReply;
using helloworld::HelloRequest;


#define NS_PER_SEC (**********)
static inline uint64_t GetMonotonicTimeNs()
{
  struct timespec ts;
	int ret = clock_gettime(CLOCK_MONOTONIC, &ts);
	if (ret != 0) {
		return 0;
	}
  uint64_t ns = 0;
	ns += (uint64_t)ts.tv_sec * NS_PER_SEC;
	ns += (uint64_t)ts.tv_nsec;
	return ns;
}

static inline void NanoSleep(int64_t nsec)
{
	struct timespec spec;
	spec.tv_sec = (nsec / NS_PER_SEC);
	spec.tv_nsec = (nsec % NS_PER_SEC);
	nanosleep(&spec, NULL);
}


class GreeterClient {
 public:
  GreeterClient(std::shared_ptr<Channel> channel)
      : stub_(Greeter::NewStub(channel)) {}

  // Assembles the client's payload, sends it and presents the response back
  // from the server.
  void SayHello() {
    //std::cout << "Send Hello" << std::endl;
    // Data we are sending to the server.
    HelloRequest request;
    request.set_t1(GetMonotonicTimeNs());

    // Container for the data we expect from the server.
    HelloReply reply;

    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // The actual RPC.
    std::mutex mu;
    std::condition_variable cv;
    bool done = false;
    Status status;
    stub_->async()->SayHello(&context, &request, &reply,
                             [&mu, &cv, &done, &status](Status s) {
                               status = std::move(s);
                               std::lock_guard<std::mutex> lock(mu);
                               done = true;
                               cv.notify_one();
                             });

    std::unique_lock<std::mutex> lock(mu);
    while (!done) {
      cv.wait(lock);
    }

    // Act upon its status.
    if (status.ok()) {
      std::cout << reply.t1() << "," << reply.t2() << "," << reply.t3() << "," << GetMonotonicTimeNs() << std::endl;
      return;
    } else {
      std::cout << status.error_code() << ": " << status.error_message()
                << std::endl;
      return;
    }
  }

 private:
  std::unique_ptr<Greeter::Stub> stub_;
};

static constexpr uint64_t DELTA_NS = NS_PER_SEC / 90;


int main(int argc, char** argv) {
  std::string target_str = "***********:50051";  // default

  if (argc >= 2) {
    std::string arg = argv[1];
    const std::string prefix = "--target=";
    if (arg.rfind(prefix, 0) == 0) {
      target_str = arg.substr(prefix.size());
    } else {
      std::cerr << "Unknown argument: " << arg << "\n";
      std::cerr << "Usage: " << argv[0] << " [--target=IP:PORT]\n";
      return 1;
    }
  }

  std::cout << "Using target: " << target_str << std::endl;
  // We indicate that the channel isn't authenticated (use of
  // InsecureChannelCredentials()).
  GreeterClient greeter(
    grpc::CreateChannel(target_str, grpc::InsecureChannelCredentials()));

  uint64_t app_ns = GetMonotonicTimeNs();

  while(true) {
    uint64_t real_ns = GetMonotonicTimeNs();
    app_ns += DELTA_NS;
    // if (i % 10 == 0) {
    //   std::cout << i << " times" << std::endl;
    // }
    greeter.SayHello();
    NanoSleep(app_ns - real_ns);
  }
  return 0;
}
