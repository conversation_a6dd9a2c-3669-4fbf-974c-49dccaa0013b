include(../cmake/common.cmake)

find_package(FlatBuffers REQUIRED)
find_package(gRPC CONFIG REQUIRED)
find_package(Protobuf CONFIG REQUIRED)


message("FLATBUFFERS_FLATC_EXECUTABLE=${FLATBUFFERS_FLATC_EXECUTABLE}")


# Define a custom command to generate FlatBuffers code

add_custom_command(
    OUTPUT
        ${CMAKE_CURRENT_BINARY_DIR}/greeter_generated.h
        ${CMAKE_CURRENT_BINARY_DIR}/greeter.grpc.fb.h
        ${CMAKE_CURRENT_BINARY_DIR}/greeter.grpc.fb.cc
    COMMAND ${FLATBUFFERS_FLATC_EXECUTABLE}
    ARGS -c --grpc --cpp --gen-object-api --filename-suffix "_generated" -o ${CMAKE_CURRENT_BINARY_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/greeter.fbs
    DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/greeter.fbs
    COMMENT "Generating FlatBuffers code from greeter.fbs"
)

# Add the client executable
add_executable(greeter_flatbuffers_client client.cc ${CMAKE_CURRENT_BINARY_DIR}/greeter.grpc.fb.cc)
target_include_directories(greeter_flatbuffers_client PRIVATE ${CMAKE_CURRENT_BINARY_DIR})
target_link_libraries(greeter_flatbuffers_client
    ${_GRPC_GRPCPP}
    flatbuffers::flatbuffers
)

# Add the server executable
add_executable(greeter_flatbuffers_server server.cc ${CMAKE_CURRENT_BINARY_DIR}/greeter.grpc.fb.cc)
target_include_directories(greeter_flatbuffers_server PRIVATE ${CMAKE_CURRENT_BINARY_DIR})
target_link_libraries(greeter_flatbuffers_server
    ${_GRPC_GRPCPP}
    flatbuffers::flatbuffers
)