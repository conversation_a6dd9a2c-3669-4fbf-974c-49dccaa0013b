#include <grpcpp/grpcpp.h>
#include "stream_demo.grpc.pb.h"
#include "grpc_common.h"

#include <framework/util/fileutil.h>

#include <string>
#include <iostream>
#include <thread>
#include <mutex>
#include <chrono>
#include <time.h>
#include <signal.h>
#include <iostream>
#include <functional>
#include <condition_variable>

using stream_demo::ImuStreamService;
using stream_demo::StartImuRequest;
using stream_demo::ImuData;
using stream_demo::StopImuRequest;
using stream_demo::StopImuResponse;

enum {
    SUBMIT_DATA_IMU = 1<<0,
    SUBMIT_DATA_VSYNC = 1<<1,
    SUBMIT_DATA_GRAYSCALE_CAMERA = 1<<2,
    SUBMIT_DATA_RGB_CAMERA = 1 << 3,
    SUBMIT_DATA_AMBIENT_LIGHT = 1 << 4,

    SUBMIT_DATA_IMU_FRAMEWORK = 1<<5,
    SUBMIT_DATA_VSYNC_FRAMEWORK = 1<<6,
    SUBMIT_DATA_GRAYSCALE_CAMERA_FRAMEWORK = 1<<7,
    SUBMIT_DATA_RGB_CAMERA_FRAMEWORK = 1 << 8,
    SUBMIT_DATA_AMBIENT_LIGHT_FRAMEWORK = 1 << 9,
};

static bool s_running = true;
static int32_t s_test_data = 0;
static int32_t s_param1 = 0;

template<class STREAM_SERVICE, class REQUEST, class RESPONSE>
class BaseStreamReactor : public grpc::ClientReadReactor<RESPONSE> {
public:
    BaseStreamReactor(typename STREAM_SERVICE::Stub* stub, const std::string& session_id) : stub_(stub), session_id_(session_id) {
        LOG_INFO("BaseStreamReactor::StreamReactor, session_id:{}", session_id);
    }

    ~BaseStreamReactor() override = default;

    virtual void Start() {
        grpc::ClientReadReactor<RESPONSE>::StartCall();
        grpc::ClientReadReactor<RESPONSE>::StartRead(&response_);
    }

    void OnReadDone(bool ok) override {
        LOG_DEBUG("BaseStreamReactor::OnReadDone, ok:{}", ok);
        if (ok) {
            HandleResponse(response_);
            grpc::ClientReadReactor<RESPONSE>::StartRead(&response_);
        }
    }

    void OnDone(const grpc::Status& status) override {
        if (status.ok()) {
            LOG_DEBUG("BaseStreamReactor::OnDone, ok:{}", status.ok());
        } else {
            LOG_ERROR("BaseStreamReactor::OnDone, ok:{}, error:{}", status.ok(), status.error_message());
        }
        delete this;
    }

private:
    virtual void HandleResponse(const RESPONSE& response) = 0;

protected:
    typename STREAM_SERVICE::Stub* stub_;
    grpc::ClientContext context_;
    REQUEST request_;
    RESPONSE response_;
    std::string session_id_;
};

class ImuStreamClient {
public:
    ImuStreamClient(std::shared_ptr<grpc::Channel> channel, const std::string& session_id)
            : stub_(ImuStreamService::NewStub(channel)), session_id_(session_id) {}

    void StartStream() {
        class StreamReactor : public BaseStreamReactor<ImuStreamService, StartImuRequest, ImuData> {
        public:
            StreamReactor(ImuStreamService::Stub* stub, const std::string& session_id) : BaseStreamReactor(stub,  session_id) {}
            ~StreamReactor() override = default;
            void Start() override {
                request_.set_session_id(session_id_);
                stub_->async()->StartAndSubmit(&context_, &request_, this);
                BaseStreamReactor::Start();
            }
        protected:
            void HandleResponse(const ImuData& imu) override {
                if (imu.frame_id() % 1000 == 1) {
                    LOG_INFO("ImuStreamClient::HandleResponse, frame_id:{}, imu_id:{}", imu.frame_id(), imu.imu_id());
                } else {
                    LOG_DEBUG("ImuStreamClient::HandleResponse, frame_id:{}, imu_id:{}", imu.frame_id(), imu.imu_id());
                }
            }

        };
        
        (new StreamReactor(stub_.get(), session_id_))->Start();
    }

    void StopStream() {
        StopImuRequest stop_request;
        stop_request.set_session_id(session_id_);
        StopImuResponse stop_response;
        grpc::ClientContext stop_context;
        grpc::Status status = stub_->Stop(&stop_context, stop_request, &stop_response);
        if (status.ok()) {
            LOG_INFO("ImuStreamClient::StopStream, ok:{}", status.ok());
        } else {
            LOG_INFO("ImuStreamClient::StopStream, ok:{}, error:{}", status.ok(), status.error_message());
        }
    }

private:
    std::unique_ptr<ImuStreamService::Stub> stub_;
    std::string session_id_;
};

template<int32_t ImuId>
class TestImu {
public:
    TestImu() {
       // std::string server_addr(SERVER_IP + ":" + GRPC_IMU_BEGIN_PORT + std::to_string(ImuId));
        std::string server_addr("localhost:" + TEST_PORT1);

        auto channel = grpc::CreateChannel(server_addr, grpc::InsecureChannelCredentials());
        client_ = std::make_shared<ImuStreamClient>(channel, "session-1");
        client_->StartStream();
    }

    virtual ~TestImu() {
        client_->StopStream();
    }

    void Update() {
        while (s_running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
    }

    static void Usage() {
        std::cout << "\t\tparam1 : fake data fps (>0)" << std::endl;
        std::cout << "\t\tparam2 : encrypt" << std::endl;
        std::cout << "\t\t\t0: no encrypt" << std::endl;
        std::cout << "\t\t\t1: data aes-128" << std::endl;
    }
private:
    std::shared_ptr<ImuStreamClient> client_;
};



void signal_handler(int signum) {
    std::cout << "Ctrl+C received. Exiting..." << std::endl;
    s_running = false;
}


int main(int argc, char* argv[]) {
	Logger::defaultLogger()->set_level(LogLevel::trace);

    signal(SIGINT, signal_handler);

    s_test_data = 1; //imu
    s_param1 = 1000; //1000fps

    std::vector<std::thread> workerThreads;
    if (s_test_data & SUBMIT_DATA_IMU) {
        workerThreads.emplace_back([]() {
            TestImu<1> testInstance;
            testInstance.Update();
        });
        // workerThreads.emplace_back([]() {
        //     TestImu<2> testInstance;
        //     testInstance.Update();
        // });
        // workerThreads.emplace_back([]() {
        //     TestImu<4> testInstance;
        //     testInstance.Update();
        // });
    }

    for (auto& t : workerThreads) {
        if (t.joinable()) {
            t.join();
        }
    }

    std::cerr << "main exit" << std::endl;
    return 0;
}

