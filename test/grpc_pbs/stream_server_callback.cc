#include <grpcpp/grpcpp.h>
#include "stream_demo.grpc.pb.h"
#include "grpc_common.h"


#include <framework/util/os_time.h>

#include <string>
#include <list>
#include <iostream>
#include <thread>
#include <chrono>
#include <time.h>
#include <signal.h>
#include <iostream>
#include <functional>


using google::protobuf::Descriptor;
using google::protobuf::FieldDescriptor;
using google::protobuf::internal::WireFormatLite;

using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::Status;
using grpc::ServerWriter;
using grpc::ServerAsyncWriter;
using grpc::ServerCompletionQueue;


using stream_demo::ImuStreamService;
using stream_demo::StartImuRequest;
using stream_demo::ImuData;
using stream_demo::StopImuRequest;
using stream_demo::StopImuResponse;

struct UserDataInfo {
    void * user_data;
    uint32_t frame_id;
};

typedef std::map<void*, UserDataInfo> UserDataMap;

enum {
    SUBMIT_DATA_IMU = 1<<0,
    SUBMIT_DATA_VSYNC = 1<<1,
    SUBMIT_DATA_GRAYSCALE_CAMERA = 1<<2,
    SUBMIT_DATA_RGB_CAMERA = 1 << 3,
    SUBMIT_DATA_AMBIENT_LIGHT = 1 << 4,


    SUBMIT_DATA_IMU_FRAMEWORK = 1<<5,
    SUBMIT_DATA_VSYNC_FRAMEWORK = 1<<6,
    SUBMIT_DATA_GRAYSCALE_CAMERA_FRAMEWORK = 1<<7,
    SUBMIT_DATA_RGB_CAMERA_FRAMEWORK = 1 << 8,
    SUBMIT_DATA_AMBIENT_LIGHT_FRAMEWORK = 1 << 9,
};

static bool s_running = true;
static int32_t s_test_data = 0;
static int32_t s_param1 = 0;
static int32_t s_param2 = 0;
static int32_t s_param3 = 0;


static std::vector<uint8_t> EncodeVarint(uint64_t value) {
    std::vector<uint8_t> buffer;
    buffer.resize(google::protobuf::io::CodedOutputStream::VarintSize64(value));
    uint8_t* ptr = buffer.data();
    ptr = google::protobuf::io::CodedOutputStream::WriteVarint64ToArray(value, ptr);
    buffer.resize(ptr - buffer.data());
    return buffer;
}

template<class RESPONSE, class RAW_DATA>
class BaseStreamReactor : public grpc::ServerWriteReactor<RESPONSE> {
public:
    BaseStreamReactor(const std::string& session_id)
            : session_id_(session_id), finished_(false), write_in_flight_(false) {
        LOG_INFO("BaseStreamReactor::BaseStreamReactor, session_id:{} ", session_id);

        // 启动写入线程
        writer_thread_ = std::thread(&BaseStreamReactor::SendLoop, this);
    }

    ~BaseStreamReactor() override {
        LOG_INFO("BaseStreamReactor::~BaseStreamReactor, session_id:{} ", session_id_);
        if (writer_thread_.joinable()) {
            writer_thread_.join();
        }
        LOG_INFO("BaseStreamReactor::~BaseStreamReactor, exit session_id:{} ", session_id_);
    }

    void EnqueueMessage(const RAW_DATA& data) {
        LOG_DEBUG("BaseStreamReactor::EnqueueMessage");
        std::lock_guard<std::mutex> lock(mu_);
        message_queue_.push(std::move(data));
        cv_.notify_all();
    }

    void OnWriteDone(bool ok) override {
        LOG_DEBUG("BaseStreamReactor::OnWriteDone, ok:{} ", ok);
        std::lock_guard<std::mutex> lock(mu_);
        write_in_flight_ = false;
        cv_.notify_all();
        if (!ok) {
            grpc::ServerWriteReactor<RESPONSE>::Finish(Status::OK);
        }
    }

    void OnDone() override {
        LOG_DEBUG("BaseStreamReactor::OnDone");
        {
            std::lock_guard<std::mutex> lock(mu_);
            finished_ = true;
            cv_.notify_all();
        }
        delete this;
    }

protected:
    virtual void ConstructEvent(RESPONSE& data, const RAW_DATA & frame_data) = 0;
    void SendLoop() {
#ifdef TEST_SYSTEM_XRLINUX
        pthread_setname_np(pthread_self(), "send_loop");
#endif
        while (s_running) {
            std::unique_lock<std::mutex> lock(mu_);
            cv_.wait(lock, [this] { return (!message_queue_.empty() && !write_in_flight_) || finished_; });
            if (finished_) {
                return;
            }
            RAW_DATA msg = std::move(message_queue_.front());
            LOG_DEBUG("BaseStreamReactor::SendLoop");
            message_queue_.pop();
            write_in_flight_ = true;
            lock.unlock();
            ConstructEvent(response_, msg);
            grpc::ServerWriteReactor<RESPONSE>::StartWrite(&response_);
        }

        LOG_INFO("BaseStreamReactor::SendLoop, Exit");
        grpc::ServerWriteReactor<RESPONSE>::Finish(Status::OK);
    }

protected:
    std::string session_id_;
    std::queue<RAW_DATA> message_queue_;
    RESPONSE response_;
    std::mutex mu_;
    std::condition_variable cv_;
    bool finished_;
    bool write_in_flight_;
    std::thread writer_thread_;
};

template<int32_t ImuId>
class ImuStreamServiceImpl final : public ImuStreamService::CallbackService {
    class StreamReactor : public BaseStreamReactor<ImuData, ::NRImuData> {
    public:
        StreamReactor(const std::string& session_id) : BaseStreamReactor(session_id) {}
        ~StreamReactor() override = default;

    protected:
        void ConstructEvent(ImuData& evt, const ::NRImuData & data) override {
            ImuData *imu_data = &evt;
            imu_data->set_hmd_time_nanos_system(data.hmd_time_nanos_system);
            imu_data->set_hmd_time_nanos_device(data.hmd_time_nanos_device);
            imu_data->set_hmd_time_nanos_sensor(data.hmd_time_nanos_sensor);
            imu_data->set_data_mask(data.data_mask);
            imu_data->set_gyroscope_x(data.gyroscope.x);
            imu_data->set_gyroscope_y(data.gyroscope.y);
            imu_data->set_gyroscope_z(data.gyroscope.z);
            imu_data->set_accelerometer_x(data.accelerometer.x);
            imu_data->set_accelerometer_y(data.accelerometer.y);
            imu_data->set_accelerometer_z(data.accelerometer.z);
            imu_data->set_magnetometer_x(data.magnetometer.x);
            imu_data->set_magnetometer_y(data.magnetometer.y);
            imu_data->set_magnetometer_z(data.magnetometer.z);
            imu_data->set_temperature(data.temperature);
            imu_data->set_imu_id(data.imu_id);
            imu_data->set_frame_id(data.frame_id);
            imu_data->set_gyroscope_numerator(data.gyroscope_numerator);
            imu_data->set_accelerometer_numerator(data.accelerometer_numerator);
            imu_data->set_magnetometer_numerator(data.magnetometer_numerator);
            imu_data->set_out_numerator_mask(data.out_numerator_mask);

            if (data.frame_id % 1000 == 1) {
                LOG_INFO("ImuStreamServiceImpl::ConstructEvent, submit success, frame_id={}", data.frame_id);
            }
        }
    };

public:
    grpc::ServerWriteReactor<ImuData>* StartAndSubmit(
            grpc::CallbackServerContext* context, const StartImuRequest* request) override {
        std::string session_id = request->session_id();
        LOG_INFO("ImuStreamServiceImpl, Received StartRequest, session_id:{} ", session_id);

        auto reactor = std::make_shared<StreamReactor>(session_id);
        {
            std::lock_guard<std::mutex> lock(mu_);
            reactors_[session_id] = reactor;
        }
        return reactor.get();
    }

    // 实现 Stop 方法
    grpc::ServerUnaryReactor* Stop(grpc::CallbackServerContext* context, const stream_demo::StopImuRequest* request,
                                   stream_demo::StopImuResponse* response) override {
        std::string session_id = request->session_id();
        LOG_INFO("ImuStreamServiceImpl, Received StopRequest, session_id:{} ", session_id);

        {
            std::lock_guard<std::mutex> lock(mu_);
            {
                auto it = reactors_.find(session_id);
                if (it != reactors_.end()) {
                    reactors_.erase(it);
                }
            }
        }

        response->set_session_id(session_id);
        response->set_result(0);  // 表示成功
        auto* reactor = context->DefaultReactor();
        reactor->Finish(Status::OK);
        return reactor;
    }

    void EnqueueMessage(const ::NRImuData& data) {
        std::lock_guard<std::mutex> lock(mu_);
        for (auto &reactor: reactors_) {
            reactor.second->EnqueueMessage(data);
        }
    }

private:
    std::mutex mu_;
    std::unordered_map<std::string, std::shared_ptr<StreamReactor>> reactors_;
};

template<int32_t ImuId>
class TestImu {
    void ImuFakeDataLoop() {
#ifdef TEST_SYSTEM_XRLINUX
        pthread_setname_np(pthread_self(), "fake_data_loop");
#endif
        while (s_running) {
            if (s_param1 > 0) {
                uint64_t begin_time = FMonotonicGetNs();
                SendEvent();
                uint64_t end_time = FMonotonicGetNs();
                int64_t delta = (int64_t)(1000*1000*1000)/s_param1-(int64_t)(end_time-begin_time);
                if (delta > 0) {
                    std::this_thread::sleep_for(std::chrono::nanoseconds(delta));
                } else {
                    LOG_ERROR("TestImu::Update, delta={}, begin={}, end={}", delta, begin_time, end_time);
                }
            } else {
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            }
        }
    }
public:
    TestImu() {
        fake_data_thread_ = std::thread(&TestImu::ImuFakeDataLoop, this);

        std::string server_addr("0.0.0.0:" + TEST_PORT1);

        //std::string server_addr("0.0.0.0:" + GRPC_IMU_BEGIN_PORT + std::to_string(ImuId));
        ServerBuilder builder;
        builder.AddListeningPort(server_addr, grpc::InsecureServerCredentials());
        builder.RegisterService(&service_);

        server_ = builder.BuildAndStart();
        LOG_INFO("Imu({}) Async server listening on {}", ImuId, server_addr);
    }

    virtual ~TestImu() {
        Shutdown();
    }

    void Update() {
        server_->Wait();
    }
    void Shutdown() {
        LOG_INFO("TestImu::Shutdown");
        if (server_) {
            server_->Shutdown(); // 触发 Wait() 返回
        }
        fake_data_thread_.join();
    }

    void SendEvent() {
        ::NRImuData data;
        memset(&data, 0, sizeof(data));
        static int32_t s_frame_id = 0;
        data.frame_id = (s_param3 > 0) ? 1 : ++s_frame_id;
        data.imu_id = 2;
        service_.EnqueueMessage(data);
    }

    static void Usage() {
        std::cout << "\t\tparam1 : fake data fps (>0)" << std::endl;
        std::cout << "\t\tparam2 : encrypt" << std::endl;
        std::cout << "\t\t\t0: no encrypt" << std::endl;
        std::cout << "\t\t\t1: data aes-128" << std::endl;
    }

private:
    ImuStreamServiceImpl<ImuId> service_;
    std::unique_ptr<Server> server_;
    std::thread fake_data_thread_;
};

void signal_handler(int signum) {
    std::cout << "Ctrl+C received. Exiting..." << std::endl;
    s_running = false;
}



int main(int argc, char* argv[]) {
	Logger::defaultLogger()->set_level(LogLevel::trace);

    signal(SIGINT, signal_handler);

    s_test_data = 1; //imu
    s_param1 = 1000; //1000fps

    std::vector<std::thread> workerThreads;

    if (s_test_data & SUBMIT_DATA_IMU)
    {
        workerThreads.emplace_back(std::thread([]() {
            static TestImu<1> testInstance;
            testInstance.Update();
        }));
        // workerThreads.emplace_back(std::thread([]() {
        //     static TestImu<2> testInstance;
        //     testInstance.Update();
        // }));
        // workerThreads.emplace_back(std::thread([]() {
        //     static TestImu<4> testInstance;
        //     testInstance.Update();
        // }));
    }

    for (auto& t : workerThreads) {
        if (t.joinable()) {
            t.join();
        }
    }

    std::cerr << "main exit" << std::endl;
    return 0;
}
