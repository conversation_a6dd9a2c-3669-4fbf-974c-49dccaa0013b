syntax = "proto3";
package stream_demo;

message StartImuRequest {
    string session_id = 1;
}

message ImuData {
    uint64 hmd_time_nanos_system = 1;
    uint64 hmd_time_nanos_device = 2;
    uint64 hmd_time_nanos_sensor = 3;
    int32 data_mask = 4;
    float gyroscope_x = 5;
    float gyroscope_y = 6;
    float gyroscope_z = 7;
    float accelerometer_x = 8;
    float accelerometer_y = 9;
    float accelerometer_z = 10;
    float magnetometer_x = 11;
    float magnetometer_y = 12;
    float magnetometer_z = 13;
    float temperature = 14;
    int32 imu_id = 15;
    uint32 frame_id = 16;
    int32 gyroscope_numerator = 17;
    int32 accelerometer_numerator = 18;
    int32 magnetometer_numerator = 19;
    uint32 out_numerator_mask = 20;
}

message StopImuRequest {
    string session_id = 1;
}

message StopImuResponse {
    string session_id = 1;
    int32 result = 2;
}

service ImuStreamService {
    // 启动后推流，多次发送 StreamEvent
    rpc StartAndSubmit(StartImuRequest) returns (stream ImuData);
    rpc Stop(StopImuRequest) returns (StopImuResponse);
}
