# 查找 Protobuf 与 gRPC（假设已通过包管理安装或编译安装）
find_package(Protobuf CONFIG REQUIRED)
find_package(gRPC CONFIG REQUIRED)

# Proto 文件路径
set(PROTO_FILE ${CMAKE_CURRENT_SOURCE_DIR}/stream_demo.proto)

# 生成的源文件路径
set(GEN_CPP_DIR ${CMAKE_CURRENT_BINARY_DIR})

set(PROTO_SRC     ${GEN_CPP_DIR}/stream_demo.pb.cc)
set(PROTO_HDR     ${GEN_CPP_DIR}/stream_demo.pb.h)
set(GRPC_SRC      ${GEN_CPP_DIR}/stream_demo.grpc.pb.cc)
set(GRPC_HDR      ${GEN_CPP_DIR}/stream_demo.grpc.pb.h)

# 自定义命令：调用 protoc 生成代码
add_custom_command(
        OUTPUT ${PROTO_SRC} ${PROTO_HDR} ${GRPC_SRC} ${GRPC_HDR}
        COMMAND ${Protobuf_PROTOC_EXECUTABLE}
        ARGS --cpp_out=${GEN_CPP_DIR}
        --grpc_out=${GEN_CPP_DIR}
        -I ${CMAKE_CURRENT_SOURCE_DIR}
        --plugin=protoc-gen-grpc=$<TARGET_FILE:gRPC::grpc_cpp_plugin>
        ${PROTO_FILE}
        DEPENDS ${PROTO_FILE}
        COMMENT "Generating Protobuf and gRPC sources"
)

add_library( stream_base INTERFACE )
target_include_directories( stream_base
        INTERFACE
        "$<BUILD_INTERFACE:${GEN_CPP_DIR}>"
)
target_link_libraries( stream_base
        INTERFACE
        framework::framework
        gRPC::grpc++
        protobuf::libprotobuf
        pthread
)


add_executable(stream_server_callback
        stream_server_callback.cc
        ${PROTO_SRC}
        ${GRPC_SRC}
)
target_link_libraries(stream_server_callback
        stream_base
)
add_executable(stream_server_callback_all_symbols
        stream_server_callback.cc
        ${PROTO_SRC}
        ${GRPC_SRC}
)
target_link_libraries(stream_server_callback_all_symbols
        stream_base
)
PROJECT_STRIP_LINK_OPTIONS(stream_server_callback stream_server_callback_all_symbols "symbol_type_null" FALSE)

add_executable(stream_client_callback
        stream_client_callback.cc
        ${PROTO_SRC}
        ${GRPC_SRC}
)
target_link_libraries(stream_client_callback
        stream_base
)
