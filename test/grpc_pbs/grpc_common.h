#pragma once
#include <openssl/evp.h>
#include <vector>
#include <stdexcept>

#include <framework/util/json.h>
#include <framework/util/log.h>
#include <fstream>

using namespace framework::util;
using namespace framework::util::log;


static const uint8_t s_key[64] = {
        0x00, 0x11, 0x22, 0x33,
        0x44, 0x55, 0x66, 0x77,
        0x88, 0x99, 0xaa, 0xbb,
        0xcc, 0xdd, 0xee, 0xff,

        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,

        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,

        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
};

static const uint8_t s_iv[16] = {
        0x01, 0x02, 0x03, 0x04,
        0x05, 0x06, 0x07, 0x08,
        0x09, 0x0a, 0x0b, 0x0c,
        0x0d, 0x0e, 0x0f, 0x10
};

static std::string SERVER_IP= "***********";
static std::string CLIENT_IP = "************";
static std::string GRPC_IMU_BEGIN_PORT = "5004";
static std::string GRPC_GRAYSCALE_CAMERA_PORT = "50051";
static std::string GRPC_RGB_CAMERA_PORT = "50052";

static const int32_t FRAMEWORK_GRAYSCALE_CAMERA_PORT = 50061;
static const int32_t FRAMEWORK_RGB_CAMERA_PORT = 50062;
static const int32_t FRAMEWORK_IMU_PORT = 50070;

static std::string  TEST_PORT1 = "50051";

#pragma pack(1)

typedef struct NRVector3f {
        float x, y, z;
} NRVector3f;

typedef struct NRImuData {
    union {
        struct {
            uint64_t hmd_time_nanos_system;
            uint64_t hmd_time_nanos_device;
            uint64_t hmd_time_nanos_sensor;
            int32_t data_mask;
            NRVector3f gyroscope;
            NRVector3f accelerometer;
            NRVector3f magnetometer;
            float temperature;
            int8_t imu_id;
            uint32_t frame_id;
            int32_t gyroscope_numerator;
            int32_t accelerometer_numerator;
            int32_t magnetometer_numerator;
            uint32_t out_numerator_mask;
        };
        uint8_t padding[128];
    };

} NRImuData;

#pragma pack()

