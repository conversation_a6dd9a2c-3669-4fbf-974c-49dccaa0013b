// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: route_guide.proto
// Protobuf C++ Version: 5.27.0

#ifndef GOOGLE_PROTOBUF_INCLUDED_route_5fguide_2eproto_2epb_2eh
#define GOOGLE_PROTOBUF_INCLUDED_route_5fguide_2eproto_2epb_2eh

#include <limits>
#include <string>
#include <type_traits>
#include <utility>

#include "google/protobuf/runtime_version.h"
#if PROTOBUF_VERSION != 5027000
#error "Protobuf C++ gencode is built with an incompatible version of"
#error "Protobuf C++ headers/runtime. See"
#error "https://protobuf.dev/support/cross-version-runtime-guarantee/#cpp"
#endif
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/arena.h"
#include "google/protobuf/arenastring.h"
#include "google/protobuf/generated_message_tctable_decl.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/metadata_lite.h"
#include "google/protobuf/generated_message_reflection.h"
#include "google/protobuf/message.h"
#include "google/protobuf/repeated_field.h"  // IWYU pragma: export
#include "google/protobuf/extension_set.h"  // IWYU pragma: export
#include "absl/strings/cord.h"
#include "google/protobuf/unknown_field_set.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"

#define PROTOBUF_INTERNAL_EXPORT_route_5fguide_2eproto

namespace google {
namespace protobuf {
namespace internal {
class AnyMetadata;
}  // namespace internal
}  // namespace protobuf
}  // namespace google

// Internal implementation detail -- do not use these members.
struct TableStruct_route_5fguide_2eproto {
  static const ::uint32_t offsets[];
};
extern const ::google::protobuf::internal::DescriptorTable
    descriptor_table_route_5fguide_2eproto;
namespace routeguide {
class Feature;
struct FeatureDefaultTypeInternal;
extern FeatureDefaultTypeInternal _Feature_default_instance_;
class Point;
struct PointDefaultTypeInternal;
extern PointDefaultTypeInternal _Point_default_instance_;
class Rectangle;
struct RectangleDefaultTypeInternal;
extern RectangleDefaultTypeInternal _Rectangle_default_instance_;
}  // namespace routeguide
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google

namespace routeguide {

// ===================================================================


// -------------------------------------------------------------------

class Point final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:routeguide.Point) */ {
 public:
  inline Point() : Point(nullptr) {}
  ~Point() override;
  template <typename = void>
  explicit PROTOBUF_CONSTEXPR Point(
      ::google::protobuf::internal::ConstantInitialized);

  inline Point(const Point& from) : Point(nullptr, from) {}
  inline Point(Point&& from) noexcept
      : Point(nullptr, std::move(from)) {}
  inline Point& operator=(const Point& from) {
    CopyFrom(from);
    return *this;
  }
  inline Point& operator=(Point&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
#ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
#endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Point& default_instance() {
    return *internal_default_instance();
  }
  static inline const Point* internal_default_instance() {
    return reinterpret_cast<const Point*>(
        &_Point_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 0;
  friend void swap(Point& a, Point& b) { a.Swap(&b); }
  inline void Swap(Point* other) {
    if (other == this) return;
#ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr && GetArena() == other->GetArena()) {
#else   // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
#endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Point* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Point* New(::google::protobuf::Arena* arena = nullptr) const final {
    return ::google::protobuf::Message::DefaultConstruct<Point>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const Point& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const Point& from) { Point::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() final;
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(Point* other);
 private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() { return "routeguide.Point"; }

 protected:
  explicit Point(::google::protobuf::Arena* arena);
  Point(::google::protobuf::Arena* arena, const Point& from);
  Point(::google::protobuf::Arena* arena, Point&& from) noexcept
      : Point(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::Message::ClassData* GetClassData() const final;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kLatitudeFieldNumber = 1,
    kLongitudeFieldNumber = 2,
  };
  // int32 latitude = 1;
  void clear_latitude() ;
  ::int32_t latitude() const;
  void set_latitude(::int32_t value);

  private:
  ::int32_t _internal_latitude() const;
  void _internal_set_latitude(::int32_t value);

  public:
  // int32 longitude = 2;
  void clear_longitude() ;
  ::int32_t longitude() const;
  void set_longitude(::int32_t value);

  private:
  ::int32_t _internal_longitude() const;
  void _internal_set_longitude(::int32_t value);

  public:
  // @@protoc_insertion_point(class_scope:routeguide.Point)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 0,
      0, 2>
      _table_;

  static constexpr const void* _raw_default_instance_ =
      &_Point_default_instance_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const Point& from_msg);
    ::int32_t latitude_;
    ::int32_t longitude_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_route_5fguide_2eproto;
};
// -------------------------------------------------------------------

class Rectangle final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:routeguide.Rectangle) */ {
 public:
  inline Rectangle() : Rectangle(nullptr) {}
  ~Rectangle() override;
  template <typename = void>
  explicit PROTOBUF_CONSTEXPR Rectangle(
      ::google::protobuf::internal::ConstantInitialized);

  inline Rectangle(const Rectangle& from) : Rectangle(nullptr, from) {}
  inline Rectangle(Rectangle&& from) noexcept
      : Rectangle(nullptr, std::move(from)) {}
  inline Rectangle& operator=(const Rectangle& from) {
    CopyFrom(from);
    return *this;
  }
  inline Rectangle& operator=(Rectangle&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
#ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
#endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Rectangle& default_instance() {
    return *internal_default_instance();
  }
  static inline const Rectangle* internal_default_instance() {
    return reinterpret_cast<const Rectangle*>(
        &_Rectangle_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 1;
  friend void swap(Rectangle& a, Rectangle& b) { a.Swap(&b); }
  inline void Swap(Rectangle* other) {
    if (other == this) return;
#ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr && GetArena() == other->GetArena()) {
#else   // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
#endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Rectangle* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Rectangle* New(::google::protobuf::Arena* arena = nullptr) const final {
    return ::google::protobuf::Message::DefaultConstruct<Rectangle>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const Rectangle& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const Rectangle& from) { Rectangle::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() final;
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(Rectangle* other);
 private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() { return "routeguide.Rectangle"; }

 protected:
  explicit Rectangle(::google::protobuf::Arena* arena);
  Rectangle(::google::protobuf::Arena* arena, const Rectangle& from);
  Rectangle(::google::protobuf::Arena* arena, Rectangle&& from) noexcept
      : Rectangle(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::Message::ClassData* GetClassData() const final;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kLoFieldNumber = 1,
    kHiFieldNumber = 2,
  };
  // .routeguide.Point lo = 1;
  bool has_lo() const;
  void clear_lo() ;
  const ::routeguide::Point& lo() const;
  PROTOBUF_NODISCARD ::routeguide::Point* release_lo();
  ::routeguide::Point* mutable_lo();
  void set_allocated_lo(::routeguide::Point* value);
  void unsafe_arena_set_allocated_lo(::routeguide::Point* value);
  ::routeguide::Point* unsafe_arena_release_lo();

  private:
  const ::routeguide::Point& _internal_lo() const;
  ::routeguide::Point* _internal_mutable_lo();

  public:
  // .routeguide.Point hi = 2;
  bool has_hi() const;
  void clear_hi() ;
  const ::routeguide::Point& hi() const;
  PROTOBUF_NODISCARD ::routeguide::Point* release_hi();
  ::routeguide::Point* mutable_hi();
  void set_allocated_hi(::routeguide::Point* value);
  void unsafe_arena_set_allocated_hi(::routeguide::Point* value);
  ::routeguide::Point* unsafe_arena_release_hi();

  private:
  const ::routeguide::Point& _internal_hi() const;
  ::routeguide::Point* _internal_mutable_hi();

  public:
  // @@protoc_insertion_point(class_scope:routeguide.Rectangle)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      1, 2, 2,
      0, 2>
      _table_;

  static constexpr const void* _raw_default_instance_ =
      &_Rectangle_default_instance_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const Rectangle& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    ::routeguide::Point* lo_;
    ::routeguide::Point* hi_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_route_5fguide_2eproto;
};
// -------------------------------------------------------------------

class Feature final : public ::google::protobuf::Message
/* @@protoc_insertion_point(class_definition:routeguide.Feature) */ {
 public:
  inline Feature() : Feature(nullptr) {}
  ~Feature() override;
  template <typename = void>
  explicit PROTOBUF_CONSTEXPR Feature(
      ::google::protobuf::internal::ConstantInitialized);

  inline Feature(const Feature& from) : Feature(nullptr, from) {}
  inline Feature(Feature&& from) noexcept
      : Feature(nullptr, std::move(from)) {}
  inline Feature& operator=(const Feature& from) {
    CopyFrom(from);
    return *this;
  }
  inline Feature& operator=(Feature&& from) noexcept {
    if (this == &from) return *this;
    if (GetArena() == from.GetArena()
#ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetArena() != nullptr
#endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<::google::protobuf::UnknownFieldSet>(::google::protobuf::UnknownFieldSet::default_instance);
  }
  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<::google::protobuf::UnknownFieldSet>();
  }

  static const ::google::protobuf::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::google::protobuf::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::google::protobuf::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Feature& default_instance() {
    return *internal_default_instance();
  }
  static inline const Feature* internal_default_instance() {
    return reinterpret_cast<const Feature*>(
        &_Feature_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 2;
  friend void swap(Feature& a, Feature& b) { a.Swap(&b); }
  inline void Swap(Feature* other) {
    if (other == this) return;
#ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() != nullptr && GetArena() == other->GetArena()) {
#else   // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetArena() == other->GetArena()) {
#endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Feature* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Feature* New(::google::protobuf::Arena* arena = nullptr) const final {
    return ::google::protobuf::Message::DefaultConstruct<Feature>(arena);
  }
  using ::google::protobuf::Message::CopyFrom;
  void CopyFrom(const Feature& from);
  using ::google::protobuf::Message::MergeFrom;
  void MergeFrom(const Feature& from) { Feature::MergeImpl(*this, from); }

  private:
  static void MergeImpl(
      ::google::protobuf::MessageLite& to_msg,
      const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() final;
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  void SharedDtor();
  void InternalSwap(Feature* other);
 private:
  friend class ::google::protobuf::internal::AnyMetadata;
  static ::absl::string_view FullMessageName() { return "routeguide.Feature"; }

 protected:
  explicit Feature(::google::protobuf::Arena* arena);
  Feature(::google::protobuf::Arena* arena, const Feature& from);
  Feature(::google::protobuf::Arena* arena, Feature&& from) noexcept
      : Feature(arena) {
    *this = ::std::move(from);
  }
  private:
  static void ArenaDtor(void* object);
  const ::google::protobuf::Message::ClassData* GetClassData() const final;

 public:
  ::google::protobuf::Metadata GetMetadata() const;
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kNameFieldNumber = 1,
    kBigDataFieldNumber = 3,
    kLocationFieldNumber = 2,
  };
  // string name = 1;
  void clear_name() ;
  const std::string& name() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_name(Arg_&& arg, Args_... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* value);

  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(
      const std::string& value);
  std::string* _internal_mutable_name();

  public:
  // bytes big_data = 3 [ctype = CORD];
  void clear_big_data() ;
  const ::absl::Cord& big_data() const;
  void set_big_data(const ::absl::Cord& value);
  void set_big_data(::absl::string_view value);
  private:
  const ::absl::Cord& _internal_big_data() const;
  void _internal_set_big_data(const ::absl::Cord& value);
  ::absl::Cord* _internal_mutable_big_data();
  public:
  // .routeguide.Point location = 2;
  bool has_location() const;
  void clear_location() ;
  const ::routeguide::Point& location() const;
  PROTOBUF_NODISCARD ::routeguide::Point* release_location();
  ::routeguide::Point* mutable_location();
  void set_allocated_location(::routeguide::Point* value);
  void unsafe_arena_set_allocated_location(::routeguide::Point* value);
  ::routeguide::Point* unsafe_arena_release_location();

  private:
  const ::routeguide::Point& _internal_location() const;
  ::routeguide::Point* _internal_mutable_location();

  public:
  // @@protoc_insertion_point(class_scope:routeguide.Feature)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 1,
      31, 2>
      _table_;

  static constexpr const void* _raw_default_instance_ =
      &_Feature_default_instance_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const Feature& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    mutable ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::ArenaStringPtr name_;
    ::absl::Cord big_data_;
    ::routeguide::Point* location_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_route_5fguide_2eproto;
};

// ===================================================================




// ===================================================================


#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// Point

// int32 latitude = 1;
inline void Point::clear_latitude() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.latitude_ = 0;
}
inline ::int32_t Point::latitude() const {
  // @@protoc_insertion_point(field_get:routeguide.Point.latitude)
  return _internal_latitude();
}
inline void Point::set_latitude(::int32_t value) {
  _internal_set_latitude(value);
  // @@protoc_insertion_point(field_set:routeguide.Point.latitude)
}
inline ::int32_t Point::_internal_latitude() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.latitude_;
}
inline void Point::_internal_set_latitude(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.latitude_ = value;
}

// int32 longitude = 2;
inline void Point::clear_longitude() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.longitude_ = 0;
}
inline ::int32_t Point::longitude() const {
  // @@protoc_insertion_point(field_get:routeguide.Point.longitude)
  return _internal_longitude();
}
inline void Point::set_longitude(::int32_t value) {
  _internal_set_longitude(value);
  // @@protoc_insertion_point(field_set:routeguide.Point.longitude)
}
inline ::int32_t Point::_internal_longitude() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.longitude_;
}
inline void Point::_internal_set_longitude(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.longitude_ = value;
}

// -------------------------------------------------------------------

// Rectangle

// .routeguide.Point lo = 1;
inline bool Rectangle::has_lo() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.lo_ != nullptr);
  return value;
}
inline void Rectangle::clear_lo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.lo_ != nullptr) _impl_.lo_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::routeguide::Point& Rectangle::_internal_lo() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::routeguide::Point* p = _impl_.lo_;
  return p != nullptr ? *p : reinterpret_cast<const ::routeguide::Point&>(::routeguide::_Point_default_instance_);
}
inline const ::routeguide::Point& Rectangle::lo() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:routeguide.Rectangle.lo)
  return _internal_lo();
}
inline void Rectangle::unsafe_arena_set_allocated_lo(::routeguide::Point* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.lo_);
  }
  _impl_.lo_ = reinterpret_cast<::routeguide::Point*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:routeguide.Rectangle.lo)
}
inline ::routeguide::Point* Rectangle::release_lo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::routeguide::Point* released = _impl_.lo_;
  _impl_.lo_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
  released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  if (GetArena() == nullptr) {
    delete old;
  }
#else   // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArena() != nullptr) {
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return released;
}
inline ::routeguide::Point* Rectangle::unsafe_arena_release_lo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:routeguide.Rectangle.lo)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::routeguide::Point* temp = _impl_.lo_;
  _impl_.lo_ = nullptr;
  return temp;
}
inline ::routeguide::Point* Rectangle::_internal_mutable_lo() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.lo_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::routeguide::Point>(GetArena());
    _impl_.lo_ = reinterpret_cast<::routeguide::Point*>(p);
  }
  return _impl_.lo_;
}
inline ::routeguide::Point* Rectangle::mutable_lo() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::routeguide::Point* _msg = _internal_mutable_lo();
  // @@protoc_insertion_point(field_mutable:routeguide.Rectangle.lo)
  return _msg;
}
inline void Rectangle::set_allocated_lo(::routeguide::Point* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.lo_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.lo_ = reinterpret_cast<::routeguide::Point*>(value);
  // @@protoc_insertion_point(field_set_allocated:routeguide.Rectangle.lo)
}

// .routeguide.Point hi = 2;
inline bool Rectangle::has_hi() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.hi_ != nullptr);
  return value;
}
inline void Rectangle::clear_hi() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.hi_ != nullptr) _impl_.hi_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::routeguide::Point& Rectangle::_internal_hi() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::routeguide::Point* p = _impl_.hi_;
  return p != nullptr ? *p : reinterpret_cast<const ::routeguide::Point&>(::routeguide::_Point_default_instance_);
}
inline const ::routeguide::Point& Rectangle::hi() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:routeguide.Rectangle.hi)
  return _internal_hi();
}
inline void Rectangle::unsafe_arena_set_allocated_hi(::routeguide::Point* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.hi_);
  }
  _impl_.hi_ = reinterpret_cast<::routeguide::Point*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:routeguide.Rectangle.hi)
}
inline ::routeguide::Point* Rectangle::release_hi() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::routeguide::Point* released = _impl_.hi_;
  _impl_.hi_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
  released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  if (GetArena() == nullptr) {
    delete old;
  }
#else   // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArena() != nullptr) {
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return released;
}
inline ::routeguide::Point* Rectangle::unsafe_arena_release_hi() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:routeguide.Rectangle.hi)

  _impl_._has_bits_[0] &= ~0x00000002u;
  ::routeguide::Point* temp = _impl_.hi_;
  _impl_.hi_ = nullptr;
  return temp;
}
inline ::routeguide::Point* Rectangle::_internal_mutable_hi() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.hi_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::routeguide::Point>(GetArena());
    _impl_.hi_ = reinterpret_cast<::routeguide::Point*>(p);
  }
  return _impl_.hi_;
}
inline ::routeguide::Point* Rectangle::mutable_hi() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000002u;
  ::routeguide::Point* _msg = _internal_mutable_hi();
  // @@protoc_insertion_point(field_mutable:routeguide.Rectangle.hi)
  return _msg;
}
inline void Rectangle::set_allocated_hi(::routeguide::Point* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.hi_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }

  _impl_.hi_ = reinterpret_cast<::routeguide::Point*>(value);
  // @@protoc_insertion_point(field_set_allocated:routeguide.Rectangle.hi)
}

// -------------------------------------------------------------------

// Feature

// string name = 1;
inline void Feature::clear_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Feature::name() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:routeguide.Feature.name)
  return _internal_name();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void Feature::set_name(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:routeguide.Feature.name)
}
inline std::string* Feature::mutable_name() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:routeguide.Feature.name)
  return _s;
}
inline const std::string& Feature::_internal_name() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.name_.Get();
}
inline void Feature::_internal_set_name(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.Set(value, GetArena());
}
inline std::string* Feature::_internal_mutable_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _impl_.name_.Mutable( GetArena());
}
inline std::string* Feature::release_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:routeguide.Feature.name)
  return _impl_.name_.Release();
}
inline void Feature::set_allocated_name(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.SetAllocated(value, GetArena());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
        if (_impl_.name_.IsDefault()) {
          _impl_.name_.Set("", GetArena());
        }
  #endif  // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:routeguide.Feature.name)
}

// .routeguide.Point location = 2;
inline bool Feature::has_location() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.location_ != nullptr);
  return value;
}
inline void Feature::clear_location() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.location_ != nullptr) _impl_.location_->Clear();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const ::routeguide::Point& Feature::_internal_location() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  const ::routeguide::Point* p = _impl_.location_;
  return p != nullptr ? *p : reinterpret_cast<const ::routeguide::Point&>(::routeguide::_Point_default_instance_);
}
inline const ::routeguide::Point& Feature::location() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:routeguide.Feature.location)
  return _internal_location();
}
inline void Feature::unsafe_arena_set_allocated_location(::routeguide::Point* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::google::protobuf::MessageLite*>(_impl_.location_);
  }
  _impl_.location_ = reinterpret_cast<::routeguide::Point*>(value);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:routeguide.Feature.location)
}
inline ::routeguide::Point* Feature::release_location() {
  ::google::protobuf::internal::TSanWrite(&_impl_);

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::routeguide::Point* released = _impl_.location_;
  _impl_.location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old = reinterpret_cast<::google::protobuf::MessageLite*>(released);
  released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  if (GetArena() == nullptr) {
    delete old;
  }
#else   // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArena() != nullptr) {
    released = ::google::protobuf::internal::DuplicateIfNonNull(released);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return released;
}
inline ::routeguide::Point* Feature::unsafe_arena_release_location() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:routeguide.Feature.location)

  _impl_._has_bits_[0] &= ~0x00000001u;
  ::routeguide::Point* temp = _impl_.location_;
  _impl_.location_ = nullptr;
  return temp;
}
inline ::routeguide::Point* Feature::_internal_mutable_location() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (_impl_.location_ == nullptr) {
    auto* p = ::google::protobuf::Message::DefaultConstruct<::routeguide::Point>(GetArena());
    _impl_.location_ = reinterpret_cast<::routeguide::Point*>(p);
  }
  return _impl_.location_;
}
inline ::routeguide::Point* Feature::mutable_location() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  _impl_._has_bits_[0] |= 0x00000001u;
  ::routeguide::Point* _msg = _internal_mutable_location();
  // @@protoc_insertion_point(field_mutable:routeguide.Feature.location)
  return _msg;
}
inline void Feature::set_allocated_location(::routeguide::Point* value) {
  ::google::protobuf::Arena* message_arena = GetArena();
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (message_arena == nullptr) {
    delete (_impl_.location_);
  }

  if (value != nullptr) {
    ::google::protobuf::Arena* submessage_arena = (value)->GetArena();
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(message_arena, value, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }

  _impl_.location_ = reinterpret_cast<::routeguide::Point*>(value);
  // @@protoc_insertion_point(field_set_allocated:routeguide.Feature.location)
}

// bytes big_data = 3 [ctype = CORD];
inline void Feature::clear_big_data() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.big_data_.Clear();
}
inline const ::absl::Cord& Feature::_internal_big_data() const {
  return _impl_.big_data_;
}
inline const ::absl::Cord& Feature::big_data() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:routeguide.Feature.big_data)
  return _internal_big_data();
}
inline void Feature::_internal_set_big_data(
    const ::absl::Cord& value) {
  _impl_.big_data_ = value;
}
inline void Feature::set_big_data(const ::absl::Cord& value) {
  _internal_set_big_data(value);
  // @@protoc_insertion_point(field_set:routeguide.Feature.big_data)
}
inline void Feature::set_big_data(::absl::string_view value) {
  _impl_.big_data_ = value;
  // @@protoc_insertion_point(field_set_string_piece:routeguide.Feature.big_data)
}
inline ::absl::Cord* Feature::_internal_mutable_big_data() {
  return &_impl_.big_data_;
}

#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)
}  // namespace routeguide


// @@protoc_insertion_point(global_scope)

#include "google/protobuf/port_undef.inc"

#endif  // GOOGLE_PROTOBUF_INCLUDED_route_5fguide_2eproto_2epb_2eh
