#!/usr/bin/env python
# encoding: utf-8

import re
import csv

# 输入输出路径（可根据实际修改）
input_log_path = "heartbeat.log"
output_csv_path = "output.csv"

# 匹配格式：4 个逗号分隔的 10 位以上整数
pattern = re.compile(r"(\d{10,}),(\d{10,}),(\d{10,}),(\d{10,})")

rows = []
deltas = []

# 读取日志并提取时间戳
with open(input_log_path, "r") as infile:
    for line in infile:
        match = pattern.search(line)
        if match:
            t1, t2, t3, t4 = match.groups()
            t1 = int(t1)
            t2 = int(t2)
            t3 = int(t3)
            t4 = int(t4)
            delta = t4 - t1
            rows.append((t1, t2, t3, t4, delta))
            deltas.append(delta)

# 写入 CSV 文件
with open(output_csv_path, "w", newline="") as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(["t1", "t2", "t3", "t4", "t4 - t1"])
    writer.writerows(rows)

# 统计信息
if deltas:
    avg = sum(deltas) / len(deltas)
    min_val = min(deltas)
    max_val = max(deltas)
    count = len(deltas)

    print(f"\n统计结果（单位：ns）：")
    print(f"  总共数据条数: {count}")
    print(f"  平均值: {avg:.2f}")
    print(f"  最小值: {min_val}")
    print(f"  最大值: {max_val}")
else:
    print("未找到匹配的数据")
